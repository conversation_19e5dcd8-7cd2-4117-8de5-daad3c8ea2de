# NL2SQL 数据库迁移指南

本指南将帮助您将 NL2SQL 系统从 SQLite 迁移到 MySQL 数据库。

## 目录

1. [准备工作](#准备工作)
2. [MySQL 安装和配置](#mysql-安装和配置)
3. [配置应用程序](#配置应用程序)
4. [数据迁移](#数据迁移)
5. [验证迁移](#验证迁移)
6. [故障排除](#故障排除)

## 准备工作

### 1. 备份现有数据

在开始迁移之前，请备份您的 SQLite 数据库：

```bash
# 备份 SQLite 数据库文件
cp backend/test.db backend/test.db.backup
```

### 2. 检查系统要求

确保您的系统满足以下要求：
- Python 3.8+
- MySQL 5.7+ 或 MySQL 8.0+
- 足够的磁盘空间

## MySQL 安装和配置

### 1. 安装 MySQL

#### Windows
1. 下载 MySQL Installer：https://dev.mysql.com/downloads/installer/
2. 运行安装程序并选择 "Developer Default" 安装类型
3. 按照向导完成安装

#### macOS
```bash
# 使用 Homebrew 安装
brew install mysql

# 启动 MySQL 服务
brew services start mysql
```

#### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装 MySQL
sudo apt install mysql-server

# 启动 MySQL 服务
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### CentOS/RHEL
```bash
# 安装 MySQL
sudo yum install mysql-server

# 启动 MySQL 服务
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

### 2. 配置 MySQL

#### 安全配置
```bash
# 运行安全配置脚本
sudo mysql_secure_installation
```

按照提示完成以下配置：
- 设置 root 密码
- 删除匿名用户
- 禁止 root 远程登录
- 删除测试数据库

#### 创建数据库和用户
```sql
-- 登录 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE nl2sql_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建专用用户（推荐）
CREATE USER 'nl2sql_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- 授予权限
GRANT ALL PRIVILEGES ON nl2sql_system.* TO 'nl2sql_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

## 配置应用程序

### 1. 安装 Python 依赖

```bash
# 进入后端目录
cd backend

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

复制配置模板并修改：

```bash
# 复制配置模板
cp .env.example .env
```

编辑 `.env` 文件，配置 MySQL 连接信息：

```env
# 数据库配置
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=nl2sql_user
DB_PASSWORD=your_secure_password
DB_NAME=nl2sql_system
DB_CHARSET=utf8mb4

# 数据库连接池配置
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800

# 其他配置保持不变...
```

### 3. 测试数据库连接

```bash
# 运行应用程序测试连接
python main.py
```

如果看到 "数据库连接测试成功" 和 "数据库初始化完成" 的日志消息，说明配置正确。

## 数据迁移

### 方法一：使用 SQL 脚本初始化（推荐新安装）

如果这是一个新的安装，可以直接使用提供的 SQL 脚本：

```bash
# 执行 DDL 脚本创建表结构
mysql -u nl2sql_user -p nl2sql_system < database/mysql_ddl.sql

# 执行初始化数据脚本（可选）
mysql -u nl2sql_user -p nl2sql_system < database/init_data.sql
```

### 方法二：从 SQLite 迁移数据

如果您有现有的 SQLite 数据需要迁移：

#### 1. 导出 SQLite 数据

```bash
# 安装 sqlite3（如果尚未安装）
# Ubuntu/Debian: sudo apt install sqlite3
# macOS: brew install sqlite3
# Windows: 下载 sqlite3.exe

# 导出数据为 SQL 格式
sqlite3 test.db .dump > sqlite_data.sql
```

#### 2. 转换 SQL 格式

SQLite 和 MySQL 的 SQL 语法略有不同，需要进行转换：

```bash
# 创建转换脚本
cat > convert_sqlite_to_mysql.py << 'EOF'
import re

def convert_sqlite_to_mysql(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除 SQLite 特定的语句
    content = re.sub(r'PRAGMA.*?;', '', content)
    content = re.sub(r'BEGIN TRANSACTION;', 'START TRANSACTION;', content)
    content = re.sub(r'COMMIT;', 'COMMIT;', content)
    
    # 转换数据类型
    content = re.sub(r'INTEGER PRIMARY KEY AUTOINCREMENT', 'INT AUTO_INCREMENT PRIMARY KEY', content)
    content = re.sub(r'INTEGER', 'INT', content)
    content = re.sub(r'REAL', 'DECIMAL(10,2)', content)
    content = re.sub(r'TEXT', 'TEXT', content)
    content = re.sub(r'BLOB', 'LONGBLOB', content)
    
    # 添加 MySQL 特定设置
    mysql_header = """
SET FOREIGN_KEY_CHECKS = 0;
SET AUTOCOMMIT = 0;
START TRANSACTION;

"""
    
    mysql_footer = """
COMMIT;
SET FOREIGN_KEY_CHECKS = 1;
"""
    
    content = mysql_header + content + mysql_footer
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)

if __name__ == "__main__":
    convert_sqlite_to_mysql('sqlite_data.sql', 'mysql_data.sql')
    print("转换完成：sqlite_data.sql -> mysql_data.sql")
EOF

# 运行转换脚本
python convert_sqlite_to_mysql.py
```

#### 3. 导入到 MySQL

```bash
# 导入转换后的数据
mysql -u nl2sql_user -p nl2sql_system < mysql_data.sql
```

## 验证迁移

### 1. 检查表结构

```sql
-- 登录 MySQL
mysql -u nl2sql_user -p nl2sql_system

-- 查看所有表
SHOW TABLES;

-- 检查表结构（示例）
DESCRIBE users;
DESCRIBE data_sources;
DESCRIBE query_history;
```

### 2. 检查数据

```sql
-- 检查用户数据
SELECT COUNT(*) FROM users;

-- 检查数据源
SELECT COUNT(*) FROM data_sources;

-- 检查查询历史
SELECT COUNT(*) FROM query_history;
```

### 3. 测试应用程序

```bash
# 启动应用程序
cd backend
python main.py
```

访问前端应用程序，测试以下功能：
- 用户登录
- 数据源连接
- SQL 查询执行
- 查询历史记录

## 故障排除

### 常见问题

#### 1. 连接被拒绝

**错误**: `Connection refused` 或 `Can't connect to MySQL server`

**解决方案**:
- 检查 MySQL 服务是否运行：`sudo systemctl status mysql`
- 检查端口是否正确（默认 3306）
- 检查防火墙设置

#### 2. 认证失败

**错误**: `Access denied for user`

**解决方案**:
- 检查用户名和密码是否正确
- 确认用户有访问数据库的权限
- 检查主机名是否正确（localhost vs 127.0.0.1）

#### 3. 字符集问题

**错误**: 中文字符显示为乱码

**解决方案**:
- 确保数据库使用 utf8mb4 字符集
- 检查连接字符串中的 charset 参数
- 确认表的字符集设置正确

#### 4. 依赖包问题

**错误**: `No module named 'pymysql'` 或类似错误

**解决方案**:
```bash
# 重新安装依赖
pip install -r requirements.txt

# 或单独安装 MySQL 驱动
pip install pymysql mysqlclient
```

### 性能优化

#### 1. 连接池配置

根据您的应用负载调整连接池参数：

```env
# 高负载环境
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=60
DB_POOL_RECYCLE=3600
```

#### 2. MySQL 配置优化

编辑 MySQL 配置文件（通常是 `/etc/mysql/my.cnf`）：

```ini
[mysqld]
# 增加最大连接数
max_connections = 200

# 优化缓冲区大小
innodb_buffer_pool_size = 1G

# 启用查询缓存
query_cache_type = 1
query_cache_size = 64M
```

## 支持

如果您在迁移过程中遇到问题，请：

1. 检查应用程序日志
2. 查看 MySQL 错误日志：`sudo tail -f /var/log/mysql/error.log`
3. 参考本指南的故障排除部分
4. 联系技术支持团队

---

**注意**: 在生产环境中进行迁移之前，请务必在测试环境中完整测试整个迁移流程。
