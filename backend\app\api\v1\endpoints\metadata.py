"""
元数据管理API接口
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.datasource import DataSource

router = APIRouter()


@router.get("/datasources", response_model=List[Dict[str, Any]])
async def get_metadata_tree(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取元数据树结构
    """
    try:
        # 获取用户可访问的数据源（使用status字段而不是is_active）
        datasources = db.query(DataSource).filter(
            DataSource.status.in_(["connected", "disconnected"])
        ).all()

        metadata_tree = []

        for ds in datasources:
            # 构建数据源节点
            datasource_node = {
                "id": f"ds_{ds.id}",
                "name": ds.name,
                "description": ds.description or f"{ds.type} 数据源",
                "type": "datasource",
                "databases": []
            }

            # 模拟数据库结构（实际应该从数据源连接获取）
            # 这里先返回一个示例结构
            if ds.type == "mysql":
                databases = get_mysql_metadata(ds)
            elif ds.type == "postgresql":
                databases = get_postgresql_metadata(ds)
            else:
                databases = get_default_metadata(ds)

            datasource_node["databases"] = databases
            metadata_tree.append(datasource_node)

        return metadata_tree

    except Exception as e:
        import traceback
        print(f"元数据API错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取元数据失败: {str(e)}"
        )


def get_mysql_metadata(datasource: DataSource) -> List[Dict[str, Any]]:
    """获取MySQL数据源的元数据"""
    # 这里应该连接到实际的MySQL数据库获取元数据
    # 为了演示，返回模拟数据
    return [
        {
            "id": f"db_{datasource.id}_1",
            "name": datasource.database or "default_db",
            "description": "主数据库",
            "type": "database",
            "tables": [
                {
                    "id": f"table_{datasource.id}_1",
                    "name": "users",
                    "description": "用户表",
                    "type": "table",
                    "columns": [
                        {
                            "id": f"col_{datasource.id}_1_1",
                            "name": "id",
                            "type": "INT",
                            "description": "用户ID",
                            "nullable": False,
                            "primary_key": True
                        },
                        {
                            "id": f"col_{datasource.id}_1_2",
                            "name": "username",
                            "type": "VARCHAR(100)",
                            "description": "用户名",
                            "nullable": False,
                            "primary_key": False
                        },
                        {
                            "id": f"col_{datasource.id}_1_3",
                            "name": "email",
                            "type": "VARCHAR(255)",
                            "description": "邮箱地址",
                            "nullable": True,
                            "primary_key": False
                        },
                        {
                            "id": f"col_{datasource.id}_1_4",
                            "name": "created_at",
                            "type": "DATETIME",
                            "description": "创建时间",
                            "nullable": False,
                            "primary_key": False
                        }
                    ]
                },
                {
                    "id": f"table_{datasource.id}_2",
                    "name": "orders",
                    "description": "订单表",
                    "type": "table",
                    "columns": [
                        {
                            "id": f"col_{datasource.id}_2_1",
                            "name": "id",
                            "type": "INT",
                            "description": "订单ID",
                            "nullable": False,
                            "primary_key": True
                        },
                        {
                            "id": f"col_{datasource.id}_2_2",
                            "name": "user_id",
                            "type": "INT",
                            "description": "用户ID",
                            "nullable": False,
                            "primary_key": False
                        },
                        {
                            "id": f"col_{datasource.id}_2_3",
                            "name": "total_amount",
                            "type": "DECIMAL(10,2)",
                            "description": "订单总金额",
                            "nullable": False,
                            "primary_key": False
                        },
                        {
                            "id": f"col_{datasource.id}_2_4",
                            "name": "status",
                            "type": "VARCHAR(50)",
                            "description": "订单状态",
                            "nullable": False,
                            "primary_key": False
                        },
                        {
                            "id": f"col_{datasource.id}_2_5",
                            "name": "created_at",
                            "type": "DATETIME",
                            "description": "创建时间",
                            "nullable": False,
                            "primary_key": False
                        }
                    ]
                }
            ]
        }
    ]


def get_postgresql_metadata(datasource: DataSource) -> List[Dict[str, Any]]:
    """获取PostgreSQL数据源的元数据"""
    # 类似MySQL的实现
    return [
        {
            "id": f"db_{datasource.id}_1",
            "name": datasource.database or "public",
            "description": "公共模式",
            "type": "database",
            "tables": [
                {
                    "id": f"table_{datasource.id}_1",
                    "name": "products",
                    "description": "产品表",
                    "type": "table",
                    "columns": [
                        {
                            "id": f"col_{datasource.id}_1_1",
                            "name": "id",
                            "type": "SERIAL",
                            "description": "产品ID",
                            "nullable": False,
                            "primary_key": True
                        },
                        {
                            "id": f"col_{datasource.id}_1_2",
                            "name": "name",
                            "type": "VARCHAR(255)",
                            "description": "产品名称",
                            "nullable": False,
                            "primary_key": False
                        },
                        {
                            "id": f"col_{datasource.id}_1_3",
                            "name": "price",
                            "type": "NUMERIC(10,2)",
                            "description": "产品价格",
                            "nullable": False,
                            "primary_key": False
                        }
                    ]
                }
            ]
        }
    ]


def get_default_metadata(datasource: DataSource) -> List[Dict[str, Any]]:
    """获取默认数据源的元数据"""
    return [
        {
            "id": f"db_{datasource.id}_1",
            "name": datasource.database or "default",
            "description": "默认数据库",
            "type": "database",
            "tables": [
                {
                    "id": f"table_{datasource.id}_1",
                    "name": "sample_table",
                    "description": "示例表",
                    "type": "table",
                    "columns": [
                        {
                            "id": f"col_{datasource.id}_1_1",
                            "name": "id",
                            "type": "INTEGER",
                            "description": "主键ID",
                            "nullable": False,
                            "primary_key": True
                        },
                        {
                            "id": f"col_{datasource.id}_1_2",
                            "name": "name",
                            "type": "TEXT",
                            "description": "名称",
                            "nullable": True,
                            "primary_key": False
                        }
                    ]
                }
            ]
        }
    ]


@router.get("/datasources/{datasource_id}/refresh")
async def refresh_datasource_metadata(
    datasource_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    刷新指定数据源的元数据
    """
    try:
        # 检查数据源是否存在
        datasource = db.query(DataSource).filter(
            DataSource.id == datasource_id,
            DataSource.status.in_(["connected", "disconnected"])
        ).first()
        
        if not datasource:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="数据源不存在"
            )
        
        # 这里应该实现实际的元数据刷新逻辑
        # 连接到数据源，获取最新的表结构信息
        
        return {"message": f"数据源 {datasource.name} 的元数据已刷新"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"刷新元数据失败: {str(e)}"
        )
