from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import uvicorn
from contextlib import asynccontextmanager

from app.core.config import settings
from app.api.v1.api import api_router
from app.core.database import init_db

# 导入所有模型以确保它们被SQLAlchemy识别
from app.models import user, query, datasource, llm_config, audit_log, system_setting, role

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化数据库
    init_db()
    yield

app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix=settings.API_V1_STR)

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    from app.core.database import SessionLocal
    from app.core.security import get_password_hash
    from app.models.user import User

    db = SessionLocal()
    try:
        # 检查是否存在管理员用户
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            # 创建默认管理员用户
            hashed_password = get_password_hash("admin123")
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                name="系统管理员",
                hashed_password=hashed_password,
                role="admin",
                is_active=True
            )
            db.add(admin_user)
            db.commit()
            print("已创建默认管理员用户: admin/admin123")
        else:
            # 确保管理员用户的密码是正确的
            if admin_user.hashed_password == "CHANGE_THIS_PASSWORD_HASH":
                admin_user.hashed_password = get_password_hash("admin123")
                db.commit()
                print("已更新管理员用户密码: admin/admin123")
    except Exception as e:
        print(f"初始化管理员用户失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )