# NL2SQL 数据智能分析系统

一个基于自然语言处理的SQL查询生成和数据分析平台，支持将用户的自然语言查询转换为SQL语句并执行。

## 系统架构

- **后端**: FastAPI + SQLAlchemy + MySQL/PostgreSQL
- **前端**: Next.js + React + TypeScript + Tailwind CSS
- **LLM集成**: OpenAI、Anthropic、Groq等多种LLM提供商
- **缓存**: Redis
- **数据库**: MySQL 8.0+

## 功能特性

- 🤖 自然语言转SQL查询
- 📊 多种数据可视化图表
- 🔌 多数据源支持（MySQL、PostgreSQL、SQLite等）
- 🧠 多LLM提供商集成
- 📝 查询历史记录
- ⭐ 收藏夹功能
- 👥 用户权限管理
- 🔒 数据安全加密
- 📈 查询性能监控

## 快速开始

### 环境要求

- Python 3.8+
- Node.js 18+
- MySQL 8.0+ 或 PostgreSQL 12+
- Redis 6.0+

### 1. 克隆项目

```bash
git clone <repository-url>
cd nl2sql-system
```

### 2. 后端部署

#### 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

#### 配置环境变量

创建 `.env` 文件：

```bash
# 应用配置
APP_NAME=NL2SQL 数据智能分析系统
APP_VERSION=1.0.0
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/nl2sql_system

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# LLM配置
DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_MODEL=gpt-3.5-turbo
```

#### 初始化数据库

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE nl2sql_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 执行初始化脚本
mysql -u root -p nl2sql_system < sql/init.sql
```

#### 启动后端服务

```bash
python main.py
```

后端服务将在 http://localhost:8000 启动

### 3. 前端部署

#### 安装依赖

```bash
cd interface
npm install
```

#### 配置环境变量

创建 `.env.local` 文件：

```bash
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
```

#### 启动前端服务

```bash
npm run dev
```

前端服务将在 http://localhost:3000 启动

## 配置指南

### 1. 数据源配置

1. 登录系统后，进入"数据源管理"页面
2. 点击"添加数据源"
3. 填写数据库连接信息：
   - 名称：数据源显示名称
   - 类型：MySQL、PostgreSQL、SQLite等
   - 主机：数据库服务器地址
   - 端口：数据库端口
   - 数据库名：要连接的数据库名
   - 用户名/密码：数据库认证信息
4. 点击"测试连接"验证配置
5. 保存并同步元数据

### 2. LLM提供商配置

1. 进入"LLM配置"页面
2. 点击"添加提供商"
3. 选择提供商类型（OpenAI、Anthropic、Groq等）
4. 填写配置信息：
   - API密钥：从提供商获取的API密钥
   - 基础URL：自定义API端点（可选）
   - 支持模型：选择可用的模型列表
5. 点击"测试连接"验证配置
6. 保存配置

### 3. 用户权限配置

系统预设三种角色：
- **管理员**：完整系统权限
- **数据分析师**：查询和分析权限
- **普通用户**：只读权限

## 使用指南

### 1. 自然语言查询

1. 在主页输入框中输入自然语言查询
2. 选择目标数据源
3. 点击"查询"按钮
4. 系统将自动：
   - 将自然语言转换为SQL
   - 执行SQL查询
   - 展示结果数据
   - 提供多种可视化选项

**查询示例**：
- "查询2023年销售额最高的5个产品"
- "统计每个月的用户注册数量"
- "找出订单金额超过1000元的客户"

### 2. 查询历史

- 在左侧边栏查看"查询历史"
- 点击历史记录可重新执行
- 支持按状态和时间筛选

### 3. 收藏夹

- 将常用查询添加到收藏夹
- 支持标签分类管理
- 可设置为公开分享

### 4. 数据导出

- 支持CSV、Excel、JSON格式导出
- 可导出完整查询结果
- 异步处理大数据量导出

## API文档

启动后端服务后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 测试

### 后端测试

```bash
cd backend
pytest tests/ -v
```

### 前端测试

```bash
cd interface
npm test
```

## 生产部署

### 使用Docker部署

1. 构建镜像：

```bash
# 后端
cd backend
docker build -t nl2sql-backend .

# 前端
cd interface
docker build -t nl2sql-frontend .
```

2. 使用Docker Compose：

```yaml
version: '3.8'
services:
  backend:
    image: nl2sql-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://root:password@mysql:3306/nl2sql_system
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - mysql
      - redis

  frontend:
    image: nl2sql-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=nl2sql_system
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6.2-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 性能优化建议

1. **数据库优化**：
   - 为常用查询字段添加索引
   - 定期清理查询历史记录
   - 使用连接池管理数据库连接

2. **缓存策略**：
   - 启用查询结果缓存
   - 合理设置缓存过期时间
   - 使用Redis集群提高可用性

3. **LLM调用优化**：
   - 设置合理的超时时间
   - 实现请求重试机制
   - 监控API调用频率和成本

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接参数是否正确
   - 确认网络连通性

2. **LLM API调用失败**
   - 验证API密钥是否有效
   - 检查网络连接和代理设置
   - 确认API配额是否充足

3. **前端无法连接后端**
   - 检查CORS配置
   - 验证API地址是否正确
   - 确认后端服务是否正常运行

### 日志查看

- 后端日志：`logs/app.log`
- 前端日志：浏览器开发者工具控制台

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- 项目Issues：[GitHub Issues](https://github.com/your-repo/issues)