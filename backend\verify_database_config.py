#!/usr/bin/env python3
"""
数据库配置验证脚本

此脚本用于验证数据库配置是否正确，并测试数据库连接。
在启动应用程序之前运行此脚本可以帮助您快速发现配置问题。

使用方法:
    python verify_database_config.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from app.core.config import settings
    from app.core.database import test_database_connection, create_database_if_not_exists
    from sqlalchemy import create_engine, text
    import logging
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def print_header(title):
    """打印标题"""
    print(f"\n{'='*50}")
    print(f" {title}")
    print(f"{'='*50}")

def print_config_info():
    """打印配置信息"""
    print_header("数据库配置信息")
    
    print(f"数据库类型: {settings.DB_TYPE}")
    print(f"主机地址: {settings.DB_HOST}")
    print(f"端口: {settings.DB_PORT}")
    print(f"用户名: {settings.DB_USER}")
    print(f"密码: {'*' * len(settings.DB_PASSWORD) if settings.DB_PASSWORD else '(未设置)'}")
    print(f"数据库名: {settings.DB_NAME}")
    
    if settings.DB_TYPE == "mysql":
        print(f"字符集: {settings.DB_CHARSET}")
    
    print(f"\n连接池配置:")
    print(f"  池大小: {settings.DB_POOL_SIZE}")
    print(f"  最大溢出: {settings.DB_MAX_OVERFLOW}")
    print(f"  超时时间: {settings.DB_POOL_TIMEOUT}秒")
    print(f"  回收时间: {settings.DB_POOL_RECYCLE}秒")
    
    # 显示数据库URL（隐藏密码）
    db_url = settings.database_url
    if settings.DB_PASSWORD:
        db_url = db_url.replace(settings.DB_PASSWORD, "***")
    print(f"\n数据库连接URL: {db_url}")

def check_required_config():
    """检查必需的配置项"""
    print_header("配置项检查")
    
    required_configs = {
        "DB_TYPE": settings.DB_TYPE,
        "DB_HOST": settings.DB_HOST,
        "DB_PORT": settings.DB_PORT,
        "DB_USER": settings.DB_USER,
        "DB_NAME": settings.DB_NAME,
        "SECRET_KEY": settings.SECRET_KEY
    }
    
    missing_configs = []
    
    for key, value in required_configs.items():
        if not value:
            missing_configs.append(key)
            print(f"❌ {key}: 未设置")
        else:
            print(f"✅ {key}: 已设置")
    
    if settings.DB_TYPE in ["mysql", "postgresql"] and not settings.DB_PASSWORD:
        missing_configs.append("DB_PASSWORD")
        print(f"❌ DB_PASSWORD: 未设置（{settings.DB_TYPE} 需要密码）")
    else:
        print(f"✅ DB_PASSWORD: 已设置")
    
    if missing_configs:
        print(f"\n❌ 发现 {len(missing_configs)} 个配置问题:")
        for config in missing_configs:
            print(f"   - {config}")
        return False
    else:
        print(f"\n✅ 所有必需配置项都已正确设置")
        return True

def test_database_server_connection():
    """测试数据库服务器连接（不连接到特定数据库）"""
    print_header("数据库服务器连接测试")
    
    if settings.DB_TYPE == "sqlite":
        print("✅ SQLite 不需要服务器连接测试")
        return True
    
    try:
        if settings.DB_TYPE == "mysql":
            # 连接到 MySQL 服务器（不指定数据库）
            server_url = f"mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/?charset={settings.DB_CHARSET}"
        elif settings.DB_TYPE == "postgresql":
            # 连接到 PostgreSQL 服务器（使用默认数据库）
            server_url = f"postgresql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/postgres"
        else:
            print(f"❌ 不支持的数据库类型: {settings.DB_TYPE}")
            return False
        
        engine = create_engine(server_url)
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        
        print(f"✅ {settings.DB_TYPE.upper()} 服务器连接成功")
        engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ 数据库服务器连接失败: {e}")
        print(f"   请检查:")
        print(f"   - {settings.DB_TYPE.upper()} 服务是否运行")
        print(f"   - 主机地址和端口是否正确")
        print(f"   - 用户名和密码是否正确")
        print(f"   - 网络连接是否正常")
        return False

def test_database_creation():
    """测试数据库创建"""
    print_header("数据库创建测试")
    
    if settings.DB_TYPE == "sqlite":
        print("✅ SQLite 会自动创建数据库文件")
        return True
    
    try:
        result = create_database_if_not_exists()
        if result:
            print(f"✅ 数据库 '{settings.DB_NAME}' 准备就绪")
            return True
        else:
            print(f"❌ 数据库创建失败")
            return False
    except Exception as e:
        print(f"❌ 数据库创建测试失败: {e}")
        return False

def test_application_database_connection():
    """测试应用程序数据库连接"""
    print_header("应用程序数据库连接测试")
    
    try:
        result = test_database_connection()
        if result:
            print("✅ 应用程序数据库连接成功")
            return True
        else:
            print("❌ 应用程序数据库连接失败")
            return False
    except Exception as e:
        print(f"❌ 应用程序数据库连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("NL2SQL 数据库配置验证工具")
    print("=" * 50)
    
    # 检查配置文件是否存在
    env_file = project_root / ".env"
    if not env_file.exists():
        print("❌ .env 配置文件不存在")
        print("请复制 .env.example 为 .env 并配置数据库连接信息")
        sys.exit(1)
    
    print(f"✅ 配置文件存在: {env_file}")
    
    # 执行各项检查
    checks = [
        ("配置项检查", check_required_config),
        ("数据库服务器连接", test_database_server_connection),
        ("数据库创建", test_database_creation),
        ("应用程序连接", test_application_database_connection)
    ]
    
    # 显示配置信息
    print_config_info()
    
    # 执行检查
    all_passed = True
    for check_name, check_func in checks:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name} 检查时发生错误: {e}")
            all_passed = False
    
    # 总结
    print_header("验证结果")
    if all_passed:
        print("🎉 所有检查都通过了！")
        print("您可以安全地启动应用程序:")
        print("   python main.py")
    else:
        print("❌ 发现配置问题，请根据上述提示进行修复")
        print("修复后可以重新运行此脚本进行验证")
        sys.exit(1)

if __name__ == "__main__":
    main()
