#!/usr/bin/env python3
"""
创建测试用户脚本

此脚本用于在数据库中创建测试用户，方便测试认证系统。

使用方法:
    python create_test_users.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from app.core.config import settings
    from app.core.database import get_db, init_db
    from app.core.security import get_password_hash
    from app.models.user import User
    from sqlalchemy.orm import Session
    import logging
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def create_test_users():
    """创建测试用户"""
    
    # 测试用户数据
    test_users = [
        {
            "username": "admin",
            "email": "<EMAIL>",
            "name": "系统管理员",
            "password": "admin123",
            "role": "admin",
            "department": "技术部",
            "position": "系统管理员",
            "is_active": True,
            "status": "active"
        },
        {
            "username": "analyst",
            "email": "<EMAIL>", 
            "name": "数据分析师",
            "password": "analyst123",
            "role": "analyst",
            "department": "数据部",
            "position": "高级分析师",
            "is_active": True
        },
        {
            "username": "user",
            "email": "<EMAIL>",
            "name": "普通用户",
            "password": "user123", 
            "role": "user",
            "department": "业务部",
            "position": "业务专员",
            "is_active": True
        },
        {
            "username": "testuser",
            "email": "<EMAIL>",
            "name": "测试用户",
            "password": "test123",
            "role": "user",
            "department": "测试部",
            "position": "测试工程师",
            "is_active": True
        }
    ]
    
    try:
        logger.info("开始创建测试用户...")
        
        # 初始化数据库
        init_db()
        
        # 获取数据库会话
        db = next(get_db())
        
        created_count = 0
        updated_count = 0
        
        for user_data in test_users:
            try:
                # 检查用户是否已存在
                existing_user = db.query(User).filter(
                    (User.username == user_data["username"]) | 
                    (User.email == user_data["email"])
                ).first()
                
                if existing_user:
                    # 更新现有用户
                    existing_user.name = user_data["name"]
                    existing_user.role = user_data["role"]
                    existing_user.department = user_data.get("department")
                    existing_user.position = user_data.get("position")
                    existing_user.is_active = user_data["is_active"]
                    existing_user.status = user_data.get("status", "active")
                    existing_user.hashed_password = get_password_hash(user_data["password"])
                    
                    logger.info(f"✅ 更新用户: {user_data['username']} ({user_data['role']})")
                    updated_count += 1
                else:
                    # 创建新用户
                    hashed_password = get_password_hash(user_data["password"])
                    
                    new_user = User(
                        username=user_data["username"],
                        email=user_data["email"],
                        name=user_data["name"],
                        hashed_password=hashed_password,
                        role=user_data["role"],
                        department=user_data.get("department"),
                        position=user_data.get("position"),
                        is_active=user_data["is_active"],
                        status=user_data.get("status", "active")
                    )
                    
                    db.add(new_user)
                    logger.info(f"✅ 创建用户: {user_data['username']} ({user_data['role']})")
                    created_count += 1
                    
            except Exception as e:
                logger.error(f"❌ 处理用户 {user_data['username']} 时发生错误: {e}")
                continue
        
        # 提交更改
        db.commit()
        
        logger.info(f"\n🎉 测试用户创建完成!")
        logger.info(f"   创建新用户: {created_count}")
        logger.info(f"   更新用户: {updated_count}")
        
        # 显示用户信息
        print("\n" + "="*60)
        print("测试用户账户信息:")
        print("="*60)
        
        for user_data in test_users:
            print(f"用户名: {user_data['username']}")
            print(f"密码: {user_data['password']}")
            print(f"角色: {user_data['role']}")
            print(f"邮箱: {user_data['email']}")
            print("-" * 30)
        
        print("\n💡 使用提示:")
        print("1. 使用上述账户信息登录系统")
        print("2. 不同角色用户有不同的权限:")
        print("   - admin: 完整系统权限")
        print("   - analyst: 数据分析权限")
        print("   - user: 基础查询权限")
        print("3. 可以在个人中心修改密码和个人信息")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建测试用户失败: {e}")
        return False
    finally:
        db.close()

def verify_users():
    """验证用户创建结果"""
    try:
        db = next(get_db())
        
        users = db.query(User).all()
        
        print(f"\n📊 数据库中的用户统计:")
        print(f"总用户数: {len(users)}")
        
        role_counts = {}
        for user in users:
            role_counts[user.role] = role_counts.get(user.role, 0) + 1
        
        for role, count in role_counts.items():
            print(f"{role}: {count} 人")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 验证用户失败: {e}")
        return False
    finally:
        db.close()

def main():
    """主函数"""
    print("NL2SQL 测试用户创建工具")
    print("=" * 50)
    
    # 检查数据库连接
    try:
        from app.core.database import test_database_connection
        if not test_database_connection():
            print("❌ 数据库连接失败，请检查配置")
            sys.exit(1)
        print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接检查失败: {e}")
        sys.exit(1)
    
    # 创建测试用户
    if create_test_users():
        # 验证创建结果
        verify_users()
        print("\n🎉 所有操作完成!")
    else:
        print("\n❌ 操作失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
