"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-settings/page",{

/***/ "(app-pages-browser)/./components/data-source-config.tsx":
/*!*******************************************!*\
  !*** ./components/data-source-config.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DataSourceConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube-diagonal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-sync.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst mockDataSources = [\n    {\n        id: \"1\",\n        name: \"生产数据库\",\n        type: \"mysql\",\n        host: \"prod-db.example.com\",\n        port: 3306,\n        database: \"production\",\n        username: \"app_user\",\n        password: \"encrypted_password\",\n        status: \"connected\",\n        description: \"生产环境主数据库\",\n        cronJob: \"0 0 * * *\",\n        createdAt: \"2024-01-01\",\n        updatedAt: \"2024-01-15\",\n        lastTested: \"2024-01-15 10:30:00\",\n        lastSync: \"2024-01-15 10:30:00\"\n    },\n    {\n        id: \"2\",\n        name: \"测试数据库\",\n        type: \"postgresql\",\n        host: \"test-db.example.com\",\n        port: 5432,\n        database: \"testdb\",\n        username: \"test_user\",\n        password: \"encrypted_password\",\n        status: \"connected\",\n        description: \"测试环境数据库\",\n        cronJob: \"0 0 * * *\",\n        createdAt: \"2024-01-02\",\n        updatedAt: \"2024-01-10\",\n        lastTested: \"2024-01-14 15:20:00\",\n        lastSync: \"2024-01-14 15:20:00\"\n    },\n    {\n        id: \"3\",\n        name: \"数据仓库\",\n        type: \"doris\",\n        host: \"dws.example.com\",\n        port: 9030,\n        database: \"DWPROD\",\n        username: \"dw_user\",\n        password: \"encrypted_password\",\n        status: \"error\",\n        description: \"数据仓库Doris\",\n        cronJob: \"0 0 * * *\",\n        createdAt: \"2024-01-03\",\n        updatedAt: \"2024-01-12\",\n        lastTested: \"2024-01-12 09:45:00\",\n        lastSync: \"2024-01-14 15:20:00\"\n    }\n];\nconst databaseTypes = [\n    {\n        value: \"doris\",\n        label: \"Doris\"\n    },\n    {\n        value: \"mysql\",\n        label: \"MySQL\"\n    },\n    {\n        value: \"postgresql\",\n        label: \"PostgreSQL\"\n    },\n    {\n        value: \"oracle\",\n        label: \"Oracle\"\n    },\n    {\n        value: \"sqlserver\",\n        label: \"SQL Server\"\n    },\n    {\n        value: \"sqlite\",\n        label: \"SQLite\"\n    }\n];\nfunction DataSourceConfig() {\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [dataSources, setDataSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDataSource, setSelectedDataSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTesting, setIsTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        type: \"mysql\",\n        host: \"\",\n        port: 3306,\n        database: \"\",\n        username: \"\",\n        password: \"\",\n        description: \"\"\n    });\n    const handleCreateDataSource = ()=>{\n        const newDataSource = {\n            id: Date.now().toString(),\n            ...formData,\n            status: \"disconnected\",\n            createdAt: new Date().toISOString().split(\"T\")[0],\n            updatedAt: new Date().toISOString().split(\"T\")[0]\n        };\n        setDataSources([\n            ...dataSources,\n            newDataSource\n        ]);\n        setIsCreateDialogOpen(false);\n        resetForm();\n    };\n    const handleEditDataSource = ()=>{\n        if (!selectedDataSource) return;\n        const updatedDataSources = dataSources.map((ds)=>ds.id === selectedDataSource.id ? {\n                ...ds,\n                ...formData,\n                updatedAt: new Date().toISOString().split(\"T\")[0]\n            } : ds);\n        setDataSources(updatedDataSources);\n        setIsEditDialogOpen(false);\n        resetForm();\n    };\n    const handleDeleteDataSource = (id)=>{\n        if (confirm(\"确定要删除这个数据源吗？\")) {\n            setDataSources(dataSources.filter((ds)=>ds.id !== id));\n        }\n    };\n    const handleTestConnection = async (dataSource)=>{\n        setIsTesting(true);\n        setTestResult(null);\n        // Simulate connection test\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        const success = Math.random() > 0.3 // 70% success rate for demo\n        ;\n        setTestResult({\n            success,\n            message: success ? \"连接测试成功！数据库连接正常。\" : \"连接测试失败：无法连接到数据库，请检查配置信息。\"\n        });\n        if (dataSource && success) {\n            // Update the data source status\n            const updatedDataSources = dataSources.map((ds)=>ds.id === dataSource.id ? {\n                    ...ds,\n                    status: \"connected\",\n                    lastTested: new Date().toLocaleString()\n                } : ds);\n            setDataSources(updatedDataSources);\n        }\n        setIsTesting(false);\n    };\n    const openEditDialog = (dataSource)=>{\n        setSelectedDataSource(dataSource);\n        setFormData({\n            name: dataSource.name,\n            type: dataSource.type,\n            host: dataSource.host,\n            port: dataSource.port,\n            database: dataSource.database,\n            username: dataSource.username,\n            password: dataSource.password,\n            description: dataSource.description || \"\"\n        });\n        setIsEditDialogOpen(true);\n    };\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            type: \"mysql\",\n            host: \"\",\n            port: 3306,\n            database: \"\",\n            username: \"\",\n            password: \"\",\n            description: \"\"\n        });\n        setSelectedDataSource(null);\n        setTestResult(null);\n        setShowPassword(false);\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"已连接\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            case \"disconnected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: \"secondary\",\n                    children: \"未连接\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: \"destructive\",\n                    children: \"连接错误\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: \"outline\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case \"disconnected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getDefaultPort = (type)=>{\n        switch(type){\n            case \"doris\":\n                return 9030;\n            case \"mysql\":\n                return 3306;\n            case \"postgresql\":\n                return 5432;\n            case \"oracle\":\n                return 1521;\n            case \"sqlserver\":\n                return 1433;\n            case \"sqlite\":\n                return 0;\n            default:\n                return 3306;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"数据源配置\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"管理数据库连接和配置信息\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                        open: isCreateDialogOpen,\n                        onOpenChange: setIsCreateDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>resetForm(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"新建数据源\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                                className: \"max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                                children: \"创建数据源\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogDescription, {\n                                                children: \"配置新的数据库连接信息\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.Tabs, {\n                                        defaultValue: \"basic\",\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsList, {\n                                                className: \"grid w-full grid-cols-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                                        value: \"basic\",\n                                                        children: \"基本信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                                        value: \"connection\",\n                                                        children: \"连接配置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                                value: \"basic\",\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"name\",\n                                                                children: \"数据源名称\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"name\",\n                                                                value: formData.name,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        name: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入数据源名称\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"type\",\n                                                                children: \"数据库类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                                value: formData.type,\n                                                                onValueChange: (value)=>{\n                                                                    setFormData({\n                                                                        ...formData,\n                                                                        type: value,\n                                                                        port: getDefaultPort(value)\n                                                                    });\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                        children: databaseTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                                value: type.value,\n                                                                                children: type.label\n                                                                            }, type.value, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"description\",\n                                                                children: \"描述\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                                id: \"description\",\n                                                                value: formData.description,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        description: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入数据源描述（可选）\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                                value: \"connection\",\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"host\",\n                                                                        children: \"主机地址\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"host\",\n                                                                        value: formData.host,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                host: e.target.value\n                                                                            }),\n                                                                        placeholder: \"localhost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"port\",\n                                                                        children: \"端口\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"port\",\n                                                                        type: \"number\",\n                                                                        value: formData.port,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                port: Number.parseInt(e.target.value)\n                                                                            })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"database\",\n                                                                children: \"数据库名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"database\",\n                                                                value: formData.database,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        database: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入数据库名称\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"username\",\n                                                                        children: \"用户名\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"username\",\n                                                                        value: formData.username,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                username: e.target.value\n                                                                            }),\n                                                                        placeholder: \"输入用户名\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"password\",\n                                                                        children: \"密码\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"password\",\n                                                                                type: showPassword ? \"text\" : \"password\",\n                                                                                value: formData.password,\n                                                                                onChange: (e)=>setFormData({\n                                                                                        ...formData,\n                                                                                        password: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"输入密码\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                lineNumber: 391,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                                onClick: ()=>setShowPassword(!showPassword),\n                                                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                    lineNumber: 405,\n                                                                                    columnNumber: 41\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                    lineNumber: 405,\n                                                                                    columnNumber: 74\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                lineNumber: 398,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg border text-sm \".concat(testResult.success ? \"bg-green-50 border-green-200 text-green-800\" : \"bg-red-50 border-red-200 text-red-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                testResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 41\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 79\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: testResult.message\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setIsCreateDialogOpen(false),\n                                                children: \"取消\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                // type=\"button\"\n                                                // variant=\"outline\"\n                                                onClick: ()=>handleTestConnection(),\n                                                disabled: isTesting || !formData.name.trim() || !formData.host.trim() || !formData.database.trim() || !formData.username.trim() || !formData.password.trim(),\n                                                children: isTesting ? \"测试中...\" : \"测试连接\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleCreateDataSource,\n                                                disabled: !formData.name.trim() || !formData.host.trim() || !formData.database.trim() || !formData.username.trim() || !formData.password.trim(),\n                                                children: \"创建数据源\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: \"数据源列表\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                children: \"系统中的所有数据源连接\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"数据源\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"类型\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"同步作业Cron\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"最近同步时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableBody, {\n                                    children: dataSources.map((dataSource)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: dataSource.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: dataSource.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-900\",\n                                                        children: dataSource.type\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            getStatusIcon(dataSource.status),\n                                                            getStatusBadge(dataSource.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: dataSource.cronJob\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: dataSource.lastSync ? new Date(dataSource.lastSync).toLocaleString() : \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-test\",\n                                                                \"data-tooltip-content\": \"测试连接\",\n                                                                onClick: ()=>handleTestConnection(dataSource),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                        id: \"tooltip-test\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-sync\",\n                                                                \"data-tooltip-content\": \"元数据同步\",\n                                                                onClick: ()=>handleTestConnection(dataSource),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                        id: \"tooltip-sync\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-edit\",\n                                                                \"data-tooltip-content\": \"编辑\",\n                                                                onClick: ()=>openEditDialog(dataSource),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 513,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                        id: \"tooltip-edit\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-trash\",\n                                                                \"data-tooltip-content\": \"删除\",\n                                                                onClick: ()=>handleDeleteDataSource(dataSource.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                        id: \"tooltip-trash\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, dataSource.id, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                open: isEditDialogOpen,\n                onOpenChange: setIsEditDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                    children: \"编辑数据源\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogDescription, {\n                                    children: \"修改数据库连接信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.Tabs, {\n                            defaultValue: \"basic\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                            value: \"basic\",\n                                            children: \"基本信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                            value: \"connection\",\n                                            children: \"连接配置\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                    value: \"basic\",\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"数据源名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"name\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"输入数据源名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"type\",\n                                                    children: \"数据库类型\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                    value: formData.type,\n                                                    onValueChange: (value)=>{\n                                                        setFormData({\n                                                            ...formData,\n                                                            type: value,\n                                                            port: getDefaultPort(value)\n                                                        });\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                            children: databaseTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                    value: type.value,\n                                                                    children: type.label\n                                                                }, type.value, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            description: e.target.value\n                                                        }),\n                                                    placeholder: \"输入数据源描述（可选）\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                    value: \"connection\",\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"host\",\n                                                            children: \"主机地址\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"host\",\n                                                            value: formData.host,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    host: e.target.value\n                                                                }),\n                                                            placeholder: \"localhost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"port\",\n                                                            children: \"端口\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"port\",\n                                                            type: \"number\",\n                                                            value: formData.port,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    port: Number.parseInt(e.target.value)\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"database\",\n                                                    children: \"数据库名\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"database\",\n                                                    value: formData.database,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            database: e.target.value\n                                                        }),\n                                                    placeholder: \"输入数据库名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"username\",\n                                                            children: \"用户名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"username\",\n                                                            value: formData.username,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    username: e.target.value\n                                                                }),\n                                                            placeholder: \"输入用户名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"password\",\n                                                            children: \"密码\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"password\",\n                                                                    type: showPassword ? \"text\" : \"password\",\n                                                                    value: formData.password,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            password: e.target.value\n                                                                        }),\n                                                                    placeholder: \"输入密码\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 39\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 72\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, this),\n                        testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg border text-sm \".concat(testResult.success ? \"bg-green-50 border-green-200 text-green-800\" : \"bg-red-50 border-red-200 text-red-800\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    testResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 39\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 77\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: testResult.message\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>handleTestConnection(),\n                                    disabled: isTesting || !formData.host || !formData.database,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 15\n                                        }, this),\n                                        isTesting ? \"测试中...\" : \"测试连接\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsEditDialogOpen(false),\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleEditDataSource,\n                                    disabled: !formData.name.trim() || !formData.host.trim() || !formData.database.trim() || !formData.username.trim() || !formData.password.trim(),\n                                    children: \"保存\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 530,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                lineNumber: 529,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_s(DataSourceConfig, \"1aH60772uoJeXW+zgh5dCdZFilU=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DataSourceConfig;\nvar _c;\n$RefreshReg$(_c, \"DataSourceConfig\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/data-source-config.tsx\n"));

/***/ })

});