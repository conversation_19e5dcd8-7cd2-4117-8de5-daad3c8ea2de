"use client"

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import apiClient from '@/lib/api-client'

// 用户信息类型定义
export interface User {
  id: number
  username: string
  email: string
  name: string
  role: string
  status: string
  is_active: boolean
  department?: string
  position?: string
  avatar?: string
  last_login?: string
  created_at: string
  updated_at: string
}

// 认证上下文类型定义
interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (username: string, password: string) => Promise<void>
  logout: () => void
  refreshUser: () => Promise<void>
  checkAuth: () => Promise<boolean>
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 认证提供者组件
interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // 计算是否已认证
  const isAuthenticated = !!user && apiClient.isAuthenticated()

  // 检查认证状态
  const checkAuth = async (): Promise<boolean> => {
    try {
      if (!apiClient.isAuthenticated()) {
        setUser(null)
        return false
      }

      // 验证token是否有效
      const isValid = await apiClient.validateToken()
      if (!isValid) {
        setUser(null)
        apiClient.clearToken()
        return false
      }

      // 获取用户信息
      if (!user) {
        await refreshUser()
      }
      
      return true
    } catch (error) {
      console.error('认证检查失败:', error)
      setUser(null)
      apiClient.clearToken()
      return false
    }
  }

  // 刷新用户信息
  const refreshUser = async (): Promise<void> => {
    try {
      if (!apiClient.isAuthenticated()) {
        console.log('用户未认证，清除用户信息')
        setUser(null)
        return
      }

      console.log('调用getCurrentUser API...')
      const userData = await apiClient.getCurrentUser()
      console.log('获取到用户数据:', userData)
      setUser(userData)
    } catch (error) {
      console.error('获取用户信息失败:', error)
      setUser(null)
      apiClient.clearToken()
      throw error
    }
  }

  // 登录函数
  const login = async (username: string, password: string): Promise<void> => {
    try {
      setIsLoading(true)
      console.log('开始登录流程...')

      // 执行登录
      const loginResult = await apiClient.login(username, password)
      console.log('登录API调用成功:', loginResult)

      // 获取用户信息
      console.log('开始获取用户信息...')
      await refreshUser()
      console.log('用户信息获取成功')

      console.log('登录成功')
    } catch (error) {
      console.error('登录失败:', error)
      setUser(null)
      apiClient.clearToken()
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // 登出函数
  const logout = (): void => {
    try {
      // 清除用户状态
      setUser(null)
      
      // 清除API客户端token
      apiClient.clearToken()
      
      // 清除其他可能的本地存储
      if (typeof window !== 'undefined') {
        localStorage.removeItem('user')
        sessionStorage.clear()
      }
      
      console.log('已登出')
      
      // 重定向到登录页
      router.push('/login')
    } catch (error) {
      console.error('登出时发生错误:', error)
    }
  }

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsLoading(true)
        
        // 检查是否有token
        if (!apiClient.isAuthenticated()) {
          setUser(null)
          return
        }

        // 验证token并获取用户信息
        const isValid = await checkAuth()
        if (!isValid) {
          setUser(null)
        }
      } catch (error) {
        console.error('初始化认证失败:', error)
        setUser(null)
        apiClient.clearToken()
      } finally {
        setIsLoading(false)
      }
    }

    initAuth()
  }, [])

  // 监听存储变化（多标签页同步）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'access_token') {
        if (!e.newValue) {
          // Token被清除，登出用户
          setUser(null)
        } else {
          // Token被设置，重新检查认证
          checkAuth()
        }
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('storage', handleStorageChange)
      return () => window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshUser,
    checkAuth,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// 使用认证上下文的Hook
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// 检查是否为管理员的Hook
export function useIsAdmin(): boolean {
  const { user } = useAuth()
  return user?.role === 'admin'
}

// 检查用户权限的Hook
export function useHasPermission(requiredRole: string): boolean {
  const { user } = useAuth()
  
  if (!user) return false
  
  // 管理员拥有所有权限
  if (user.role === 'admin') return true
  
  // 检查特定角色权限
  const roleHierarchy = {
    'admin': 3,
    'analyst': 2,
    'user': 1
  }
  
  const userLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0
  
  return userLevel >= requiredLevel
}
