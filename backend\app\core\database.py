from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError, DatabaseError
import logging
from app.core.config import settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建数据库引擎，使用新的配置系统
engine = create_engine(
    settings.database_url,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_timeout=settings.DB_POOL_TIMEOUT,
    pool_recycle=settings.DB_POOL_RECYCLE,
    echo=settings.DEBUG  # 在调试模式下显示SQL语句
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def test_database_connection():
    """测试数据库连接"""
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        logger.info("数据库连接测试成功")
        return True
    except OperationalError as e:
        logger.error(f"数据库连接失败: {e}")
        return False
    except Exception as e:
        logger.error(f"数据库连接测试出现未知错误: {e}")
        return False

def create_database_if_not_exists():
    """如果数据库不存在则创建（仅适用于MySQL）"""
    if settings.DB_TYPE == "mysql":
        try:
            # 创建不包含数据库名的连接URL
            db_url_without_db = f"mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/?charset={settings.DB_CHARSET}"
            temp_engine = create_engine(db_url_without_db)

            with temp_engine.connect() as connection:
                # 检查数据库是否存在
                result = connection.execute(text(f"SHOW DATABASES LIKE '{settings.DB_NAME}'"))
                if not result.fetchone():
                    # 创建数据库
                    connection.execute(text(f"CREATE DATABASE {settings.DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                    logger.info(f"数据库 {settings.DB_NAME} 创建成功")
                else:
                    logger.info(f"数据库 {settings.DB_NAME} 已存在")

            temp_engine.dispose()
            return True
        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            return False
    return True

def init_db():
    """初始化数据库"""
    try:
        logger.info("开始初始化数据库...")
        logger.info(f"数据库类型: {settings.DB_TYPE}")
        logger.info(f"数据库连接URL: {settings.database_url.replace(settings.DB_PASSWORD, '***') if settings.DB_PASSWORD else settings.database_url}")

        # 对于MySQL，先尝试创建数据库
        if settings.DB_TYPE == "mysql":
            if not create_database_if_not_exists():
                raise Exception("无法创建数据库")

        # 测试数据库连接
        if not test_database_connection():
            raise Exception("数据库连接失败")

        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")

        logger.info("数据库初始化完成")
        return True

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise e