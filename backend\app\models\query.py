from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, ForeignKey, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

class QueryHistory(Base):
    __tablename__ = "query_history"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    natural_query = Column(Text, nullable=False)
    generated_sql = Column(Text, nullable=False)
    execution_status = Column(String(20), default="pending")
    results = Column(JSON)
    execution_time = Column(Integer)  # 毫秒
    error_message = Column(Text)
    data_source_id = Column(Integer, ForeignKey("data_sources.id"))
    created_at = Column(DateTime, server_default=func.now())
    
    user = relationship("User")
    data_source = relationship("DataSource")

class Favorite(Base):
    __tablename__ = "favorites"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, Foreign<PERSON>ey("users.id"), nullable=False)
    query_id = Column(Integer, ForeignKey("query_history.id"), nullable=False)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    tags = Column(JSON)
    is_public = Column(Boolean, default=False)
    created_at = Column(DateTime, server_default=func.now())
    
    user = relationship("User")
    query = relationship("QueryHistory")