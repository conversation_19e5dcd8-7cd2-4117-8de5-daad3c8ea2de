import asyncio
import pymysql
import psycopg2
import sqlite3
from typing import Dict, List, Any, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session
from app.models.datasource import DataSource, DatabaseMetadata
from app.core.database import get_db
from app.core.security import decrypt_password

class DatabaseService:
    def __init__(self):
        self.connections = {}
    
    async def test_connection(self, data_source_id: int) -> bool:
        """测试数据库连接"""
        try:
            db = next(get_db())
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
            if not data_source:
                return False
            
            engine = self._create_engine(data_source)
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except Exception:
            return False
    
    async def execute_query(self, sql: str, data_source_id: int) -> Dict[str, Any]:
        """执行SQL查询"""
        db = next(get_db())
        data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
        
        if not data_source:
            raise Exception("数据源不存在")
        
        engine = self._create_engine(data_source)
        
        try:
            with engine.connect() as conn:
                result = conn.execute(text(sql))
                
                if result.returns_rows:
                    columns = list(result.keys())
                    rows = [dict(zip(columns, row)) for row in result.fetchall()]
                    return {
                        "columns": columns,
                        "rows": rows,
                        "total_rows": len(rows)
                    }
                else:
                    return {
                        "message": f"查询执行成功，影响 {result.rowcount} 行",
                        "affected_rows": result.rowcount
                    }
        except Exception as e:
            raise Exception(f"SQL执行错误: {str(e)}")
    
    async def sync_metadata(self, data_source_id: int) -> bool:
        """同步数据库元数据"""
        db = next(get_db())
        data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
        
        if not data_source:
            return False
        
        try:
            # 清除旧的元数据
            db.query(DatabaseMetadata).filter(
                DatabaseMetadata.data_source_id == data_source_id
            ).delete()
            
            # 获取新的元数据
            metadata_list = self._extract_metadata(data_source)
            
            # 保存新的元数据
            for metadata in metadata_list:
                db_metadata = DatabaseMetadata(**metadata, data_source_id=data_source_id)
                db.add(db_metadata)
            
            db.commit()
            return True
        except Exception:
            db.rollback()
            return False
    
    def _create_engine(self, data_source: DataSource):
        """创建数据库引擎"""
        password = decrypt_password(data_source.password)
        
        if data_source.type == "mysql":
            url = f"mysql+pymysql://{data_source.username}:{password}@{data_source.host}:{data_source.port}/{data_source.database}"
        elif data_source.type == "postgresql":
            url = f"postgresql://{data_source.username}:{password}@{data_source.host}:{data_source.port}/{data_source.database}"
        elif data_source.type == "sqlite":
            url = f"sqlite:///{data_source.database}"
        else:
            raise Exception(f"不支持的数据库类型: {data_source.type}")
        
        return create_engine(url)
    
    def _extract_metadata(self, data_source: DataSource) -> List[Dict]:
        """提取数据库元数据"""
        engine = self._create_engine(data_source)
        metadata_list = []
        
        if data_source.type == "mysql":
            sql = """
            SELECT 
                TABLE_SCHEMA as database_name,
                TABLE_NAME as table_name,
                COLUMN_NAME as column_name,
                DATA_TYPE as column_type,
                COLUMN_COMMENT as column_comment,
                CASE WHEN COLUMN_KEY = 'PRI' THEN 1 ELSE 0 END as is_primary_key,
                CASE WHEN IS_NULLABLE = 'YES' THEN 1 ELSE 0 END as is_nullable
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = %s
            ORDER BY TABLE_NAME, ORDINAL_POSITION
            """
            
            with engine.connect() as conn:
                result = conn.execute(text(sql), (data_source.database,))
                for row in result:
                    metadata_list.append(dict(row._mapping))
        
        return metadata_list