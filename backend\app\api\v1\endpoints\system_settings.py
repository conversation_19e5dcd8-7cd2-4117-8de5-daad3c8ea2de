"""
系统设置API接口
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.schemas.system_setting import (
    SystemSetting,
    SystemSettingCreate,
    SystemSettingUpdate,
    ApplicationSettings,
    ApplicationSettingsUpdate,
    SettingsResponse,
    SystemSettingsBatch
)
from app.services.system_setting_service import SystemSettingService

router = APIRouter()


def get_setting_service(db: Session = Depends(get_db)) -> SystemSettingService:
    """获取系统设置服务实例"""
    return SystemSettingService(db)


@router.get("/application", response_model=ApplicationSettings)
async def get_application_settings(
    setting_service: SystemSettingService = Depends(get_setting_service),
    current_user: User = Depends(get_current_user)
):
    """
    获取应用程序设置
    """
    try:
        settings = setting_service.get_application_settings()
        return settings
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设置失败: {str(e)}"
        )


@router.put("/application", response_model=SettingsResponse)
async def update_application_settings(
    settings_update: ApplicationSettingsUpdate,
    setting_service: SystemSettingService = Depends(get_setting_service),
    current_user: User = Depends(get_current_user)
):
    """
    更新应用程序设置
    """
    # 检查用户权限（只有管理员可以修改设置）
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以修改系统设置"
        )

    try:
        updated_settings = setting_service.update_application_settings(settings_update)
        return SettingsResponse(
            success=True,
            message="设置更新成功",
            data=updated_settings
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新设置失败: {str(e)}"
        )


@router.post("/application/reset", response_model=SettingsResponse)
async def reset_application_settings(
    setting_service: SystemSettingService = Depends(get_setting_service),
    current_user: User = Depends(get_current_user)
):
    """
    重置应用程序设置为默认值
    """
    # 检查用户权限（只有管理员可以重置设置）
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以重置系统设置"
        )

    try:
        setting_service.reset_settings_to_default()
        settings = setting_service.get_application_settings()
        return SettingsResponse(
            success=True,
            message="设置已重置为默认值",
            data=settings
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置设置失败: {str(e)}"
        )


@router.get("/", response_model=List[SystemSetting])
async def get_all_settings(
    category: str = None,
    setting_service: SystemSettingService = Depends(get_setting_service),
    current_user: User = Depends(get_current_user)
):
    """
    获取所有系统设置
    """
    # 检查用户权限（只有管理员可以查看所有设置）
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以查看系统设置"
        )

    try:
        if category:
            settings = setting_service.get_settings_by_category(category)
        else:
            settings = setting_service.get_all_settings()
        
        return [SystemSetting.from_orm(setting) for setting in settings]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设置失败: {str(e)}"
        )


@router.get("/{key}", response_model=SystemSetting)
async def get_setting_by_key(
    key: str,
    setting_service: SystemSettingService = Depends(get_setting_service),
    current_user: User = Depends(get_current_user)
):
    """
    根据键名获取单个设置
    """
    # 检查用户权限（只有管理员可以查看设置详情）
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以查看系统设置"
        )

    try:
        setting = setting_service.get_setting_by_key(key)
        if not setting:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"设置 '{key}' 不存在"
            )
        
        return SystemSetting.from_orm(setting)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设置失败: {str(e)}"
        )


@router.put("/{key}", response_model=SystemSetting)
async def update_setting_by_key(
    key: str,
    setting_update: SystemSettingUpdate,
    setting_service: SystemSettingService = Depends(get_setting_service),
    current_user: User = Depends(get_current_user)
):
    """
    更新单个设置
    """
    # 检查用户权限（只有管理员可以修改设置）
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以修改系统设置"
        )

    try:
        # 只更新value字段
        if setting_update.value is not None:
            updated_setting = setting_service.update_setting(key, setting_update.value)
            if not updated_setting:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"设置 '{key}' 不存在"
                )
            return SystemSetting.from_orm(updated_setting)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须提供要更新的值"
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新设置失败: {str(e)}"
        )


@router.post("/", response_model=SystemSetting)
async def create_setting(
    setting_create: SystemSettingCreate,
    setting_service: SystemSettingService = Depends(get_setting_service),
    current_user: User = Depends(get_current_user)
):
    """
    创建新设置
    """
    # 检查用户权限（只有管理员可以创建设置）
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以创建系统设置"
        )

    try:
        setting = setting_service.create_setting(
            category=setting_create.category,
            key=setting_create.key,
            value=setting_create.value,
            value_type=setting_create.value_type,
            description=setting_create.description,
            is_editable=setting_create.is_editable
        )
        return SystemSetting.from_orm(setting)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建设置失败: {str(e)}"
        )


@router.delete("/{key}")
async def delete_setting(
    key: str,
    setting_service: SystemSettingService = Depends(get_setting_service),
    current_user: User = Depends(get_current_user)
):
    """
    删除设置
    """
    # 检查用户权限（只有管理员可以删除设置）
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以删除系统设置"
        )

    try:
        success = setting_service.delete_setting(key)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"设置 '{key}' 不存在"
            )
        
        return {"message": f"设置 '{key}' 已删除"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除设置失败: {str(e)}"
        )
