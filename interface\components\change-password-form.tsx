"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface ChangePasswordFormProps {
  onPasswordChanged: () => void
  onCancel: () => void
}

export default function ChangePasswordForm({ onPasswordChanged, onCancel }: ChangePasswordFormProps) {
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmNewPassword, setConfirmNewPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    if (newPassword !== confirmNewPassword) {
      setError("新密码和确认密码不匹配。")
      setIsLoading(false)
      return
    }

    if (newPassword.length < 6) {
      setError("新密码长度至少为6个字符。")
      setIsLoading(false)
      return
    }

    // Simulate API call to change password
    try {
      await new Promise((resolve) => setTimeout(resolve, 1500)) // Simulate network delay

      // Mock success/failure based on current password
      if (currentPassword === "password") {
        // Assuming "password" is the mock current password
        toast({
          title: "密码修改成功",
          description: "您的密码已成功更新。",
        })
        onPasswordChanged() // Notify parent component
      } else {
        setError("当前密码不正确。")
      }
    } catch (err) {
      setError("修改密码失败，请稍后重试。")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="border-none shadow-none">
      <CardHeader className="px-0 pt-0">
        <CardTitle>修改密码</CardTitle>
        <CardDescription>请填写以下字段以更新您的密码。</CardDescription>
      </CardHeader>
      <CardContent className="px-0">
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="currentPassword">当前密码</Label>
            <Input
              id="currentPassword"
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="newPassword">新密码</Label>
            <Input
              id="newPassword"
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirmNewPassword">确认新密码</Label>
            <Input
              id="confirmNewPassword"
              type="password"
              value={confirmNewPassword}
              onChange={(e) => setConfirmNewPassword(e.target.value)}
              required
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "保存中..." : "保存新密码"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
