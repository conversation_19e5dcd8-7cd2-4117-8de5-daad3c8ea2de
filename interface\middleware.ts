import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// 定义需要保护的路由
const protectedRoutes = [
  '/',
  '/profile',
  '/settings',
  '/admin-settings',
  '/docs'
]

// 定义公开路由（不需要认证）
const publicRoutes = [
  '/login',
  '/register'
]

// 定义需要管理员权限的路由
const adminRoutes = [
  '/admin-settings'
]

// 定义需要分析师及以上权限的路由
const analystRoutes = [
  '/docs'
]

// JWT token解析函数（简化版，仅用于检查token是否存在和基本格式）
function parseJWT(token: string) {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      return null
    }
    
    const payload = JSON.parse(atob(parts[1]))
    return payload
  } catch (error) {
    return null
  }
}

// 检查token是否过期
function isTokenExpired(payload: any): boolean {
  if (!payload.exp) {
    return true
  }
  
  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp < currentTime
}

// 从请求中获取token
function getTokenFromRequest(request: NextRequest): string | null {
  // 从Authorization header获取
  const authHeader = request.headers.get('authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }
  
  // 从cookie获取
  const tokenCookie = request.cookies.get('access_token')
  if (tokenCookie) {
    return tokenCookie.value
  }
  
  return null
}

// 检查路由是否需要保护
function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route => {
    if (route === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(route)
  })
}

// 检查路由是否为公开路由
function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route => pathname.startsWith(route))
}

// 检查路由是否需要管理员权限
function isAdminRoute(pathname: string): boolean {
  return adminRoutes.some(route => pathname.startsWith(route))
}

// 检查路由是否需要分析师权限
function isAnalystRoute(pathname: string): boolean {
  return analystRoutes.some(route => pathname.startsWith(route))
}

// 主中间件函数
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // 跳过静态资源和API路由
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') // 文件扩展名
  ) {
    return NextResponse.next()
  }

  console.log(`[Middleware] 处理路由: ${pathname}`)

  // 如果是公开路由，直接允许访问
  if (isPublicRoute(pathname)) {
    console.log(`[Middleware] 公开路由，允许访问: ${pathname}`)
    return NextResponse.next()
  }

  // 如果是受保护的路由，检查认证状态
  if (isProtectedRoute(pathname)) {
    const token = getTokenFromRequest(request)

    console.log(`[Middleware] 检查token: ${token ? '存在' : '不存在'}`)

    if (!token) {
      console.log(`[Middleware] 未找到token，重定向到登录页: ${pathname}`)
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // 解析token
    const payload = parseJWT(token)
    if (!payload) {
      console.log(`[Middleware] token格式无效，重定向到登录页: ${pathname}`)
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }

    console.log(`[Middleware] token解析成功，用户: ${payload.sub}, 过期时间: ${new Date(payload.exp * 1000).toLocaleString()}`)

    // 检查token是否过期
    if (isTokenExpired(payload)) {
      console.log(`[Middleware] token已过期，重定向到登录页: ${pathname}`)
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // 检查管理员权限
    if (isAdminRoute(pathname)) {
      const userRole = payload.role || 'user'
      if (userRole !== 'admin') {
        console.log(`[Middleware] 需要管理员权限，当前角色: ${userRole}`)
        return NextResponse.redirect(new URL('/', request.url))
      }
    }

    // 检查分析师权限
    if (isAnalystRoute(pathname)) {
      const userRole = payload.role || 'user'
      const roleHierarchy = { admin: 3, analyst: 2, user: 1 }
      const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0
      const requiredLevel = roleHierarchy.analyst
      
      if (userLevel < requiredLevel) {
        console.log(`[Middleware] 需要分析师权限，当前角色: ${userRole}`)
        return NextResponse.redirect(new URL('/', request.url))
      }
    }

    console.log(`[Middleware] 认证通过，允许访问: ${pathname}`)
  }

  return NextResponse.next()
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - 其他静态资源
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
}
