"use client"

import { usePathname } from 'next/navigation'
import MainHeader from '@/components/main-header'

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()
  
  // 定义不需要显示header的页面路径
  const noHeaderPaths = ['/login', '/register']
  
  // 检查当前路径是否需要隐藏header
  const shouldHideHeader = noHeaderPaths.includes(pathname)
  
  if (shouldHideHeader) {
    // 登录和注册页面：不显示header，全屏布局
    return (
      <div className="min-h-screen">
        {children}
      </div>
    )
  }
  
  // 其他页面：显示header，带padding-top
  return (
    <>
      <MainHeader />
      <div className="pt-[56px] h-screen flex flex-col">
        {children}
      </div>
    </>
  )
}
