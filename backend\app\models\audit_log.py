from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

class AuditLog(Base):
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    action = Column(String(100), nullable=False)  # login, query, create_datasource等
    resource_type = Column(String(50))  # user, datasource, query等
    resource_id = Column(Integer)
    details = Column(JSON)  # 详细信息
    ip_address = Column(String(45))
    user_agent = Column(Text)
    status = Column(String(20), default="success")  # success, failed
    created_at = Column(DateTime, server_default=func.now())
    
    user = relationship("User")