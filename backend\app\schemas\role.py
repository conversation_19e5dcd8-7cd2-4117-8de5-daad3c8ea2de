"""
角色管理相关的Pydantic模型
"""

from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class PermissionBase(BaseModel):
    """权限基础模型"""
    code: str = Field(..., description="权限代码")
    name: str = Field(..., description="权限名称")
    description: Optional[str] = Field(None, description="权限描述")
    category: str = Field(..., description="权限分类")


class Permission(PermissionBase):
    """权限响应模型"""
    id: int
    is_system: bool = Field(description="是否为系统权限")
    created_at: Optional[datetime] = Field(description="创建时间")

    class Config:
        from_attributes = True


class DataPermissionBase(BaseModel):
    """数据权限基础模型"""
    code: str = Field(..., description="数据权限代码")
    name: str = Field(..., description="数据权限名称")
    description: Optional[str] = Field(None, description="数据权限描述")
    resource_type: str = Field(..., description="资源类型")
    resource_id: str = Field(..., description="资源ID")
    parent_id: Optional[int] = Field(None, description="父级权限ID")


class DataPermission(DataPermissionBase):
    """数据权限响应模型"""
    id: int
    created_at: Optional[datetime] = Field(description="创建时间")

    class Config:
        from_attributes = True


class RoleBase(BaseModel):
    """角色基础模型"""
    name: str = Field(..., description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    is_active: bool = Field(default=True, description="是否激活")


class RoleCreate(RoleBase):
    """创建角色的模型"""
    permissions: List[str] = Field(default=[], description="权限代码列表")
    data_permissions: List[str] = Field(default=[], description="数据权限代码列表")


class RoleUpdate(BaseModel):
    """更新角色的模型"""
    name: Optional[str] = Field(None, description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    is_active: Optional[bool] = Field(None, description="是否激活")
    permissions: Optional[List[str]] = Field(None, description="权限代码列表")
    data_permissions: Optional[List[str]] = Field(None, description="数据权限代码列表")


class Role(RoleBase):
    """角色响应模型"""
    id: int
    is_system: bool = Field(description="是否为系统内置角色")
    permissions: List[str] = Field(description="权限代码列表")
    data_permissions: List[str] = Field(description="数据权限代码列表")
    user_count: int = Field(description="用户数量")
    created_at: Optional[datetime] = Field(description="创建时间")
    updated_at: Optional[datetime] = Field(description="更新时间")

    class Config:
        from_attributes = True


class RoleAssignment(BaseModel):
    """角色分配模型"""
    user_id: int = Field(..., description="用户ID")
    role_ids: List[int] = Field(..., description="角色ID列表")


class PermissionTree(BaseModel):
    """权限树模型"""
    category: str = Field(..., description="权限分类")
    permissions: List[Permission] = Field(..., description="权限列表")


class DataPermissionTree(BaseModel):
    """数据权限树模型"""
    resource_type: str = Field(..., description="资源类型")
    resource_id: str = Field(..., description="资源ID")
    name: str = Field(..., description="资源名称")
    children: List['DataPermissionTree'] = Field(default=[], description="子资源")
    code: Optional[str] = Field(None, description="权限代码")


# 更新前向引用
DataPermissionTree.model_rebuild()


class RolePermissionResponse(BaseModel):
    """角色权限响应模型"""
    role: Role = Field(..., description="角色信息")
    permission_tree: List[PermissionTree] = Field(..., description="权限树")
    data_permission_tree: List[DataPermissionTree] = Field(..., description="数据权限树")


class UserRoleResponse(BaseModel):
    """用户角色响应模型"""
    user_id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    name: str = Field(..., description="姓名")
    roles: List[Role] = Field(..., description="角色列表")


class RoleStatistics(BaseModel):
    """角色统计模型"""
    total_roles: int = Field(..., description="总角色数")
    system_roles: int = Field(..., description="系统角色数")
    custom_roles: int = Field(..., description="自定义角色数")
    active_roles: int = Field(..., description="激活角色数")
    total_permissions: int = Field(..., description="总权限数")
    total_data_permissions: int = Field(..., description="总数据权限数")


class BulkRoleOperation(BaseModel):
    """批量角色操作模型"""
    role_ids: List[int] = Field(..., description="角色ID列表")
    operation: str = Field(..., description="操作类型: activate, deactivate, delete")


class RoleExport(BaseModel):
    """角色导出模型"""
    roles: List[Role] = Field(..., description="角色列表")
    permissions: List[Permission] = Field(..., description="权限列表")
    data_permissions: List[DataPermission] = Field(..., description="数据权限列表")
    export_time: datetime = Field(..., description="导出时间")
    version: str = Field(default="1.0", description="版本号")


class RoleImport(BaseModel):
    """角色导入模型"""
    roles: List[RoleCreate] = Field(..., description="要导入的角色列表")
    overwrite_existing: bool = Field(default=False, description="是否覆盖现有角色")


class AdminSettingsData(BaseModel):
    """系统管理设置数据模型"""
    role_statistics: RoleStatistics = Field(..., description="角色统计")
    recent_activities: List[dict] = Field(default=[], description="最近活动")
    system_health: dict = Field(default={}, description="系统健康状态")
    security_alerts: List[dict] = Field(default=[], description="安全警报")


class SystemHealthCheck(BaseModel):
    """系统健康检查模型"""
    database_status: str = Field(..., description="数据库状态")
    cache_status: str = Field(..., description="缓存状态")
    api_status: str = Field(..., description="API状态")
    disk_usage: float = Field(..., description="磁盘使用率")
    memory_usage: float = Field(..., description="内存使用率")
    cpu_usage: float = Field(..., description="CPU使用率")
    uptime: int = Field(..., description="运行时间（秒）")
    last_check: datetime = Field(..., description="最后检查时间")


class SecurityAlert(BaseModel):
    """安全警报模型"""
    id: int = Field(..., description="警报ID")
    type: str = Field(..., description="警报类型")
    severity: str = Field(..., description="严重程度: low, medium, high, critical")
    message: str = Field(..., description="警报消息")
    details: dict = Field(default={}, description="详细信息")
    created_at: datetime = Field(..., description="创建时间")
    resolved: bool = Field(default=False, description="是否已解决")
    resolved_at: Optional[datetime] = Field(None, description="解决时间")


class RecentActivity(BaseModel):
    """最近活动模型"""
    id: int = Field(..., description="活动ID")
    user_id: Optional[int] = Field(None, description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    action: str = Field(..., description="操作类型")
    resource_type: str = Field(..., description="资源类型")
    resource_id: Optional[int] = Field(None, description="资源ID")
    description: str = Field(..., description="操作描述")
    ip_address: Optional[str] = Field(None, description="IP地址")
    created_at: datetime = Field(..., description="创建时间")
    status: str = Field(default="success", description="操作状态")
