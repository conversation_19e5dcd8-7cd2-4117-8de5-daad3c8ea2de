import openai
from typing import Optional
from app.core.config import settings
from app.models.datasource import DataSource, DatabaseMetadata
from app.core.database import get_db

class NL2SQLService:
    def __init__(self):
        self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def generate_sql(self, natural_query: str, data_source_id: int) -> str:
        # 获取数据源元数据
        db = next(get_db())
        metadata = self._get_metadata_context(db, data_source_id)
        
        # 构建提示词
        prompt = f"""
        基于以下数据库结构，将自然语言查询转换为SQL语句：
        
        数据库结构：
        {metadata}
        
        自然语言查询：{natural_query}
        
        请生成对应的SQL查询语句，只返回SQL语句，不要包含其他解释。
        """
        
        response = await self.client.chat.completions.create(
            model=settings.DEFAULT_LLM_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1
        )
        
        return response.choices[0].message.content.strip()
    
    def _get_metadata_context(self, db, data_source_id: int) -> str:
        # 获取数据源的表结构信息
        metadata = db.query(DatabaseMetadata).filter(
            DatabaseMetadata.data_source_id == data_source_id
        ).all()
        
        # 格式化为可读的结构描述
        context = ""
        current_table = None
        for meta in metadata:
            if meta.table_name != current_table:
                if current_table:
                    context += "\n"
                context += f"表 {meta.table_name}:\n"
                current_table = meta.table_name
            
            context += f"  - {meta.column_name} ({meta.column_type})"
            if meta.column_comment:
                context += f" - {meta.column_comment}"
            context += "\n"
        
        return context