"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Settings, Database, Shield, Bell, Palette, Save, RefreshCw } from "lucide-react"

export default function ApplicationSettings() {
  const { user } = useAuth()
  const [settings, setSettings] = useState({
    // General Settings
    appName: "NL2SQL 数据智能分析系统",
    appDescription: "自然语言转SQL的数据智能分析平台",
    defaultLanguage: "zh-CN",
    timezone: "Asia/Shanghai",

    // Query Settings
    maxQueryTimeout: 30,
    maxResultRows: 1000,
    enableQueryCache: true,
    cacheExpiration: 3600,

    // Security Settings
    enableAuditLog: true,
    sessionTimeout: 1800,
    maxLoginAttempts: 5,
    enableTwoFactor: false,

    // Notification Settings
    enableEmailNotifications: true,
    enableSystemAlerts: true,
    notificationEmail: "<EMAIL>",

    // UI Settings
    defaultTheme: "system",
    enableDarkMode: true,
    compactMode: false,
    showWelcomeMessage: true,
  })

  const [isSaving, setIsSaving] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  // 加载设置
  useEffect(() => {
    const loadSettings = async () => {
      if (!user) return

      try {
        setIsLoading(true)
        setError("")
        const apiClient = (await import("@/lib/api-client")).default
        const response = await apiClient.get('/settings/application')

        setSettings(response)
      } catch (error) {
        console.error('加载设置失败:', error)
        setError('加载设置失败，请稍后重试')
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [user])

  const handleSave = async () => {
    if (!user || user.role !== 'admin') {
      setError('只有管理员可以修改系统设置')
      return
    }

    try {
      setIsSaving(true)
      setError("")

      const apiClient = (await import("@/lib/api-client")).default
      const response = await apiClient.put('/settings/application', settings)

      if (response.success) {
        setSettings(response.data)
        // 可以添加成功提示
        console.log('设置保存成功')
      } else {
        setError(response.message || '保存设置失败')
      }
    } catch (error: any) {
      console.error('保存设置失败:', error)
      setError(error.message || '保存设置失败，请稍后重试')
    } finally {
      setIsSaving(false)
    }
  }

  const handleReset = async () => {
    if (!user || user.role !== 'admin') {
      setError('只有管理员可以重置系统设置')
      return
    }

    if (!confirm('确定要重置所有设置为默认值吗？此操作不可撤销。')) {
      return
    }

    try {
      setIsSaving(true)
      setError("")

      const apiClient = (await import("@/lib/api-client")).default
      const response = await apiClient.post('/settings/application/reset', {})

      if (response.success) {
        setSettings(response.data)
        console.log('设置已重置为默认值')
      } else {
        setError(response.message || '重置设置失败')
      }
    } catch (error: any) {
      console.error('重置设置失败:', error)
      setError(error.message || '重置设置失败，请稍后重试')
    } finally {
      setIsSaving(false)
    }
  }

  // 加载状态
  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">应用设置</h1>
            <p className="text-gray-600">管理系统的基本配置和功能设置</p>
          </div>
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-6 border border-gray-200 rounded-lg animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">应用设置</h1>
          <p className="text-gray-600">管理系统的基本配置和功能设置</p>
          {user?.role !== 'admin' && (
            <p className="text-amber-600 text-sm mt-1">
              注意：只有管理员可以修改系统设置
            </p>
          )}
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isSaving || user?.role !== 'admin'}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            重置
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || user?.role !== 'admin'}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? "保存中..." : "保存设置"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">常规</TabsTrigger>
          <TabsTrigger value="query">查询</TabsTrigger>
          <TabsTrigger value="security">安全</TabsTrigger>
          <TabsTrigger value="notifications">通知</TabsTrigger>
          <TabsTrigger value="appearance">外观</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                基本信息
              </CardTitle>
              <CardDescription>配置应用的基本信息和区域设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="appName">应用名称</Label>
                <Input
                  id="appName"
                  value={settings.appName}
                  onChange={(e) => setSettings({ ...settings, appName: e.target.value })}
                  disabled={user?.role !== 'admin'}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="appDescription">应用描述</Label>
                <Input
                  id="appDescription"
                  value={settings.appDescription}
                  onChange={(e) => setSettings({ ...settings, appDescription: e.target.value })}
                  disabled={user?.role !== 'admin'}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="language">默认语言</Label>
                  <Select
                    value={settings.defaultLanguage}
                    onValueChange={(value) => setSettings({ ...settings, defaultLanguage: value })}
                    disabled={user?.role !== 'admin'}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="zh-CN">简体中文</SelectItem>
                      <SelectItem value="en-US">English</SelectItem>
                      <SelectItem value="ja-JP">日本語</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timezone">时区</Label>
                  <Select
                    value={settings.timezone}
                    onValueChange={(value) => setSettings({ ...settings, timezone: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Asia/Shanghai">Asia/Shanghai</SelectItem>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">America/New_York</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="query" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                查询配置
              </CardTitle>
              <CardDescription>配置SQL查询的执行参数和缓存设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="queryTimeout">查询超时时间 (秒)</Label>
                  <Input
                    id="queryTimeout"
                    type="number"
                    value={settings.maxQueryTimeout}
                    onChange={(e) => setSettings({ ...settings, maxQueryTimeout: Number.parseInt(e.target.value) })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxRows">最大结果行数</Label>
                  <Input
                    id="maxRows"
                    type="number"
                    value={settings.maxResultRows}
                    onChange={(e) => setSettings({ ...settings, maxResultRows: Number.parseInt(e.target.value) })}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用查询缓存</Label>
                    <p className="text-sm text-gray-600">缓存查询结果以提高性能</p>
                  </div>
                  <Switch
                    checked={settings.enableQueryCache}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableQueryCache: checked })}
                  />
                </div>

                {settings.enableQueryCache && (
                  <div className="space-y-2">
                    <Label htmlFor="cacheExpiration">缓存过期时间 (秒)</Label>
                    <Input
                      id="cacheExpiration"
                      type="number"
                      value={settings.cacheExpiration}
                      onChange={(e) => setSettings({ ...settings, cacheExpiration: Number.parseInt(e.target.value) })}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                安全设置
              </CardTitle>
              <CardDescription>配置系统的安全策略和访问控制</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">会话超时时间 (秒)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={settings.sessionTimeout}
                    onChange={(e) => setSettings({ ...settings, sessionTimeout: Number.parseInt(e.target.value) })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxAttempts">最大登录尝试次数</Label>
                  <Input
                    id="maxAttempts"
                    type="number"
                    value={settings.maxLoginAttempts}
                    onChange={(e) => setSettings({ ...settings, maxLoginAttempts: Number.parseInt(e.target.value) })}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用审计日志</Label>
                    <p className="text-sm text-gray-600">记录用户操作和系统事件</p>
                  </div>
                  <Switch
                    checked={settings.enableAuditLog}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableAuditLog: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用双因素认证</Label>
                    <p className="text-sm text-gray-600">为用户账户添加额外的安全层</p>
                  </div>
                  <Switch
                    checked={settings.enableTwoFactor}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableTwoFactor: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                通知设置
              </CardTitle>
              <CardDescription>配置系统通知和邮件提醒</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="notificationEmail">通知邮箱</Label>
                <Input
                  id="notificationEmail"
                  type="email"
                  value={settings.notificationEmail}
                  onChange={(e) => setSettings({ ...settings, notificationEmail: e.target.value })}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用邮件通知</Label>
                    <p className="text-sm text-gray-600">发送重要事件的邮件通知</p>
                  </div>
                  <Switch
                    checked={settings.enableEmailNotifications}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableEmailNotifications: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用系统警报</Label>
                    <p className="text-sm text-gray-600">显示系统状态和错误警报</p>
                  </div>
                  <Switch
                    checked={settings.enableSystemAlerts}
                    onCheckedChange={(checked) => setSettings({ ...settings, enableSystemAlerts: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                外观设置
              </CardTitle>
              <CardDescription>自定义界面外观和用户体验</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="theme">默认主题</Label>
                <Select
                  value={settings.defaultTheme}
                  onValueChange={(value) => setSettings({ ...settings, defaultTheme: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">浅色</SelectItem>
                    <SelectItem value="dark">深色</SelectItem>
                    <SelectItem value="system">跟随系统</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="space-y-4">
                

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>紧凑模式</Label>
                    <p className="text-sm text-gray-600">使用更紧凑的界面布局</p>
                  </div>
                  <Switch
                    checked={settings.compactMode}
                    onCheckedChange={(checked) => setSettings({ ...settings, compactMode: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>显示欢迎消息</Label>
                    <p className="text-sm text-gray-600">在首页显示欢迎和帮助信息</p>
                  </div>
                  <Switch
                    checked={settings.showWelcomeMessage}
                    onCheckedChange={(checked) => setSettings({ ...settings, showWelcomeMessage: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
