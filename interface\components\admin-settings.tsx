"use client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import UserManagement from "@/components/user-management"
import RoleManagement from "@/components/role-management"
import DataSourceConfig from "@/components/data-source-config"
import LLMConfig from "@/components/llm-config"
import { Users, Shield, Database, Brain } from "lucide-react"

export default function AdminSettings() {
  const { user } = useAuth()

  // 权限检查
  if (!user || user.role !== 'admin') {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">访问受限</h1>
          <p className="text-gray-600">只有管理员可以访问系统管理功能</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div>
        <h1 className="text-2xl font-bold">系统管理</h1>
        <p className="text-gray-600">管理用户、权限、数据源和AI模型配置</p>
      </div>

      <Tabs defaultValue="users" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            用户管理
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            角色权限
          </TabsTrigger>
          <TabsTrigger value="datasources" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            数据源
          </TabsTrigger>
          <TabsTrigger value="llm" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI模型
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <UserManagement />
        </TabsContent>

        <TabsContent value="roles">
          <RoleManagement />
        </TabsContent>

        <TabsContent value="datasources">
          <DataSourceConfig />
        </TabsContent>

        <TabsContent value="llm">
          <LLMConfig />
        </TabsContent>
      </Tabs>
    </div>
  )
}
