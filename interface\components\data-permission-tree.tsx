"use client"

import { Checkbox } from "@/components/ui/checkbox"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronRight, ChevronDown, Database, Table, Server } from "lucide-react"
import { useState } from "react"
import { <PERSON>lt<PERSON>, TooltipContent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Re-define the structure for data sources, databases, and tables
interface Column {
  id: string
  name: string
  type: string
  description: string
}

interface TableNode {
  id: string
  name: string
  description: string
  columns: Column[] // Columns are not selectable for data permissions, but kept for structure
}

interface DatabaseNode {
  id: string
  name: string
  description: string
  tables: TableNode[]
}

interface DataSourceNode {
  id: string
  name: string
  description: string
  databases: DatabaseNode[]
}

// Mock metadata structure (copied from metadata-explorer for self-containment)
const mockMetadata: DataSourceNode[] = [
  {
    id: "ds1",
    name: "数据源A",
    description: "第一个数据源，包含销售和客户数据",
    databases: [
      {
        id: "db1",
        name: "销售数据库",
        description: "包含所有销售相关的数据表",
        tables: [
          {
            id: "table1",
            name: "sales_data",
            description: "销售交易明细表",
            columns: [], // Simplified for permission tree
          },
          {
            id: "table2",
            name: "products",
            description: "产品信息表",
            columns: [],
          },
        ],
      },
      {
        id: "db2",
        name: "客户数据库",
        description: "包含所有客户相关的数据表",
        tables: [
          {
            id: "table3",
            name: "customers",
            description: "客户信息表",
            columns: [],
          },
        ],
      },
    ],
  },
  {
    id: "ds2",
    name: "数据源B",
    description: "第二个数据源，包含订单数据",
    databases: [
      {
        id: "db3",
        name: "订单数据库",
        description: "包含订单信息",
        tables: [
          {
            id: "table4",
            name: "orders",
            description: "订单主表",
            columns: [],
          },
        ],
      },
    ],
  },
]

interface DataPermissionTreeProps {
  selectedDataPermissions: string[]
  onDataPermissionChange: (permissions: string[]) => void
}

export default function DataPermissionTree({
  selectedDataPermissions,
  onDataPermissionChange,
}: DataPermissionTreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<string[]>(
    mockMetadata.map((ds) => ds.id).concat(mockMetadata.flatMap((ds) => ds.databases.map((db) => db.id))),
  ) // Expand all by default

  const toggleNode = (nodeId: string) => {
    setExpandedNodes((prev) => (prev.includes(nodeId) ? prev.filter((id) => id !== nodeId) : [...prev, nodeId]))
  }

  const isNodeExpanded = (nodeId: string) => expandedNodes.includes(nodeId)

  const getAllChildIds = (node: DataSourceNode | DatabaseNode | TableNode): string[] => {
    let ids: string[] = []
    if ("databases" in node && node.databases) {
      node.databases.forEach((db) => {
        ids.push(db.id)
        ids = ids.concat(getAllChildIds(db))
      })
    } else if ("tables" in node && node.tables) {
      node.tables.forEach((table) => {
        ids.push(table.id)
        // Tables don't have children in this context, so no recursion needed for columns
      })
    }
    return ids
  }

  const handlePermissionToggle = (nodeId: string, node: DataSourceNode | DatabaseNode | TableNode) => {
    const newPermissions = new Set(selectedDataPermissions)
    const isCurrentlySelected = newPermissions.has(nodeId)
    const childIds = getAllChildIds(node)

    if (isCurrentlySelected) {
      // If node is selected, unselect it and all its children
      newPermissions.delete(nodeId)
      childIds.forEach((id) => newPermissions.delete(id))
    } else {
      // If node is not selected, select it and all its children
      newPermissions.add(nodeId)
      childIds.forEach((id) => newPermissions.add(id))
    }

    onDataPermissionChange(Array.from(newPermissions))
  }

  const isNodeSelected = (nodeId: string, node: DataSourceNode | DatabaseNode | TableNode) => {
    const childIds = getAllChildIds(node)
    const allChildrenSelected = childIds.every((id) => selectedDataPermissions.includes(id))
    return selectedDataPermissions.includes(nodeId) && allChildrenSelected
  }

  const isNodeIndeterminate = (nodeId: string, node: DataSourceNode | DatabaseNode | TableNode) => {
    const childIds = getAllChildIds(node)
    if (childIds.length === 0) return false // Leaf node
    const selectedChildren = childIds.filter((id) => selectedDataPermissions.includes(id))
    return selectedChildren.length > 0 && selectedChildren.length < childIds.length
  }

  const renderTable = (table: TableNode, level: number) => {
    const isSelected = selectedDataPermissions.includes(table.id)
    return (
      <div key={table.id} className={`flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 ml-${level * 6}`}>
        <div className="w-6" /> {/* Indent for icon alignment */}
        <Checkbox id={table.id} checked={isSelected} onCheckedChange={() => handlePermissionToggle(table.id, table)} />
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <label htmlFor={table.id} className="flex items-center space-x-2 flex-1 cursor-pointer">
                <Table className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">{table.name}</span>
              </label>
            </TooltipTrigger>
            <TooltipContent side="right">{table.description}</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    )
  }

  const renderDatabase = (database: DatabaseNode, level: number) => {
    const isExpanded = isNodeExpanded(database.id)
    const isSelected = isNodeSelected(database.id, database)
    const isIndeterminate = isNodeIndeterminate(database.id, database)

    return (
      <div key={database.id} className="space-y-1">
        <div className={`flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 ml-${level * 6}`}>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => toggleNode(database.id)}>
            {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
          </Button>
          <Checkbox
            id={database.id}
            checked={isSelected}
            onCheckedChange={() => handlePermissionToggle(database.id, database)}
            className={isIndeterminate ? "data-[state=checked]:bg-blue-600" : ""}
          />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <label htmlFor={database.id} className="flex items-center space-x-2 flex-1 cursor-pointer">
                  <Database className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">{database.name}</span>
                </label>
              </TooltipTrigger>
              <TooltipContent side="right">{database.description}</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {isExpanded && <div className="space-y-1">{database.tables.map((table) => renderTable(table, level + 1))}</div>}
      </div>
    )
  }

  const renderDataSource = (dataSource: DataSourceNode, level: number) => {
    const isExpanded = isNodeExpanded(dataSource.id)
    const isSelected = isNodeSelected(dataSource.id, dataSource)
    const isIndeterminate = isNodeIndeterminate(dataSource.id, dataSource)

    return (
      <div key={dataSource.id} className="space-y-1">
        <div className={`flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 ml-${level * 6}`}>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => toggleNode(dataSource.id)}>
            {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
          </Button>
          <Checkbox
            id={dataSource.id}
            checked={isSelected}
            onCheckedChange={() => handlePermissionToggle(dataSource.id, dataSource)}
            className={isIndeterminate ? "data-[state=checked]:bg-blue-600" : ""}
          />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <label htmlFor={dataSource.id} className="flex items-center space-x-2 flex-1 cursor-pointer">
                  <Server className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium">{dataSource.name}</span>
                </label>
              </TooltipTrigger>
              <TooltipContent side="right">{dataSource.description}</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {isExpanded && (
          <div className="space-y-1">{dataSource.databases.map((db) => renderDatabase(db, level + 1))}</div>
        )}
      </div>
    )
  }

  const handleSelectAll = () => {
    const allIds: string[] = []
    mockMetadata.forEach((ds) => {
      allIds.push(ds.id)
      ds.databases.forEach((db) => {
        allIds.push(db.id)
        db.tables.forEach((table) => {
          allIds.push(table.id)
        })
      })
    })
    onDataPermissionChange(allIds)
  }

  const handleClearAll = () => {
    onDataPermissionChange([])
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">数据权限</h3>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={handleClearAll}>
            清除全部
          </Button>
          <Button variant="outline" size="sm" onClick={handleSelectAll}>
            选择全部
          </Button>
        </div>
      </div>

      <div className="border rounded-lg p-4 max-h-96 overflow-y-auto">
        {mockMetadata.map((dataSource) => renderDataSource(dataSource, 0))}
      </div>
    </div>
  )
}
