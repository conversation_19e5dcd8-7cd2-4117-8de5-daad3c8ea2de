from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from datetime import datetime, timedelta
from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.query import QueryHistory
from app.models.datasource import DataSource

router = APIRouter()

@router.get("/dashboard")
async def get_dashboard_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取仪表板统计数据"""
    # 总查询数
    total_queries = db.query(QueryHistory).count()
    
    # 今日查询数
    today = datetime.now().date()
    today_queries = db.query(QueryHistory).filter(
        func.date(QueryHistory.created_at) == today
    ).count()
    
    # 成功率
    success_queries = db.query(QueryHistory).filter(
        QueryHistory.execution_status == "success"
    ).count()
    success_rate = (success_queries / total_queries * 100) if total_queries > 0 else 0
    
    # 活跃用户数
    active_users = db.query(User).filter(User.is_active == True).count()
    
    # 数据源数量
    data_sources = db.query(DataSource).count()
    
    # 最近7天查询趋势
    seven_days_ago = datetime.now() - timedelta(days=7)
    daily_queries = db.query(
        func.date(QueryHistory.created_at).label('date'),
        func.count(QueryHistory.id).label('count')
    ).filter(
        QueryHistory.created_at >= seven_days_ago
    ).group_by(
        func.date(QueryHistory.created_at)
    ).order_by('date').all()
    
    # 热门查询类型
    popular_queries = db.query(
        QueryHistory.natural_query,
        func.count(QueryHistory.id).label('count')
    ).filter(
        QueryHistory.natural_query != ""
    ).group_by(
        QueryHistory.natural_query
    ).order_by(desc('count')).limit(10).all()
    
    return {
        "total_queries": total_queries,
        "today_queries": today_queries,
        "success_rate": round(success_rate, 2),
        "active_users": active_users,
        "data_sources": data_sources,
        "daily_trend": [{"date": str(item.date), "count": item.count} for item in daily_queries],
        "popular_queries": [{"query": item.natural_query, "count": item.count} for item in popular_queries]
    }

@router.get("/user-stats")
async def get_user_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户个人统计"""
    # 用户查询总数
    user_queries = db.query(QueryHistory).filter(
        QueryHistory.user_id == current_user.id
    ).count()
    
    # 用户成功查询数
    user_success = db.query(QueryHistory).filter(
        QueryHistory.user_id == current_user.id,
        QueryHistory.execution_status == "success"
    ).count()
    
    # 用户收藏数
    from app.models.query import Favorite
    user_favorites = db.query(Favorite).filter(
        Favorite.user_id == current_user.id
    ).count()
    
    # 最近查询
    recent_queries = db.query(QueryHistory).filter(
        QueryHistory.user_id == current_user.id
    ).order_by(desc(QueryHistory.created_at)).limit(5).all()
    
    return {
        "total_queries": user_queries,
        "success_queries": user_success,
        "success_rate": round((user_success / user_queries * 100) if user_queries > 0 else 0, 2),
        "favorites_count": user_favorites,
        "recent_queries": [
            {
                "id": q.id,
                "natural_query": q.natural_query,
                "status": q.execution_status,
                "created_at": q.created_at
            } for q in recent_queries
        ]
    }