"use client"

import { useAuth } from "@/contexts/auth-context"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User, Shield, BarChart3 } from "lucide-react"

export default function UserStatus() {
  const { user, isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 animate-pulse">
        <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
        <div className="h-4 w-20 bg-gray-200 rounded"></div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return null
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-3 w-3" />
      case 'analyst':
        return <BarChart3 className="h-3 w-3" />
      default:
        return <User className="h-3 w-3" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800'
      case 'analyst':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return '管理员'
      case 'analyst':
        return '分析师'
      default:
        return '用户'
    }
  }

  return (
    <div className="flex items-center space-x-2">
      <Avatar className="h-8 w-8">
        <AvatarImage src={user.avatar} alt={user.name || user.username} />
        <AvatarFallback>
          {(user.name || user.username).charAt(0).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      <div className="flex flex-col">
        <span className="text-sm font-medium text-gray-900">
          {user.name || user.username}
        </span>
        <Badge 
          variant="secondary" 
          className={`text-xs ${getRoleColor(user.role)} flex items-center space-x-1`}
        >
          {getRoleIcon(user.role)}
          <span>{getRoleLabel(user.role)}</span>
        </Badge>
      </div>
    </div>
  )
}

// 简化版本的用户状态组件
export function UserStatusCompact() {
  const { user, isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <div className="h-6 w-16 bg-gray-200 rounded animate-pulse"></div>
  }

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <div className="flex items-center space-x-1">
      <Avatar className="h-6 w-6">
        <AvatarImage src={user.avatar} alt={user.name || user.username} />
        <AvatarFallback className="text-xs">
          {(user.name || user.username).charAt(0).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      <span className="text-sm text-gray-700">
        {user.name || user.username}
      </span>
    </div>
  )
}

// 仅显示用户角色的组件
export function UserRole() {
  const { user, isAuthenticated } = useAuth()

  if (!isAuthenticated || !user) {
    return null
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800'
      case 'analyst':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return '管理员'
      case 'analyst':
        return '分析师'
      default:
        return '用户'
    }
  }

  return (
    <Badge variant="secondary" className={`text-xs ${getRoleColor(user.role)}`}>
      {getRoleLabel(user.role)}
    </Badge>
  )
}
