from fastapi import APIRouter
from app.api.v1.endpoints import auth, users, query, datasource, llm, favorites, analytics, system_settings, roles, metadata

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(query.router, prefix="/query", tags=["查询"])
api_router.include_router(datasource.router, prefix="/datasources", tags=["数据源"])
api_router.include_router(llm.router, prefix="/llm", tags=["LLM配置"])
api_router.include_router(favorites.router, prefix="/favorites", tags=["收藏夹"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["统计分析"])
api_router.include_router(system_settings.router, prefix="/settings", tags=["系统设置"])
api_router.include_router(roles.router, prefix="/roles", tags=["角色管理"])
api_router.include_router(metadata.router, prefix="/metadata", tags=["元数据管理"])
