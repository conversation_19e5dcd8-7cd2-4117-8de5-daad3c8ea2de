#!/usr/bin/env python3
"""
API测试脚本
用于验证后端API是否正常工作
"""

import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://127.0.0.1:8000/api/v1"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def test_login():
    """测试登录"""
    print("=== 测试登录 ===")
    
    # 准备登录数据
    login_data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print(f"登录成功，获取到token: {token[:20]}...")
            return token
        else:
            print(f"登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api_with_token(token, endpoint, description):
    """使用token测试API端点"""
    print(f"\n=== 测试 {description} ===")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"请求失败: {response.text}")
            return False
    except Exception as e:
        print(f"请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始API测试...")
    print(f"测试时间: {datetime.now()}")
    print(f"后端地址: {BASE_URL}")
    
    # 1. 测试登录
    token = test_login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    # 2. 测试各个API端点
    test_cases = [
        ("/users/me", "获取当前用户信息"),
        ("/favorites/", "获取收藏夹"),
        ("/datasources/", "获取数据源列表"),
        ("/metadata/datasources", "获取元数据"),
        ("/users/", "获取用户列表"),
        ("/roles/", "获取角色列表"),
    ]
    
    results = {}
    for endpoint, description in test_cases:
        success = test_api_with_token(token, endpoint, description)
        results[endpoint] = success
    
    # 3. 输出测试结果总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    for endpoint, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{endpoint:<25} {status}")
    
    success_count = sum(results.values())
    total_count = len(results)
    print(f"\n总计: {success_count}/{total_count} 个API测试通过")
    
    if success_count == total_count:
        print("🎉 所有API测试都通过了！")
    else:
        print("⚠️  有API测试失败，请检查后端服务")

if __name__ == "__main__":
    main()
