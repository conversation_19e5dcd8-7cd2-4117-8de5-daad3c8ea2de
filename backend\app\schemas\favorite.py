from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class FavoriteCreate(BaseModel):
    query_id: int
    name: str
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = False

class FavoriteUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None

class FavoriteResponse(BaseModel):
    id: int
    user_id: int
    query_id: int
    name: str
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    is_public: bool
    created_at: datetime
    
    class Config:
        from_attributes = True