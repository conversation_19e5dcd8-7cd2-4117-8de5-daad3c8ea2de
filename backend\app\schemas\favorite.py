from pydantic import BaseModel, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime

class FavoriteCreate(BaseModel):
    query_id: int
    name: str
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = False

class FavoriteUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None

class FavoriteResponse(BaseModel):
    id: int
    user_id: int
    query_id: int
    name: str
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    is_public: bool
    created_at: datetime

    @field_validator('tags', mode='before')
    @classmethod
    def validate_tags(cls, v):
        """处理tags字段的JSON序列化"""
        if v is None:
            return []
        if isinstance(v, list):
            return v
        if isinstance(v, str):
            try:
                import json
                return json.loads(v)
            except:
                return []
        return []

    class Config:
        from_attributes = True