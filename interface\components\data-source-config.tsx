"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import apiClient from "@/lib/api-client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { FolderSync, Plus, Edit, Trash2, TestTube, TestTubeDiagonal, CheckCircle, XCircle, AlertCircle, Eye, EyeOff } from "lucide-react"
import { Tooltip } from 'react-tooltip'

interface DataSource {
  id: string
  name: string
  type: "doris" | "mysql" | "postgresql" | "oracle" | "sqlserver" | "sqlite"
  host: string
  port: number
  database: string
  username: string
  password: string
  status: "connected" | "disconnected" | "error"
  description?: string
  cronJob?: string
  createdAt: string
  updatedAt: string
  lastTested?: string
  lastSync?: string
}

const mockDataSources: DataSource[] = [
  {
    id: "1",
    name: "生产数据库",
    type: "mysql",
    host: "prod-db.example.com",
    port: 3306,
    database: "production",
    username: "app_user",
    password: "encrypted_password",
    status: "connected",
    description: "生产环境主数据库",
    cronJob: "0 0 * * *",
    createdAt: "2024-01-01",
    updatedAt: "2024-01-15",
    lastTested: "2024-01-15 10:30:00",
    lastSync: "2024-01-15 10:30:00",
  },
  {
    id: "2",
    name: "测试数据库",
    type: "postgresql",
    host: "test-db.example.com",
    port: 5432,
    database: "testdb",
    username: "test_user",
    password: "encrypted_password",
    status: "connected",
    description: "测试环境数据库",
    cronJob: "0 0 * * *",
    createdAt: "2024-01-02",
    updatedAt: "2024-01-10",
    lastTested: "2024-01-14 15:20:00",
    lastSync: "2024-01-14 15:20:00",
  },
  {
    id: "3",
    name: "数据仓库",
    type: "doris",
    host: "dws.example.com",
    port: 9030,
    database: "DWPROD",
    username: "dw_user",
    password: "encrypted_password",
    status: "error",
    description: "数据仓库Doris",
    cronJob: "0 0 * * *",
    createdAt: "2024-01-03",
    updatedAt: "2024-01-12",
    lastTested: "2024-01-12 09:45:00",
    lastSync: "2024-01-14 15:20:00",
  },
]

const databaseTypes = [
  { value: "doris", label: "Doris" },
  { value: "mysql", label: "MySQL" },
  { value: "postgresql", label: "PostgreSQL" },
  { value: "oracle", label: "Oracle" },
  { value: "sqlserver", label: "SQL Server" },
  { value: "sqlite", label: "SQLite" },
]

export default function DataSourceConfig() {
  const { user } = useAuth()
  const [dataSources, setDataSources] = useState<DataSource[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedDataSource, setSelectedDataSource] = useState<DataSource | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)
  const [isTesting, setIsTesting] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    type: "mysql" as DataSource["type"],
    host: "",
    port: 3306,
    database: "",
    username: "",
    password: "",
    description: "",
  })

  // 加载数据源数据
  useEffect(() => {
    const loadDataSources = async () => {
      if (!user || user.role !== 'admin') return

      try {
        setIsLoading(true)
        setError("")
        const response = await apiClient.get('/datasources/')

        // 转换后端数据格式为前端格式
        const transformedDataSources = response.map((ds: any) => ({
          id: ds.id.toString(),
          name: ds.name,
          type: ds.db_type,
          host: ds.host,
          port: ds.port,
          database: ds.database_name,
          username: ds.username,
          password: "******", // 不显示真实密码
          status: ds.is_active ? "connected" : "disconnected",
          description: ds.description || "",
          createdAt: new Date(ds.created_at).toISOString().split('T')[0],
          updatedAt: new Date(ds.updated_at).toISOString().split('T')[0],
        }))

        setDataSources(transformedDataSources)
      } catch (error: any) {
        console.error('加载数据源失败:', error)
        setError('加载数据源失败，使用模拟数据')
        // 如果API调用失败，使用模拟数据作为后备
        setDataSources(mockDataSources)
      } finally {
        setIsLoading(false)
      }
    }

    loadDataSources()
  }, [user])

  const handleCreateDataSource = () => {
    const newDataSource: DataSource = {
      id: Date.now().toString(),
      ...formData,
      status: "disconnected",
      createdAt: new Date().toISOString().split("T")[0],
      updatedAt: new Date().toISOString().split("T")[0],
    }

    setDataSources([...dataSources, newDataSource])
    setIsCreateDialogOpen(false)
    resetForm()
  }

  const handleEditDataSource = () => {
    if (!selectedDataSource) return

    const updatedDataSources = dataSources.map((ds) =>
      ds.id === selectedDataSource.id
        ? {
            ...ds,
            ...formData,
            updatedAt: new Date().toISOString().split("T")[0],
          }
        : ds,
    )

    setDataSources(updatedDataSources)
    setIsEditDialogOpen(false)
    resetForm()
  }

  const handleDeleteDataSource = (id: string) => {
    if (confirm("确定要删除这个数据源吗？")) {
      setDataSources(dataSources.filter((ds) => ds.id !== id))
    }
  }

  const handleTestConnection = async (dataSource?: DataSource) => {
    setIsTesting(true)
    setTestResult(null)

    // Simulate connection test
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const success = Math.random() > 0.3 // 70% success rate for demo
    setTestResult({
      success,
      message: success ? "连接测试成功！数据库连接正常。" : "连接测试失败：无法连接到数据库，请检查配置信息。",
    })

    if (dataSource && success) {
      // Update the data source status
      const updatedDataSources = dataSources.map((ds) =>
        ds.id === dataSource.id
          ? {
              ...ds,
              status: "connected" as const,
              lastTested: new Date().toLocaleString(),
            }
          : ds,
      )
      setDataSources(updatedDataSources)
    }

    setIsTesting(false)
  }

  const openEditDialog = (dataSource: DataSource) => {
    setSelectedDataSource(dataSource)
    setFormData({
      name: dataSource.name,
      type: dataSource.type,
      host: dataSource.host,
      port: dataSource.port,
      database: dataSource.database,
      username: dataSource.username,
      password: dataSource.password,
      description: dataSource.description || "",
    })
    setIsEditDialogOpen(true)
  }

  const resetForm = () => {
    setFormData({
      name: "",
      type: "mysql",
      host: "",
      port: 3306,
      database: "",
      username: "",
      password: "",
      description: "",
    })
    setSelectedDataSource(null)
    setTestResult(null)
    setShowPassword(false)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "connected":
        return <Badge className="bg-green-100 text-green-800">已连接</Badge>
      case "disconnected":
        return <Badge variant="secondary">未连接</Badge>
      case "error":
        return <Badge variant="destructive">连接错误</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "connected":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "disconnected":
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return null
    }
  }

  const getDefaultPort = (type: string) => {
    switch (type) {
      case "doris":
        return 9030
      case "mysql":
        return 3306
      case "postgresql":
        return 5432
      case "oracle":
        return 1521
      case "sqlserver":
        return 1433
      case "sqlite":
        return 0
      default:
        return 3306
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">数据源配置</h2>
          <p className="text-gray-600">管理数据库连接和配置信息</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => resetForm()}>
              <Plus className="h-4 w-4 mr-2" />
              新建数据源
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>创建数据源</DialogTitle>
              <DialogDescription>配置新的数据库连接信息</DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="basic" className="space-y-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="basic">基本信息</TabsTrigger>
                <TabsTrigger value="connection">连接配置</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">数据源名称</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="输入数据源名称"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">数据库类型</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value: DataSource["type"]) => {
                      setFormData({
                        ...formData,
                        type: value,
                        port: getDefaultPort(value),
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {databaseTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="输入数据源描述（可选）"
                  />
                </div>
              </TabsContent>

              <TabsContent value="connection" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="host">主机地址</Label>
                    <Input
                      id="host"
                      value={formData.host}
                      onChange={(e) => setFormData({ ...formData, host: e.target.value })}
                      placeholder="localhost"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="port">端口</Label>
                    <Input
                      id="port"
                      type="number"
                      value={formData.port}
                      onChange={(e) => setFormData({ ...formData, port: Number.parseInt(e.target.value) })}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="database">数据库名</Label>
                  <Input
                    id="database"
                    value={formData.database}
                    onChange={(e) => setFormData({ ...formData, database: e.target.value })}
                    placeholder="输入数据库名称"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="username">用户名</Label>
                    <Input
                      id="username"
                      value={formData.username}
                      onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                      placeholder="输入用户名"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">密码</Label>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                        placeholder="输入密码"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {testResult && (
              <div
                className={`p-3 rounded-lg border text-sm ${testResult.success ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"}`}
              >
                <div className="flex items-center gap-2">
                  {testResult.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                  <span>{testResult.message}</span>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2 mt-4">
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                取消
              </Button>
              <Button
                // type="button"
                // variant="outline"
                onClick={() => handleTestConnection()}
                disabled={
                  isTesting ||
                  !formData.name.trim() ||
                  !formData.host.trim() ||
                  !formData.database.trim() ||
                  !formData.username.trim() ||
                  !formData.password.trim()
                }
              >
                {isTesting ? "测试中..." : "测试连接"}
              </Button>
              <Button
                onClick={handleCreateDataSource}
                disabled={
                  !formData.name.trim() ||
                  !formData.host.trim() ||
                  !formData.database.trim() ||
                  !formData.username.trim() ||
                  !formData.password.trim()
                }
              >
                创建数据源
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            数据源列表
          </CardTitle>
          <CardDescription>系统中的所有数据源连接</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>数据源</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>同步作业Cron</TableHead>
                <TableHead>最近同步时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dataSources.map((dataSource) => (
                <TableRow key={dataSource.id}>
                  <TableCell>
                      <div className="text-sm font-medium text-gray-900">{dataSource.name}</div>
                      <div className="text-sm text-gray-500">{dataSource.description}</div>

                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-gray-900">{dataSource.type}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(dataSource.status)}
                      {getStatusBadge(dataSource.status)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-500">{dataSource.cronJob}</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-500">{dataSource.lastSync ? new Date(dataSource.lastSync).toLocaleString() : "N/A"}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" data-tooltip-id="tooltip-test" data-tooltip-content="测试连接" onClick={() => handleTestConnection(dataSource)}>
                          <TestTubeDiagonal className="h-4 w-4" />
                          <Tooltip id="tooltip-test" />
                      </Button>
                      <Button variant="outline" size="sm" data-tooltip-id="tooltip-sync" data-tooltip-content="元数据同步" onClick={() => handleTestConnection(dataSource)}>
                          <FolderSync className="h-4 w-4" />
                          <Tooltip id="tooltip-sync" />
                      </Button>
                      <Button variant="outline" size="sm" data-tooltip-id="tooltip-edit" data-tooltip-content="编辑" onClick={() => openEditDialog(dataSource)}>
                          <Edit className="h-4 w-4" />
                          <Tooltip id="tooltip-edit" />
                      </Button>
                      <Button variant="outline" size="sm" data-tooltip-id="tooltip-trash" data-tooltip-content="删除" onClick={() => handleDeleteDataSource(dataSource.id)}>
                        <Trash2 className="h-4 w-4" />
                        <Tooltip id="tooltip-trash" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑数据源</DialogTitle>
            <DialogDescription>修改数据库连接信息</DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="basic" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="connection">连接配置</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">数据源名称</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="输入数据源名称"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">数据库类型</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: DataSource["type"]) => {
                    setFormData({
                      ...formData,
                      type: value,
                      port: getDefaultPort(value),
                    })
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {databaseTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="输入数据源描述（可选）"
                />
              </div>
            </TabsContent>

            <TabsContent value="connection" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="host">主机地址</Label>
                  <Input
                    id="host"
                    value={formData.host}
                    onChange={(e) => setFormData({ ...formData, host: e.target.value })}
                    placeholder="localhost"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="port">端口</Label>
                  <Input
                    id="port"
                    type="number"
                    value={formData.port}
                    onChange={(e) => setFormData({ ...formData, port: Number.parseInt(e.target.value) })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="database">数据库名</Label>
                <Input
                  id="database"
                  value={formData.database}
                  onChange={(e) => setFormData({ ...formData, database: e.target.value })}
                  placeholder="输入数据库名称"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="username">用户名</Label>
                  <Input
                    id="username"
                    value={formData.username}
                    onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                    placeholder="输入用户名"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">密码</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      placeholder="输入密码"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {testResult && (
            <div
              className={`p-3 rounded-lg border text-sm ${testResult.success ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"}`}
            >
              <div className="flex items-center gap-2">
                {testResult.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                <span>{testResult.message}</span>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-2 mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleTestConnection()}
              disabled={isTesting || !formData.host || !formData.database}
            >
              <TestTube className="h-4 w-4 mr-2" />
              {isTesting ? "测试中..." : "测试连接"}
            </Button>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={handleEditDataSource}
              disabled={
                !formData.name.trim() ||
                !formData.host.trim() ||
                !formData.database.trim() ||
                !formData.username.trim() ||
                !formData.password.trim()
              }
            >
              保存
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
