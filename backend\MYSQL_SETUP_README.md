# NL2SQL MySQL 数据库配置指南

本文档说明如何将 NL2SQL 系统配置为使用 MySQL 数据库。

## 🎯 概述

NL2SQL 系统现在支持多种数据库类型，包括：
- **MySQL** (推荐用于生产环境)
- **PostgreSQL**
- **SQLite** (适用于开发和测试)

## 🚀 快速开始

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 配置数据库

复制配置模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置 MySQL 连接信息：
```env
# 数据库配置
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=nl2sql_system
DB_CHARSET=utf8mb4
```

### 3. 验证配置

运行配置验证脚本：
```bash
python verify_database_config.py
```

### 4. 启动应用

```bash
python main.py
```

## 📋 详细配置说明

### 配置方式

系统支持两种数据库配置方式：

#### 方式1：使用 DATABASE_URL（优先级更高）
```env
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/nl2sql_system?charset=utf8mb4
```

#### 方式2：使用分离的配置参数
```env
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=username
DB_PASSWORD=password
DB_NAME=nl2sql_system
DB_CHARSET=utf8mb4
```

### 连接池配置

```env
# 数据库连接池配置
DB_POOL_SIZE=5          # 连接池大小
DB_MAX_OVERFLOW=10      # 最大溢出连接数
DB_POOL_TIMEOUT=30      # 连接池超时时间（秒）
DB_POOL_RECYCLE=1800    # 连接回收时间（秒）
```

## 🗄️ 数据库设置

### MySQL 安装

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### CentOS/RHEL
```bash
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

#### macOS
```bash
brew install mysql
brew services start mysql
```

### 创建数据库和用户

```sql
-- 登录 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE nl2sql_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'nl2sql_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- 授予权限
GRANT ALL PRIVILEGES ON nl2sql_system.* TO 'nl2sql_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
```

### 初始化数据库结构

系统提供了完整的 MySQL DDL 脚本：

```bash
# 执行 DDL 脚本创建表结构
mysql -u nl2sql_user -p nl2sql_system < database/mysql_ddl.sql

# 执行初始化数据脚本（可选）
mysql -u nl2sql_user -p nl2sql_system < database/init_data.sql
```

## 🔧 配置文件说明

### 主要配置文件

| 文件 | 说明 |
|------|------|
| `.env` | 环境配置文件（用户配置） |
| `.env.example` | 配置模板文件 |
| `app/core/config.py` | 配置类定义 |
| `app/core/database.py` | 数据库连接管理 |

### 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `DB_TYPE` | 数据库类型 | `mysql` |
| `DB_HOST` | 数据库主机 | `localhost` |
| `DB_PORT` | 数据库端口 | `3306` |
| `DB_USER` | 数据库用户名 | `root` |
| `DB_PASSWORD` | 数据库密码 | - |
| `DB_NAME` | 数据库名称 | `nl2sql_system` |
| `DB_CHARSET` | 字符集 | `utf8mb4` |

## 🛠️ 工具和脚本

### 配置验证脚本

`verify_database_config.py` - 验证数据库配置是否正确

```bash
python verify_database_config.py
```

功能：
- ✅ 检查配置项完整性
- ✅ 测试数据库服务器连接
- ✅ 验证数据库创建权限
- ✅ 测试应用程序连接

### 数据库脚本

| 脚本 | 说明 |
|------|------|
| `database/mysql_ddl.sql` | MySQL 表结构定义 |
| `database/init_data.sql` | 初始化数据 |
| `database/database_config_example.py` | Python 配置示例 |

## 📚 迁移指南

如果您需要从 SQLite 迁移到 MySQL，请参考：
- [DATABASE_MIGRATION_GUIDE.md](DATABASE_MIGRATION_GUIDE.md)

## 🔍 故障排除

### 常见问题

#### 1. 连接被拒绝
```
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server")
```

**解决方案：**
- 检查 MySQL 服务是否运行
- 验证主机地址和端口
- 检查防火墙设置

#### 2. 认证失败
```
pymysql.err.OperationalError: (1045, "Access denied for user")
```

**解决方案：**
- 检查用户名和密码
- 确认用户权限
- 验证主机名配置

#### 3. 数据库不存在
```
pymysql.err.OperationalError: (1049, "Unknown database")
```

**解决方案：**
- 创建数据库
- 检查数据库名称拼写
- 确认用户有访问权限

### 调试模式

在 `.env` 文件中设置：
```env
DEBUG=true
```

这将在控制台显示 SQL 执行日志，有助于调试。

## 🔒 安全建议

1. **使用强密码**：为数据库用户设置复杂密码
2. **限制权限**：只授予必要的数据库权限
3. **网络安全**：在生产环境中限制数据库访问
4. **定期备份**：建立数据库备份策略
5. **监控日志**：定期检查数据库和应用日志

## 📈 性能优化

### 连接池调优

根据应用负载调整连接池参数：

```env
# 高负载环境
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=60
DB_POOL_RECYCLE=3600
```

### MySQL 配置优化

编辑 `/etc/mysql/my.cnf`：

```ini
[mysqld]
max_connections = 200
innodb_buffer_pool_size = 1G
query_cache_type = 1
query_cache_size = 64M
```

## 📞 支持

如果您在配置过程中遇到问题：

1. 运行 `python verify_database_config.py` 进行诊断
2. 检查应用程序日志
3. 查看 MySQL 错误日志
4. 参考故障排除部分
5. 联系技术支持团队

---

**注意**：在生产环境中部署之前，请务必在测试环境中验证所有配置。
