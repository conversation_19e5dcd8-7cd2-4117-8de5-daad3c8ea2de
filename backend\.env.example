# NL2SQL 系统环境配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置

# ===========================================
# 数据库配置
# ===========================================

# 数据库配置方式1：直接使用数据库URL（优先级更高）
# 如果设置了 DATABASE_URL，将忽略下面的分离配置参数
# DATABASE_URL=mysql+pymysql://username:password@localhost:3306/nl2sql_system?charset=utf8mb4
# DATABASE_URL=postgresql://username:password@localhost:5432/nl2sql_system
# DATABASE_URL=sqlite:///./test.db

# 数据库配置方式2：使用分离的数据库配置参数
# 支持的数据库类型：mysql, postgresql, sqlite
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password_here
DB_NAME=nl2sql_system
DB_CHARSET=utf8mb4

# 数据库连接池配置
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800

# ===========================================
# 应用程序配置
# ===========================================

# 应用基本信息
APP_NAME=NL2SQL 数据智能分析系统
APP_VERSION=1.0.0
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000

# API配置
API_V1_STR=/api/v1

# ===========================================
# 安全配置
# ===========================================

# JWT配置
# 请生成一个安全的密钥，可以使用以下命令生成：
# openssl rand -hex 32
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# 跨域配置
# ===========================================

# CORS允许的源地址
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:3001","http://localhost:3002"]

# ===========================================
# Redis配置
# ===========================================

# Redis连接URL
REDIS_URL=redis://localhost:6379/0

# ===========================================
# LLM配置
# ===========================================

# 默认LLM提供商和模型
DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_MODEL=gpt-3.5-turbo

# ===========================================
# 配置说明
# ===========================================

# MySQL配置示例：
# DB_TYPE=mysql
# DB_HOST=localhost
# DB_PORT=3306
# DB_USER=nl2sql_user
# DB_PASSWORD=your_secure_password
# DB_NAME=nl2sql_system
# DB_CHARSET=utf8mb4

# PostgreSQL配置示例：
# DB_TYPE=postgresql
# DB_HOST=localhost
# DB_PORT=5432
# DB_USER=nl2sql_user
# DB_PASSWORD=your_secure_password
# DB_NAME=nl2sql_system

# SQLite配置示例（用于开发测试）：
# DB_TYPE=sqlite
# DB_NAME=./test.db

# 注意事项：
# 1. 请确保数据库服务器已启动并可访问
# 2. 请确保数据库用户有足够的权限创建表和执行查询
# 3. 对于生产环境，请使用强密码并限制数据库访问权限
# 4. SECRET_KEY 必须是一个安全的随机字符串
# 5. 在生产环境中，请将 DEBUG 设置为 false
