"use client"

import Link from "next/link"
import { useState, useEffect } from "react" // Explicitly import React
import { useRouter } from "next/navigation"
import { HelpCircle, User, Settings, LogOut, FileText } from "lucide-react" // Removed Moon, Sun
import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
// Removed useTheme, Switch, Label imports

export default function MainHeader() {
  const router = useRouter()
  const { user, logout, isAuthenticated, isLoading } = useAuth()
  const [mounted, setMounted] = useState(false) // New mounted state

  // useEffect only runs on the client, so now we can safely show the UI
  useEffect(() => {
    setMounted(true)
  }, [])

  const handleLogout = () => {
    logout()
  }

  // 如果未挂载或正在加载，不显示用户相关内容
  if (!mounted || isLoading) {
    return (
      <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200 bg-white fixed top-0 left-0 right-0 z-50">
        <div className="flex items-center">
          <Link href="/" className="text-lg font-semibold text-gray-800 cursor-pointer">
            <h1>NL2SQL 数据智能分析系统</h1>
          </Link>
        </div>
        <div className="flex items-center space-x-2">
          {/* 加载状态时显示简化的header */}
        </div>
      </div>
    )
  }

  // 如果未认证，显示简化的header
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200 bg-white fixed top-0 left-0 right-0 z-50">
        <div className="flex items-center">
          <Link href="/" className="text-lg font-semibold text-gray-800 cursor-pointer">
            <h1>NL2SQL 数据智能分析系统</h1>
          </Link>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href="/login">登录</Link>
          </Button>
        </div>
      </div>
    )
  }

  const isAdmin = user?.role === 'admin'
  const isAnalyst = user?.role === 'analyst' || isAdmin

  return (
    <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200 bg-white fixed top-0 left-0 right-0 z-50">
      <div className="flex items-center">
        <Link href="/" className="text-lg font-semibold text-gray-800 cursor-pointer">
          <h1>NL2SQL 数据智能分析系统</h1>
        </Link>
      </div>
      <div className="flex items-center space-x-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" asChild>
                <Link href="/">
                  <FileText className="h-5 w-5" />
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>新建查询</TooltipContent>
          </Tooltip>
        </TooltipProvider>
        {isAnalyst && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                {/* Reverting Link usage to use asChild on Button */}
                <Button variant="ghost" size="icon" asChild>
                  <Link href="/docs">
                    <HelpCircle className="h-5 w-5" />
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent>帮助文档</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {/* Theme Toggle Switch - Removed */}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <User className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <div className="px-2 py-1.5 text-sm text-gray-600">
              <div className="font-medium">{user?.name || user?.username}</div>
              <div className="text-xs text-gray-500">{user?.email}</div>
              <div className="text-xs text-gray-500 capitalize">{user?.role}</div>
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/profile">
                <User className="mr-2 h-4 w-4" />
                <span>个人中心</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/settings">
                <Settings className="mr-2 h-4 w-4" />
                <span>应用设置</span>
              </Link>
            </DropdownMenuItem>
            {isAdmin && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/admin-settings">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>系统管理</span>
                  </Link>
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>退出登录</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
