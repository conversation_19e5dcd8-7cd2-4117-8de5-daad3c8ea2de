from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.query import QueryHistory, Favorite
from app.schemas.query import QueryRequest, QueryResponse, QueryHistoryResponse
from app.services.nl2sql import NL2SQLService
from app.services.database import DatabaseService

router = APIRouter()

@router.post("/execute", response_model=QueryResponse)
async def execute_query(
    query_request: QueryRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # NL转SQL
        if query_request.input_mode == "nl":
            nl2sql_service = NL2SQLService()
            sql_query = await nl2sql_service.generate_sql(
                query_request.natural_query,
                query_request.data_source_id
            )
        else:
            sql_query = query_request.sql_query
        
        # 执行SQL
        db_service = DatabaseService()
        results = await db_service.execute_query(
            sql_query, 
            query_request.data_source_id
        )
        
        # 保存查询历史
        query_history = QueryHistory(
            user_id=current_user.id,
            natural_query=query_request.natural_query or "",
            generated_sql=sql_query,
            execution_status="success",
            results=results,
            data_source_id=query_request.data_source_id
        )
        db.add(query_history)
        db.commit()
        
        return QueryResponse(
            sql_query=sql_query,
            results=results,
            execution_status="success",
            query_id=query_history.id
        )
        
    except Exception as e:
        # 保存失败记录
        query_history = QueryHistory(
            user_id=current_user.id,
            natural_query=query_request.natural_query or "",
            generated_sql=sql_query if 'sql_query' in locals() else "",
            execution_status="error",
            error_message=str(e),
            data_source_id=query_request.data_source_id
        )
        db.add(query_history)
        db.commit()
        
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/history", response_model=List[QueryHistoryResponse])
async def get_query_history(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    queries = db.query(QueryHistory).filter(
        QueryHistory.user_id == current_user.id
    ).order_by(QueryHistory.created_at.desc()).limit(50).all()
    
    return queries