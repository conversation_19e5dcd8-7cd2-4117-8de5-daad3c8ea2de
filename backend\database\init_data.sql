-- NL2SQL系统初始化数据脚本

USE nl2sql_system;

-- 确保系统设置表中有初始记录
INSERT INTO system_settings (id) VALUES (1) ON DUPLICATE KEY UPDATE id = 1;

-- 创建默认LLM提供商
INSERT INTO llm_providers 
(name, provider_type, api_key, base_url, models, is_active, is_default) VALUES
('OpenAI', 'openai', 'sk-your-api-key-here', 'https://api.openai.com/v1', 
'{"models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]}', 
TRUE, TRUE)
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 创建示例数据源（需要根据实际情况修改）
INSERT INTO data_sources 
(name, type, host, port, `database`, username, password, status, description) VALUES
('示例MySQL数据源', 'mysql', 'localhost', 3306, 'example_db', 'root', 'encrypted_password_here', 
'disconnected', '这是一个示例数据源，请修改为实际连接信息')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 注意：以下是示例数据，实际使用时应根据需要修改或删除

-- 创建测试用户（密码需要在应用程序中进行哈希处理）
INSERT INTO users 
(username, email, name, hashed_password, role, department, position, is_active) VALUES
('test_user', '<EMAIL>', '测试用户', 'CHANGE_THIS_PASSWORD_HASH', 'user', '测试部门', '测试职位', TRUE)
ON DUPLICATE KEY UPDATE id = id;

-- 创建示例查询历史（仅用于演示，实际使用时可删除）
INSERT INTO query_history 
(user_id, natural_query, generated_sql, execution_status, data_source_id, created_at) VALUES
(1, '查询所有用户', 'SELECT * FROM users', 'completed', 1, NOW() - INTERVAL 1 DAY),
(1, '查询最近10条订单', 'SELECT * FROM orders ORDER BY created_at DESC LIMIT 10', 'completed', 1, NOW() - INTERVAL 12 HOUR);

-- 创建示例收藏（仅用于演示，实际使用时可删除）
INSERT INTO favorites 
(user_id, query_id, name, description, tags, is_public) VALUES
(1, 1, '用户查询', '查询所有系统用户', '{"tags": ["用户", "系统"]}', TRUE);

-- 创建示例审计日志（仅用于演示，实际使用时可删除）
INSERT INTO audit_logs 
(user_id, action, resource_type, resource_id, details, ip_address, status) VALUES
(1, 'login', 'user', 1, '{"browser": "Chrome", "os": "Windows"}', '127.0.0.1', 'success'),
(1, 'query', 'datasource', 1, '{"query_id": 1}', '127.0.0.1', 'success');