from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class DataSourceCreate(BaseModel):
    name: str
    type: str
    host: str
    port: int
    database: str
    username: str
    password: str
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None

class DataSourceUpdate(BaseModel):
    name: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None
    database: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None

class DataSourceResponse(BaseModel):
    id: int
    name: str
    type: str
    host: str
    port: int
    database: str
    username: str
    status: str
    description: Optional[str]
    last_tested: Optional[datetime]
    last_sync: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True