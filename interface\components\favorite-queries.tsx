"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { <PERSON>, Clock, StarOff} from "lucide-react"
import apiClient from "@/lib/api-client"

interface FavoriteQuery {
  id: string
  title: string
  query: string
  createdAt: string
  tags: string[]
}

const mockFavoriteQueries: FavoriteQuery[] = [
  {
    id: "1",
    title: "销售额前10的产品",
    query:
      "SELECT product_name, SUM(sales_amount) as total_sales FROM sales_data GROUP BY product_name ORDER BY total_sales DESC LIMIT 10",
    createdAt: "2024-01-15",
    tags: ["销售", "产品"],
  },
  {
    id: "2",
    title: "月度用户增长趋势",
    query:
      "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as new_users FROM users GROUP BY month ORDER BY month",
    createdAt: "2024-01-14",
    tags: ["用户", "增长"],
  },
  {
    id: "3",
    title: "订单状态分布",
    query: "SELECT status, COUNT(*) as count FROM orders GROUP BY status",
    createdAt: "2024-01-13",
    tags: ["订单", "状态"],
  },
]

export default function FavoriteQueries() {
  const { user } = useAuth()
  const [searchTerm] = useState("")
  const [favoriteQueries, setFavoriteQueries] = useState<FavoriteQuery[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // 加载收藏查询
  useEffect(() => {
    const loadFavorites = async () => {
      if (!user) return

      try {
        setIsLoading(true)
        const response = await apiClient.get('/favorites/')

        // 转换后端数据格式
        const transformedFavorites = response.map((item: any) => ({
          id: item.id.toString(),
          title: item.name,
          query: item.query_id ? '' : '', // 需要根据实际API结构调整
          createdAt: new Date(item.created_at).toISOString().split('T')[0],
          tags: item.tags ? JSON.parse(item.tags).tags || [] : [],
        }))

        setFavoriteQueries(transformedFavorites)
      } catch (error) {
        console.error('加载收藏查询失败:', error)
        // 如果API调用失败，使用模拟数据作为后备
        setFavoriteQueries(mockFavoriteQueries)
      } finally {
        setIsLoading(false)
      }
    }

    loadFavorites()
  }, [user])

  const filteredQueries = favoriteQueries.filter((query) =>
    query.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    query.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  // 加载状态
  if (isLoading) {
    return (
      <div className="p-2">
        <div className="text-sm font-medium text-gray-500 mb-2 px-2">收藏的成功查询</div>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-3 border border-gray-200 rounded-lg animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-2">
      <div className="text-sm font-medium text-gray-500 mb-2 px-2">收藏的成功查询</div>

        <div className="space-y-1">
          {filteredQueries.map((query) => (
            <div
              key={query.id}
              className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium text-sm text-gray-900 line-clamp-1">{query.title}</h4>
                <Star className="h-4 w-4 text-yellow-500 fill-current flex-shrink-0" />
              </div>

              <p className="text-xs text-gray-600 mb-2 line-clamp-2 font-mono bg-gray-50 p-2 rounded">{query.query}</p>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-1">
                  {query.tags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>

                <div className="flex items-center text-xs text-gray-500">
                  <Clock className="h-3 w-3 mr-1" />
                  {query.createdAt}
                </div>
              </div>
            </div>
          ))}

          {filteredQueries.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Star className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">暂无收藏的查询</p>
              <p className="text-xs text-gray-400 mt-1">{searchTerm ? "未找到匹配的查询" : "开始收藏您常用的查询吧"}</p>
            </div>
          )}
        </div>

    </div>
  )
}
