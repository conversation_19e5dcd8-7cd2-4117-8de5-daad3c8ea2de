"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-settings/page",{

/***/ "(app-pages-browser)/./components/data-source-config.tsx":
/*!*******************************************!*\
  !*** ./components/data-source-config.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DataSourceConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube-diagonal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-sync.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst mockDataSources = [\n    {\n        id: \"1\",\n        name: \"生产数据库\",\n        type: \"mysql\",\n        host: \"prod-db.example.com\",\n        port: 3306,\n        database: \"production\",\n        username: \"app_user\",\n        password: \"encrypted_password\",\n        status: \"connected\",\n        description: \"生产环境主数据库\",\n        cronJob: \"0 0 * * *\",\n        createdAt: \"2024-01-01\",\n        updatedAt: \"2024-01-15\",\n        lastTested: \"2024-01-15 10:30:00\",\n        lastSync: \"2024-01-15 10:30:00\"\n    },\n    {\n        id: \"2\",\n        name: \"测试数据库\",\n        type: \"postgresql\",\n        host: \"test-db.example.com\",\n        port: 5432,\n        database: \"testdb\",\n        username: \"test_user\",\n        password: \"encrypted_password\",\n        status: \"connected\",\n        description: \"测试环境数据库\",\n        cronJob: \"0 0 * * *\",\n        createdAt: \"2024-01-02\",\n        updatedAt: \"2024-01-10\",\n        lastTested: \"2024-01-14 15:20:00\",\n        lastSync: \"2024-01-14 15:20:00\"\n    },\n    {\n        id: \"3\",\n        name: \"数据仓库\",\n        type: \"doris\",\n        host: \"dws.example.com\",\n        port: 9030,\n        database: \"DWPROD\",\n        username: \"dw_user\",\n        password: \"encrypted_password\",\n        status: \"error\",\n        description: \"数据仓库Doris\",\n        cronJob: \"0 0 * * *\",\n        createdAt: \"2024-01-03\",\n        updatedAt: \"2024-01-12\",\n        lastTested: \"2024-01-12 09:45:00\",\n        lastSync: \"2024-01-14 15:20:00\"\n    }\n];\nconst databaseTypes = [\n    {\n        value: \"doris\",\n        label: \"Doris\"\n    },\n    {\n        value: \"mysql\",\n        label: \"MySQL\"\n    },\n    {\n        value: \"postgresql\",\n        label: \"PostgreSQL\"\n    },\n    {\n        value: \"oracle\",\n        label: \"Oracle\"\n    },\n    {\n        value: \"sqlserver\",\n        label: \"SQL Server\"\n    },\n    {\n        value: \"sqlite\",\n        label: \"SQLite\"\n    }\n];\nfunction DataSourceConfig() {\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [dataSources, setDataSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDataSource, setSelectedDataSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTesting, setIsTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        type: \"mysql\",\n        host: \"\",\n        port: 3306,\n        database: \"\",\n        username: \"\",\n        password: \"\",\n        description: \"\"\n    });\n    // 加载数据源数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DataSourceConfig.useEffect\": ()=>{\n            const loadDataSources = {\n                \"DataSourceConfig.useEffect.loadDataSources\": async ()=>{\n                    if (!user || user.role !== 'admin') return;\n                    try {\n                        setIsLoading(true);\n                        setError(\"\");\n                        const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('/datasources/');\n                        // 转换后端数据格式为前端格式\n                        const transformedDataSources = response.map({\n                            \"DataSourceConfig.useEffect.loadDataSources.transformedDataSources\": (ds)=>({\n                                    id: ds.id.toString(),\n                                    name: ds.name,\n                                    type: ds.db_type,\n                                    host: ds.host,\n                                    port: ds.port,\n                                    database: ds.database_name,\n                                    username: ds.username,\n                                    password: \"******\",\n                                    status: ds.is_active ? \"connected\" : \"disconnected\",\n                                    description: ds.description || \"\",\n                                    createdAt: new Date(ds.created_at).toISOString().split('T')[0],\n                                    updatedAt: new Date(ds.updated_at).toISOString().split('T')[0]\n                                })\n                        }[\"DataSourceConfig.useEffect.loadDataSources.transformedDataSources\"]);\n                        setDataSources(transformedDataSources);\n                    } catch (error) {\n                        console.error('加载数据源失败:', error);\n                        setError('加载数据源失败，使用模拟数据');\n                        // 如果API调用失败，使用模拟数据作为后备\n                        setDataSources(mockDataSources);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"DataSourceConfig.useEffect.loadDataSources\"];\n            loadDataSources();\n        }\n    }[\"DataSourceConfig.useEffect\"], [\n        user\n    ]);\n    const handleCreateDataSource = ()=>{\n        const newDataSource = {\n            id: Date.now().toString(),\n            ...formData,\n            status: \"disconnected\",\n            createdAt: new Date().toISOString().split(\"T\")[0],\n            updatedAt: new Date().toISOString().split(\"T\")[0]\n        };\n        setDataSources([\n            ...dataSources,\n            newDataSource\n        ]);\n        setIsCreateDialogOpen(false);\n        resetForm();\n    };\n    const handleEditDataSource = ()=>{\n        if (!selectedDataSource) return;\n        const updatedDataSources = dataSources.map((ds)=>ds.id === selectedDataSource.id ? {\n                ...ds,\n                ...formData,\n                updatedAt: new Date().toISOString().split(\"T\")[0]\n            } : ds);\n        setDataSources(updatedDataSources);\n        setIsEditDialogOpen(false);\n        resetForm();\n    };\n    const handleDeleteDataSource = (id)=>{\n        if (confirm(\"确定要删除这个数据源吗？\")) {\n            setDataSources(dataSources.filter((ds)=>ds.id !== id));\n        }\n    };\n    const handleTestConnection = async (dataSource)=>{\n        setIsTesting(true);\n        setTestResult(null);\n        // Simulate connection test\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        const success = Math.random() > 0.3 // 70% success rate for demo\n        ;\n        setTestResult({\n            success,\n            message: success ? \"连接测试成功！数据库连接正常。\" : \"连接测试失败：无法连接到数据库，请检查配置信息。\"\n        });\n        if (dataSource && success) {\n            // Update the data source status\n            const updatedDataSources = dataSources.map((ds)=>ds.id === dataSource.id ? {\n                    ...ds,\n                    status: \"connected\",\n                    lastTested: new Date().toLocaleString()\n                } : ds);\n            setDataSources(updatedDataSources);\n        }\n        setIsTesting(false);\n    };\n    const openEditDialog = (dataSource)=>{\n        setSelectedDataSource(dataSource);\n        setFormData({\n            name: dataSource.name,\n            type: dataSource.type,\n            host: dataSource.host,\n            port: dataSource.port,\n            database: dataSource.database,\n            username: dataSource.username,\n            password: dataSource.password,\n            description: dataSource.description || \"\"\n        });\n        setIsEditDialogOpen(true);\n    };\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            type: \"mysql\",\n            host: \"\",\n            port: 3306,\n            database: \"\",\n            username: \"\",\n            password: \"\",\n            description: \"\"\n        });\n        setSelectedDataSource(null);\n        setTestResult(null);\n        setShowPassword(false);\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"已连接\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 16\n                }, this);\n            case \"disconnected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    variant: \"secondary\",\n                    children: \"未连接\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    variant: \"destructive\",\n                    children: \"连接错误\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    variant: \"outline\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 16\n                }, this);\n            case \"disconnected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getDefaultPort = (type)=>{\n        switch(type){\n            case \"doris\":\n                return 9030;\n            case \"mysql\":\n                return 3306;\n            case \"postgresql\":\n                return 5432;\n            case \"oracle\":\n                return 1521;\n            case \"sqlserver\":\n                return 1433;\n            case \"sqlite\":\n                return 0;\n            default:\n                return 3306;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"数据源配置\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"管理数据库连接和配置信息\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                        open: isCreateDialogOpen,\n                        onOpenChange: setIsCreateDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>resetForm(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"新建数据源\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                className: \"max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                                children: \"创建数据源\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                                children: \"配置新的数据库连接信息\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                                        defaultValue: \"basic\",\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsList, {\n                                                className: \"grid w-full grid-cols-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                                        value: \"basic\",\n                                                        children: \"基本信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                                        value: \"connection\",\n                                                        children: \"连接配置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                                value: \"basic\",\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"name\",\n                                                                children: \"数据源名称\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"name\",\n                                                                value: formData.name,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        name: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入数据源名称\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"type\",\n                                                                children: \"数据库类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                value: formData.type,\n                                                                onValueChange: (value)=>{\n                                                                    setFormData({\n                                                                        ...formData,\n                                                                        type: value,\n                                                                        port: getDefaultPort(value)\n                                                                    });\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                            lineNumber: 363,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                        children: databaseTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                value: type.value,\n                                                                                children: type.label\n                                                                            }, type.value, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                lineNumber: 367,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"description\",\n                                                                children: \"描述\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_12__.Textarea, {\n                                                                id: \"description\",\n                                                                value: formData.description,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        description: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入数据源描述（可选）\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                                value: \"connection\",\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"host\",\n                                                                        children: \"主机地址\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"host\",\n                                                                        value: formData.host,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                host: e.target.value\n                                                                            }),\n                                                                        placeholder: \"localhost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"port\",\n                                                                        children: \"端口\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"port\",\n                                                                        type: \"number\",\n                                                                        value: formData.port,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                port: Number.parseInt(e.target.value)\n                                                                            })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"database\",\n                                                                children: \"数据库名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"database\",\n                                                                value: formData.database,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        database: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入数据库名称\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"username\",\n                                                                        children: \"用户名\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"username\",\n                                                                        value: formData.username,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                username: e.target.value\n                                                                            }),\n                                                                        placeholder: \"输入用户名\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                        htmlFor: \"password\",\n                                                                        children: \"密码\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                id: \"password\",\n                                                                                type: showPassword ? \"text\" : \"password\",\n                                                                                value: formData.password,\n                                                                                onChange: (e)=>setFormData({\n                                                                                        ...formData,\n                                                                                        password: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"输入密码\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                                onClick: ()=>setShowPassword(!showPassword),\n                                                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 41\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 74\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                lineNumber: 438,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg border text-sm \".concat(testResult.success ? \"bg-green-50 border-green-200 text-green-800\" : \"bg-red-50 border-red-200 text-red-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                testResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 41\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 79\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: testResult.message\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setIsCreateDialogOpen(false),\n                                                children: \"取消\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                // type=\"button\"\n                                                // variant=\"outline\"\n                                                onClick: ()=>handleTestConnection(),\n                                                disabled: isTesting || !formData.name.trim() || !formData.host.trim() || !formData.database.trim() || !formData.username.trim() || !formData.password.trim(),\n                                                children: isTesting ? \"测试中...\" : \"测试连接\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                onClick: handleCreateDataSource,\n                                                disabled: !formData.name.trim() || !formData.host.trim() || !formData.database.trim() || !formData.username.trim() || !formData.password.trim(),\n                                                children: \"创建数据源\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: \"数据源列表\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                children: \"系统中的所有数据源连接\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"数据源\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"类型\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"同步作业Cron\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"最近同步时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableBody, {\n                                    children: dataSources.map((dataSource)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: dataSource.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: dataSource.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-900\",\n                                                        children: dataSource.type\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            getStatusIcon(dataSource.status),\n                                                            getStatusBadge(dataSource.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: dataSource.cronJob\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: dataSource.lastSync ? new Date(dataSource.lastSync).toLocaleString() : \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-test\",\n                                                                \"data-tooltip-content\": \"测试连接\",\n                                                                onClick: ()=>handleTestConnection(dataSource),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_14__.Tooltip, {\n                                                                        id: \"tooltip-test\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-sync\",\n                                                                \"data-tooltip-content\": \"元数据同步\",\n                                                                onClick: ()=>handleTestConnection(dataSource),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_14__.Tooltip, {\n                                                                        id: \"tooltip-sync\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-edit\",\n                                                                \"data-tooltip-content\": \"编辑\",\n                                                                onClick: ()=>openEditDialog(dataSource),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 553,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_14__.Tooltip, {\n                                                                        id: \"tooltip-edit\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-trash\",\n                                                                \"data-tooltip-content\": \"删除\",\n                                                                onClick: ()=>handleDeleteDataSource(dataSource.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_14__.Tooltip, {\n                                                                        id: \"tooltip-trash\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, dataSource.id, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                lineNumber: 500,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isEditDialogOpen,\n                onOpenChange: setIsEditDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"编辑数据源\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: \"修改数据库连接信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                            defaultValue: \"basic\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                            value: \"basic\",\n                                            children: \"基本信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                            value: \"connection\",\n                                            children: \"连接配置\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                    value: \"basic\",\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"数据源名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"输入数据源名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"type\",\n                                                    children: \"数据库类型\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                    value: formData.type,\n                                                    onValueChange: (value)=>{\n                                                        setFormData({\n                                                            ...formData,\n                                                            type: value,\n                                                            port: getDefaultPort(value)\n                                                        });\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                            children: databaseTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: type.value,\n                                                                    children: type.label\n                                                                }, type.value, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_12__.Textarea, {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            description: e.target.value\n                                                        }),\n                                                    placeholder: \"输入数据源描述（可选）\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                    value: \"connection\",\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"host\",\n                                                            children: \"主机地址\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"host\",\n                                                            value: formData.host,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    host: e.target.value\n                                                                }),\n                                                            placeholder: \"localhost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"port\",\n                                                            children: \"端口\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"port\",\n                                                            type: \"number\",\n                                                            value: formData.port,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    port: Number.parseInt(e.target.value)\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"database\",\n                                                    children: \"数据库名\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"database\",\n                                                    value: formData.database,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            database: e.target.value\n                                                        }),\n                                                    placeholder: \"输入数据库名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"username\",\n                                                            children: \"用户名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"username\",\n                                                            value: formData.username,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    username: e.target.value\n                                                                }),\n                                                            placeholder: \"输入用户名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"password\",\n                                                            children: \"密码\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"password\",\n                                                                    type: showPassword ? \"text\" : \"password\",\n                                                                    value: formData.password,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            password: e.target.value\n                                                                        }),\n                                                                    placeholder: \"输入密码\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 39\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 72\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 11\n                        }, this),\n                        testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg border text-sm \".concat(testResult.success ? \"bg-green-50 border-green-200 text-green-800\" : \"bg-red-50 border-red-200 text-red-800\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    testResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 39\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 77\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: testResult.message\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 700,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>handleTestConnection(),\n                                    disabled: isTesting || !formData.host || !formData.database,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 15\n                                        }, this),\n                                        isTesting ? \"测试中...\" : \"测试连接\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsEditDialogOpen(false),\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleEditDataSource,\n                                    disabled: !formData.name.trim() || !formData.host.trim() || !formData.database.trim() || !formData.username.trim() || !formData.password.trim(),\n                                    children: \"保存\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 707,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                lineNumber: 569,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s(DataSourceConfig, \"vMUFWKGq3PwbQT73k2nootByBiQ=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DataSourceConfig;\nvar _c;\n$RefreshReg$(_c, \"DataSourceConfig\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZGF0YS1zb3VyY2UtY29uZmlnLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDTTtBQUNUO0FBQ087QUFDRjtBQUNBO0FBQ21EO0FBQ25EO0FBUWQ7QUFDc0U7QUFDQztBQUNuRDtBQUM0QjtBQUMwRDtBQUNsRztBQW9CdkMsTUFBTStDLGtCQUFnQztJQUNwQztRQUNFQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRWQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VkLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7Q0FDRDtBQUVELE1BQU1DLGdCQUFnQjtJQUNwQjtRQUFFQyxPQUFPO1FBQVNDLE9BQU87SUFBUTtJQUNqQztRQUFFRCxPQUFPO1FBQVNDLE9BQU87SUFBUTtJQUNqQztRQUFFRCxPQUFPO1FBQWNDLE9BQU87SUFBYTtJQUMzQztRQUFFRCxPQUFPO1FBQVVDLE9BQU87SUFBUztJQUNuQztRQUFFRCxPQUFPO1FBQWFDLE9BQU87SUFBYTtJQUMxQztRQUFFRCxPQUFPO1FBQVVDLE9BQU87SUFBUztDQUNwQztBQUVjLFNBQVNDOztJQUN0QixNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHakUsK0RBQU9BO0lBQ3hCLE1BQU0sQ0FBQ2tFLGFBQWFDLGVBQWUsR0FBR3JFLCtDQUFRQSxDQUFlLEVBQUU7SUFDL0QsTUFBTSxDQUFDc0UsV0FBV0MsYUFBYSxHQUFHdkUsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDd0UsT0FBT0MsU0FBUyxHQUFHekUsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDMEUsb0JBQW9CQyxzQkFBc0IsR0FBRzNFLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzRFLGtCQUFrQkMsb0JBQW9CLEdBQUc3RSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM4RSxvQkFBb0JDLHNCQUFzQixHQUFHL0UsK0NBQVFBLENBQW9CO0lBQ2hGLE1BQU0sQ0FBQ2dGLGNBQWNDLGdCQUFnQixHQUFHakYsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDa0YsWUFBWUMsY0FBYyxHQUFHbkYsK0NBQVFBLENBQStDO0lBQzNGLE1BQU0sQ0FBQ29GLFdBQVdDLGFBQWEsR0FBR3JGLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3NGLFVBQVVDLFlBQVksR0FBR3ZGLCtDQUFRQSxDQUFDO1FBQ3ZDaUQsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWRSxhQUFhO0lBQ2Y7SUFFQSxVQUFVO0lBQ1Z4RCxnREFBU0E7c0NBQUM7WUFDUixNQUFNdUY7OERBQWtCO29CQUN0QixJQUFJLENBQUNyQixRQUFRQSxLQUFLc0IsSUFBSSxLQUFLLFNBQVM7b0JBRXBDLElBQUk7d0JBQ0ZsQixhQUFhO3dCQUNiRSxTQUFTO3dCQUNULE1BQU1pQixXQUFXLE1BQU12Rix1REFBU0EsQ0FBQ3dGLEdBQUcsQ0FBQzt3QkFFckMsZ0JBQWdCO3dCQUNoQixNQUFNQyx5QkFBeUJGLFNBQVNHLEdBQUc7aUdBQUMsQ0FBQ0MsS0FBYTtvQ0FDeEQ5QyxJQUFJOEMsR0FBRzlDLEVBQUUsQ0FBQytDLFFBQVE7b0NBQ2xCOUMsTUFBTTZDLEdBQUc3QyxJQUFJO29DQUNiQyxNQUFNNEMsR0FBR0UsT0FBTztvQ0FDaEI3QyxNQUFNMkMsR0FBRzNDLElBQUk7b0NBQ2JDLE1BQU0wQyxHQUFHMUMsSUFBSTtvQ0FDYkMsVUFBVXlDLEdBQUdHLGFBQWE7b0NBQzFCM0MsVUFBVXdDLEdBQUd4QyxRQUFRO29DQUNyQkMsVUFBVTtvQ0FDVkMsUUFBUXNDLEdBQUdJLFNBQVMsR0FBRyxjQUFjO29DQUNyQ3pDLGFBQWFxQyxHQUFHckMsV0FBVyxJQUFJO29DQUMvQkUsV0FBVyxJQUFJd0MsS0FBS0wsR0FBR00sVUFBVSxFQUFFQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtvQ0FDOUQxQyxXQUFXLElBQUl1QyxLQUFLTCxHQUFHUyxVQUFVLEVBQUVGLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dDQUNoRTs7d0JBRUFqQyxlQUFldUI7b0JBQ2pCLEVBQUUsT0FBT3BCLE9BQVk7d0JBQ25CZ0MsUUFBUWhDLEtBQUssQ0FBQyxZQUFZQTt3QkFDMUJDLFNBQVM7d0JBQ1QsdUJBQXVCO3dCQUN2QkosZUFBZXRCO29CQUNqQixTQUFVO3dCQUNSd0IsYUFBYTtvQkFDZjtnQkFDRjs7WUFFQWlCO1FBQ0Y7cUNBQUc7UUFBQ3JCO0tBQUs7SUFFVCxNQUFNc0MseUJBQXlCO1FBQzdCLE1BQU1DLGdCQUE0QjtZQUNoQzFELElBQUltRCxLQUFLUSxHQUFHLEdBQUdaLFFBQVE7WUFDdkIsR0FBR1QsUUFBUTtZQUNYOUIsUUFBUTtZQUNSRyxXQUFXLElBQUl3QyxPQUFPRSxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUNqRDFDLFdBQVcsSUFBSXVDLE9BQU9FLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQ25EO1FBRUFqQyxlQUFlO2VBQUlEO1lBQWFzQztTQUFjO1FBQzlDL0Isc0JBQXNCO1FBQ3RCaUM7SUFDRjtJQUVBLE1BQU1DLHVCQUF1QjtRQUMzQixJQUFJLENBQUMvQixvQkFBb0I7UUFFekIsTUFBTWdDLHFCQUFxQjFDLFlBQVl5QixHQUFHLENBQUMsQ0FBQ0MsS0FDMUNBLEdBQUc5QyxFQUFFLEtBQUs4QixtQkFBbUI5QixFQUFFLEdBQzNCO2dCQUNFLEdBQUc4QyxFQUFFO2dCQUNMLEdBQUdSLFFBQVE7Z0JBQ1gxQixXQUFXLElBQUl1QyxPQUFPRSxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUNuRCxJQUNBUjtRQUdOekIsZUFBZXlDO1FBQ2ZqQyxvQkFBb0I7UUFDcEIrQjtJQUNGO0lBRUEsTUFBTUcseUJBQXlCLENBQUMvRDtRQUM5QixJQUFJZ0UsUUFBUSxpQkFBaUI7WUFDM0IzQyxlQUFlRCxZQUFZNkMsTUFBTSxDQUFDLENBQUNuQixLQUFPQSxHQUFHOUMsRUFBRSxLQUFLQTtRQUN0RDtJQUNGO0lBRUEsTUFBTWtFLHVCQUF1QixPQUFPQztRQUNsQzlCLGFBQWE7UUFDYkYsY0FBYztRQUVkLDJCQUEyQjtRQUMzQixNQUFNLElBQUlpQyxRQUFRLENBQUNDLFVBQVlDLFdBQVdELFNBQVM7UUFFbkQsTUFBTUUsVUFBVUMsS0FBS0MsTUFBTSxLQUFLLElBQUksNEJBQTRCOztRQUNoRXRDLGNBQWM7WUFDWm9DO1lBQ0FHLFNBQVNILFVBQVUsb0JBQW9CO1FBQ3pDO1FBRUEsSUFBSUosY0FBY0ksU0FBUztZQUN6QixnQ0FBZ0M7WUFDaEMsTUFBTVQscUJBQXFCMUMsWUFBWXlCLEdBQUcsQ0FBQyxDQUFDQyxLQUMxQ0EsR0FBRzlDLEVBQUUsS0FBS21FLFdBQVduRSxFQUFFLEdBQ25CO29CQUNFLEdBQUc4QyxFQUFFO29CQUNMdEMsUUFBUTtvQkFDUkssWUFBWSxJQUFJc0MsT0FBT3dCLGNBQWM7Z0JBQ3ZDLElBQ0E3QjtZQUVOekIsZUFBZXlDO1FBQ2pCO1FBRUF6QixhQUFhO0lBQ2Y7SUFFQSxNQUFNdUMsaUJBQWlCLENBQUNUO1FBQ3RCcEMsc0JBQXNCb0M7UUFDdEI1QixZQUFZO1lBQ1Z0QyxNQUFNa0UsV0FBV2xFLElBQUk7WUFDckJDLE1BQU1pRSxXQUFXakUsSUFBSTtZQUNyQkMsTUFBTWdFLFdBQVdoRSxJQUFJO1lBQ3JCQyxNQUFNK0QsV0FBVy9ELElBQUk7WUFDckJDLFVBQVU4RCxXQUFXOUQsUUFBUTtZQUM3QkMsVUFBVTZELFdBQVc3RCxRQUFRO1lBQzdCQyxVQUFVNEQsV0FBVzVELFFBQVE7WUFDN0JFLGFBQWEwRCxXQUFXMUQsV0FBVyxJQUFJO1FBQ3pDO1FBQ0FvQixvQkFBb0I7SUFDdEI7SUFFQSxNQUFNK0IsWUFBWTtRQUNoQnJCLFlBQVk7WUFDVnRDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxVQUFVO1lBQ1ZDLFVBQVU7WUFDVkUsYUFBYTtRQUNmO1FBQ0FzQixzQkFBc0I7UUFDdEJJLGNBQWM7UUFDZEYsZ0JBQWdCO0lBQ2xCO0lBRUEsTUFBTTRDLGlCQUFpQixDQUFDckU7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDNUMsdURBQUtBO29CQUFDa0gsV0FBVTs4QkFBOEI7Ozs7OztZQUN4RCxLQUFLO2dCQUNILHFCQUFPLDhEQUFDbEgsdURBQUtBO29CQUFDbUgsU0FBUTs4QkFBWTs7Ozs7O1lBQ3BDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNuSCx1REFBS0E7b0JBQUNtSCxTQUFROzhCQUFjOzs7Ozs7WUFDdEM7Z0JBQ0UscUJBQU8sOERBQUNuSCx1REFBS0E7b0JBQUNtSCxTQUFROzhCQUFVOzs7Ozs7UUFDcEM7SUFDRjtJQUVBLE1BQU1DLGdCQUFnQixDQUFDeEU7UUFDckIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDZiw2S0FBV0E7b0JBQUNxRixXQUFVOzs7Ozs7WUFDaEMsS0FBSztnQkFDSCxxQkFBTyw4REFBQ25GLDZLQUFXQTtvQkFBQ21GLFdBQVU7Ozs7OztZQUNoQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDcEYsNktBQU9BO29CQUFDb0YsV0FBVTs7Ozs7O1lBQzVCO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTUcsaUJBQWlCLENBQUMvRTtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEscUJBQ0UsOERBQUNnRjtRQUFJSixXQUFVOzswQkFDYiw4REFBQ0k7Z0JBQUlKLFdBQVU7O2tDQUNiLDhEQUFDSTs7MENBQ0MsOERBQUNDO2dDQUFHTCxXQUFVOzBDQUF3Qjs7Ozs7OzBDQUN0Qyw4REFBQ007Z0NBQUVOLFdBQVU7MENBQWdCOzs7Ozs7Ozs7Ozs7a0NBRS9CLDhEQUFDakgseURBQU1BO3dCQUFDd0gsTUFBTTNEO3dCQUFvQjRELGNBQWMzRDs7MENBQzlDLDhEQUFDekQsZ0VBQWFBO2dDQUFDcUgsT0FBTzswQ0FDcEIsNEVBQUNuSSx5REFBTUE7b0NBQUNvSSxTQUFTLElBQU01Qjs7c0RBQ3JCLDhEQUFDeEUsNktBQUlBOzRDQUFDMEYsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs7Ozs7OzBDQUlyQyw4REFBQ2hILGdFQUFhQTtnQ0FBQ2dILFdBQVU7O2tEQUN2Qiw4REFBQzlHLCtEQUFZQTs7MERBQ1gsOERBQUNDLDhEQUFXQTswREFBQzs7Ozs7OzBEQUNiLDhEQUFDRixvRUFBaUJBOzBEQUFDOzs7Ozs7Ozs7Ozs7a0RBR3JCLDhEQUFDZ0Isc0RBQUlBO3dDQUFDMEcsY0FBYTt3Q0FBUVgsV0FBVTs7MERBQ25DLDhEQUFDN0YsMERBQVFBO2dEQUFDNkYsV0FBVTs7a0VBQ2xCLDhEQUFDNUYsNkRBQVdBO3dEQUFDOEIsT0FBTTtrRUFBUTs7Ozs7O2tFQUMzQiw4REFBQzlCLDZEQUFXQTt3REFBQzhCLE9BQU07a0VBQWE7Ozs7Ozs7Ozs7OzswREFHbEMsOERBQUNoQyw2REFBV0E7Z0RBQUNnQyxPQUFNO2dEQUFROEQsV0FBVTs7a0VBQ25DLDhEQUFDSTt3REFBSUosV0FBVTs7MEVBQ2IsOERBQUN4SCx1REFBS0E7Z0VBQUNvSSxTQUFROzBFQUFPOzs7Ozs7MEVBQ3RCLDhEQUFDckksdURBQUtBO2dFQUNKMkMsSUFBRztnRUFDSGdCLE9BQU9zQixTQUFTckMsSUFBSTtnRUFDcEIwRixVQUFVLENBQUNDLElBQU1yRCxZQUFZO3dFQUFFLEdBQUdELFFBQVE7d0VBQUVyQyxNQUFNMkYsRUFBRUMsTUFBTSxDQUFDN0UsS0FBSztvRUFBQztnRUFDakU4RSxhQUFZOzs7Ozs7Ozs7Ozs7a0VBSWhCLDhEQUFDWjt3REFBSUosV0FBVTs7MEVBQ2IsOERBQUN4SCx1REFBS0E7Z0VBQUNvSSxTQUFROzBFQUFPOzs7Ozs7MEVBQ3RCLDhEQUFDakgsMERBQU1BO2dFQUNMdUMsT0FBT3NCLFNBQVNwQyxJQUFJO2dFQUNwQjZGLGVBQWUsQ0FBQy9FO29FQUNkdUIsWUFBWTt3RUFDVixHQUFHRCxRQUFRO3dFQUNYcEMsTUFBTWM7d0VBQ05aLE1BQU02RSxlQUFlakU7b0VBQ3ZCO2dFQUNGOztrRkFFQSw4REFBQ3BDLGlFQUFhQTtrRkFDWiw0RUFBQ0MsK0RBQVdBOzs7Ozs7Ozs7O2tGQUVkLDhEQUFDSCxpRUFBYUE7a0ZBQ1hxQyxjQUFjOEIsR0FBRyxDQUFDLENBQUMzQyxxQkFDbEIsOERBQUN2Qiw4REFBVUE7Z0ZBQWtCcUMsT0FBT2QsS0FBS2MsS0FBSzswRkFDM0NkLEtBQUtlLEtBQUs7K0VBRElmLEtBQUtjLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBUW5DLDhEQUFDa0U7d0RBQUlKLFdBQVU7OzBFQUNiLDhEQUFDeEgsdURBQUtBO2dFQUFDb0ksU0FBUTswRUFBYzs7Ozs7OzBFQUM3Qiw4REFBQzVHLDhEQUFRQTtnRUFDUGtCLElBQUc7Z0VBQ0hnQixPQUFPc0IsU0FBUzdCLFdBQVc7Z0VBQzNCa0YsVUFBVSxDQUFDQyxJQUFNckQsWUFBWTt3RUFBRSxHQUFHRCxRQUFRO3dFQUFFN0IsYUFBYW1GLEVBQUVDLE1BQU0sQ0FBQzdFLEtBQUs7b0VBQUM7Z0VBQ3hFOEUsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUtsQiw4REFBQzlHLDZEQUFXQTtnREFBQ2dDLE9BQU07Z0RBQWE4RCxXQUFVOztrRUFDeEMsOERBQUNJO3dEQUFJSixXQUFVOzswRUFDYiw4REFBQ0k7Z0VBQUlKLFdBQVU7O2tGQUNiLDhEQUFDeEgsdURBQUtBO3dFQUFDb0ksU0FBUTtrRkFBTzs7Ozs7O2tGQUN0Qiw4REFBQ3JJLHVEQUFLQTt3RUFDSjJDLElBQUc7d0VBQ0hnQixPQUFPc0IsU0FBU25DLElBQUk7d0VBQ3BCd0YsVUFBVSxDQUFDQyxJQUFNckQsWUFBWTtnRkFBRSxHQUFHRCxRQUFRO2dGQUFFbkMsTUFBTXlGLEVBQUVDLE1BQU0sQ0FBQzdFLEtBQUs7NEVBQUM7d0VBQ2pFOEUsYUFBWTs7Ozs7Ozs7Ozs7OzBFQUdoQiw4REFBQ1o7Z0VBQUlKLFdBQVU7O2tGQUNiLDhEQUFDeEgsdURBQUtBO3dFQUFDb0ksU0FBUTtrRkFBTzs7Ozs7O2tGQUN0Qiw4REFBQ3JJLHVEQUFLQTt3RUFDSjJDLElBQUc7d0VBQ0hFLE1BQUs7d0VBQ0xjLE9BQU9zQixTQUFTbEMsSUFBSTt3RUFDcEJ1RixVQUFVLENBQUNDLElBQU1yRCxZQUFZO2dGQUFFLEdBQUdELFFBQVE7Z0ZBQUVsQyxNQUFNNEYsT0FBT0MsUUFBUSxDQUFDTCxFQUFFQyxNQUFNLENBQUM3RSxLQUFLOzRFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS3hGLDhEQUFDa0U7d0RBQUlKLFdBQVU7OzBFQUNiLDhEQUFDeEgsdURBQUtBO2dFQUFDb0ksU0FBUTswRUFBVzs7Ozs7OzBFQUMxQiw4REFBQ3JJLHVEQUFLQTtnRUFDSjJDLElBQUc7Z0VBQ0hnQixPQUFPc0IsU0FBU2pDLFFBQVE7Z0VBQ3hCc0YsVUFBVSxDQUFDQyxJQUFNckQsWUFBWTt3RUFBRSxHQUFHRCxRQUFRO3dFQUFFakMsVUFBVXVGLEVBQUVDLE1BQU0sQ0FBQzdFLEtBQUs7b0VBQUM7Z0VBQ3JFOEUsYUFBWTs7Ozs7Ozs7Ozs7O2tFQUloQiw4REFBQ1o7d0RBQUlKLFdBQVU7OzBFQUNiLDhEQUFDSTtnRUFBSUosV0FBVTs7a0ZBQ2IsOERBQUN4SCx1REFBS0E7d0VBQUNvSSxTQUFRO2tGQUFXOzs7Ozs7a0ZBQzFCLDhEQUFDckksdURBQUtBO3dFQUNKMkMsSUFBRzt3RUFDSGdCLE9BQU9zQixTQUFTaEMsUUFBUTt3RUFDeEJxRixVQUFVLENBQUNDLElBQU1yRCxZQUFZO2dGQUFFLEdBQUdELFFBQVE7Z0ZBQUVoQyxVQUFVc0YsRUFBRUMsTUFBTSxDQUFDN0UsS0FBSzs0RUFBQzt3RUFDckU4RSxhQUFZOzs7Ozs7Ozs7Ozs7MEVBR2hCLDhEQUFDWjtnRUFBSUosV0FBVTs7a0ZBQ2IsOERBQUN4SCx1REFBS0E7d0VBQUNvSSxTQUFRO2tGQUFXOzs7Ozs7a0ZBQzFCLDhEQUFDUjt3RUFBSUosV0FBVTs7MEZBQ2IsOERBQUN6SCx1REFBS0E7Z0ZBQ0oyQyxJQUFHO2dGQUNIRSxNQUFNOEIsZUFBZSxTQUFTO2dGQUM5QmhCLE9BQU9zQixTQUFTL0IsUUFBUTtnRkFDeEJvRixVQUFVLENBQUNDLElBQU1yRCxZQUFZO3dGQUFFLEdBQUdELFFBQVE7d0ZBQUUvQixVQUFVcUYsRUFBRUMsTUFBTSxDQUFDN0UsS0FBSztvRkFBQztnRkFDckU4RSxhQUFZOzs7Ozs7MEZBRWQsOERBQUMxSSx5REFBTUE7Z0ZBQ0w4QyxNQUFLO2dGQUNMNkUsU0FBUTtnRkFDUm1CLE1BQUs7Z0ZBQ0xwQixXQUFVO2dGQUNWVSxTQUFTLElBQU12RCxnQkFBZ0IsQ0FBQ0Q7MEZBRS9CQSw2QkFBZSw4REFBQ25DLDZLQUFNQTtvRkFBQ2lGLFdBQVU7Ozs7O3lHQUFlLDhEQUFDbEYsNktBQUdBO29GQUFDa0YsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBUTNFNUMsNEJBQ0MsOERBQUNnRDt3Q0FDQ0osV0FBVyxpQ0FBOEksT0FBN0c1QyxXQUFXcUMsT0FBTyxHQUFHLGdEQUFnRDtrREFFakgsNEVBQUNXOzRDQUFJSixXQUFVOztnREFDWjVDLFdBQVdxQyxPQUFPLGlCQUFHLDhEQUFDOUUsNktBQVdBO29EQUFDcUYsV0FBVTs7Ozs7eUVBQWUsOERBQUNuRiw2S0FBV0E7b0RBQUNtRixXQUFVOzs7Ozs7OERBQ25GLDhEQUFDcUI7OERBQU1qRSxXQUFXd0MsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSy9CLDhEQUFDUTt3Q0FBSUosV0FBVTs7MERBQ2IsOERBQUMxSCx5REFBTUE7Z0RBQUMySCxTQUFRO2dEQUFVUyxTQUFTLElBQU03RCxzQkFBc0I7MERBQVE7Ozs7OzswREFHdkUsOERBQUN2RSx5REFBTUE7Z0RBQ0wsZ0JBQWdCO2dEQUNoQixvQkFBb0I7Z0RBQ3BCb0ksU0FBUyxJQUFNdEI7Z0RBQ2ZrQyxVQUNFaEUsYUFDQSxDQUFDRSxTQUFTckMsSUFBSSxDQUFDb0csSUFBSSxNQUNuQixDQUFDL0QsU0FBU25DLElBQUksQ0FBQ2tHLElBQUksTUFDbkIsQ0FBQy9ELFNBQVNqQyxRQUFRLENBQUNnRyxJQUFJLE1BQ3ZCLENBQUMvRCxTQUFTaEMsUUFBUSxDQUFDK0YsSUFBSSxNQUN2QixDQUFDL0QsU0FBUy9CLFFBQVEsQ0FBQzhGLElBQUk7MERBR3hCakUsWUFBWSxXQUFXOzs7Ozs7MERBRTFCLDhEQUFDaEYseURBQU1BO2dEQUNMb0ksU0FBUy9CO2dEQUNUMkMsVUFDRSxDQUFDOUQsU0FBU3JDLElBQUksQ0FBQ29HLElBQUksTUFDbkIsQ0FBQy9ELFNBQVNuQyxJQUFJLENBQUNrRyxJQUFJLE1BQ25CLENBQUMvRCxTQUFTakMsUUFBUSxDQUFDZ0csSUFBSSxNQUN2QixDQUFDL0QsU0FBU2hDLFFBQVEsQ0FBQytGLElBQUksTUFDdkIsQ0FBQy9ELFNBQVMvQixRQUFRLENBQUM4RixJQUFJOzBEQUUxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFULDhEQUFDOUkscURBQUlBOztrQ0FDSCw4REFBQ0csMkRBQVVBOzswQ0FDVCw4REFBQ0MsMERBQVNBO2dDQUFDbUgsV0FBVTswQ0FBMEI7Ozs7OzswQ0FHL0MsOERBQUNySCxnRUFBZUE7MENBQUM7Ozs7Ozs7Ozs7OztrQ0FFbkIsOERBQUNELDREQUFXQTtrQ0FDViw0RUFBQ1csd0RBQUtBOzs4Q0FDSiw4REFBQ0ksOERBQVdBOzhDQUNWLDRFQUFDQywyREFBUUE7OzBEQUNQLDhEQUFDRiw0REFBU0E7MERBQUM7Ozs7OzswREFDWCw4REFBQ0EsNERBQVNBOzBEQUFDOzs7Ozs7MERBQ1gsOERBQUNBLDREQUFTQTswREFBQzs7Ozs7OzBEQUNYLDhEQUFDQSw0REFBU0E7MERBQUM7Ozs7OzswREFDWCw4REFBQ0EsNERBQVNBOzBEQUFDOzs7Ozs7MERBQ1gsOERBQUNBLDREQUFTQTswREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR2YsOERBQUNGLDREQUFTQTs4Q0FDUGdELFlBQVl5QixHQUFHLENBQUMsQ0FBQ3NCLDJCQUNoQiw4REFBQzNGLDJEQUFRQTs7OERBQ1AsOERBQUNILDREQUFTQTs7c0VBQ04sOERBQUM2Rzs0REFBSUosV0FBVTtzRUFBcUNYLFdBQVdsRSxJQUFJOzs7Ozs7c0VBQ25FLDhEQUFDaUY7NERBQUlKLFdBQVU7c0VBQXlCWCxXQUFXMUQsV0FBVzs7Ozs7Ozs7Ozs7OzhEQUdsRSw4REFBQ3BDLDREQUFTQTs4REFDUiw0RUFBQzZHO3dEQUFJSixXQUFVO2tFQUF5QlgsV0FBV2pFLElBQUk7Ozs7Ozs7Ozs7OzhEQUV6RCw4REFBQzdCLDREQUFTQTs4REFDUiw0RUFBQzZHO3dEQUFJSixXQUFVOzs0REFDWkUsY0FBY2IsV0FBVzNELE1BQU07NERBQy9CcUUsZUFBZVYsV0FBVzNELE1BQU07Ozs7Ozs7Ozs7Ozs4REFHckMsOERBQUNuQyw0REFBU0E7OERBQ1IsNEVBQUM4SDt3REFBS3JCLFdBQVU7a0VBQXlCWCxXQUFXekQsT0FBTzs7Ozs7Ozs7Ozs7OERBRTdELDhEQUFDckMsNERBQVNBOzhEQUNSLDRFQUFDOEg7d0RBQUtyQixXQUFVO2tFQUF5QlgsV0FBV3JELFFBQVEsR0FBRyxJQUFJcUMsS0FBS2dCLFdBQVdyRCxRQUFRLEVBQUU2RCxjQUFjLEtBQUs7Ozs7Ozs7Ozs7OzhEQUVsSCw4REFBQ3RHLDREQUFTQTs4REFDUiw0RUFBQzZHO3dEQUFJSixXQUFVOzswRUFDYiw4REFBQzFILHlEQUFNQTtnRUFBQzJILFNBQVE7Z0VBQVVtQixNQUFLO2dFQUFLSSxtQkFBZ0I7Z0VBQWVDLHdCQUFxQjtnRUFBT2YsU0FBUyxJQUFNdEIscUJBQXFCQzs7a0ZBQy9ILDhEQUFDM0UsNktBQWdCQTt3RUFBQ3NGLFdBQVU7Ozs7OztrRkFDNUIsOERBQUNoRixtREFBT0E7d0VBQUNFLElBQUc7Ozs7Ozs7Ozs7OzswRUFFaEIsOERBQUM1Qyx5REFBTUE7Z0VBQUMySCxTQUFRO2dFQUFVbUIsTUFBSztnRUFBS0ksbUJBQWdCO2dFQUFlQyx3QkFBcUI7Z0VBQVFmLFNBQVMsSUFBTXRCLHFCQUFxQkM7O2tGQUNoSSw4REFBQ2hGLDZLQUFVQTt3RUFBQzJGLFdBQVU7Ozs7OztrRkFDdEIsOERBQUNoRixtREFBT0E7d0VBQUNFLElBQUc7Ozs7Ozs7Ozs7OzswRUFFaEIsOERBQUM1Qyx5REFBTUE7Z0VBQUMySCxTQUFRO2dFQUFVbUIsTUFBSztnRUFBS0ksbUJBQWdCO2dFQUFlQyx3QkFBcUI7Z0VBQUtmLFNBQVMsSUFBTVosZUFBZVQ7O2tGQUN2SCw4REFBQzlFLDZLQUFJQTt3RUFBQ3lGLFdBQVU7Ozs7OztrRkFDaEIsOERBQUNoRixtREFBT0E7d0VBQUNFLElBQUc7Ozs7Ozs7Ozs7OzswRUFFaEIsOERBQUM1Qyx5REFBTUE7Z0VBQUMySCxTQUFRO2dFQUFVbUIsTUFBSztnRUFBS0ksbUJBQWdCO2dFQUFnQkMsd0JBQXFCO2dFQUFLZixTQUFTLElBQU16Qix1QkFBdUJJLFdBQVduRSxFQUFFOztrRkFDL0ksOERBQUNWLDZLQUFNQTt3RUFBQ3dGLFdBQVU7Ozs7OztrRkFDbEIsOERBQUNoRixtREFBT0E7d0VBQUNFLElBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQ0FyQ0xtRSxXQUFXbkUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQWdEdEMsOERBQUNuQyx5REFBTUE7Z0JBQUN3SCxNQUFNekQ7Z0JBQWtCMEQsY0FBY3pEOzBCQUM1Qyw0RUFBQy9ELGdFQUFhQTtvQkFBQ2dILFdBQVU7O3NDQUN2Qiw4REFBQzlHLCtEQUFZQTs7OENBQ1gsOERBQUNDLDhEQUFXQTs4Q0FBQzs7Ozs7OzhDQUNiLDhEQUFDRixvRUFBaUJBOzhDQUFDOzs7Ozs7Ozs7Ozs7c0NBR3JCLDhEQUFDZ0Isc0RBQUlBOzRCQUFDMEcsY0FBYTs0QkFBUVgsV0FBVTs7OENBQ25DLDhEQUFDN0YsMERBQVFBO29DQUFDNkYsV0FBVTs7c0RBQ2xCLDhEQUFDNUYsNkRBQVdBOzRDQUFDOEIsT0FBTTtzREFBUTs7Ozs7O3NEQUMzQiw4REFBQzlCLDZEQUFXQTs0Q0FBQzhCLE9BQU07c0RBQWE7Ozs7Ozs7Ozs7Ozs4Q0FHbEMsOERBQUNoQyw2REFBV0E7b0NBQUNnQyxPQUFNO29DQUFROEQsV0FBVTs7c0RBQ25DLDhEQUFDSTs0Q0FBSUosV0FBVTs7OERBQ2IsOERBQUN4SCx1REFBS0E7b0RBQUNvSSxTQUFROzhEQUFPOzs7Ozs7OERBQ3RCLDhEQUFDckksdURBQUtBO29EQUNKMkMsSUFBRztvREFDSGdCLE9BQU9zQixTQUFTckMsSUFBSTtvREFDcEIwRixVQUFVLENBQUNDLElBQU1yRCxZQUFZOzREQUFFLEdBQUdELFFBQVE7NERBQUVyQyxNQUFNMkYsRUFBRUMsTUFBTSxDQUFDN0UsS0FBSzt3REFBQztvREFDakU4RSxhQUFZOzs7Ozs7Ozs7Ozs7c0RBSWhCLDhEQUFDWjs0Q0FBSUosV0FBVTs7OERBQ2IsOERBQUN4SCx1REFBS0E7b0RBQUNvSSxTQUFROzhEQUFPOzs7Ozs7OERBQ3RCLDhEQUFDakgsMERBQU1BO29EQUNMdUMsT0FBT3NCLFNBQVNwQyxJQUFJO29EQUNwQjZGLGVBQWUsQ0FBQy9FO3dEQUNkdUIsWUFBWTs0REFDVixHQUFHRCxRQUFROzREQUNYcEMsTUFBTWM7NERBQ05aLE1BQU02RSxlQUFlakU7d0RBQ3ZCO29EQUNGOztzRUFFQSw4REFBQ3BDLGlFQUFhQTtzRUFDWiw0RUFBQ0MsK0RBQVdBOzs7Ozs7Ozs7O3NFQUVkLDhEQUFDSCxpRUFBYUE7c0VBQ1hxQyxjQUFjOEIsR0FBRyxDQUFDLENBQUMzQyxxQkFDbEIsOERBQUN2Qiw4REFBVUE7b0VBQWtCcUMsT0FBT2QsS0FBS2MsS0FBSzs4RUFDM0NkLEtBQUtlLEtBQUs7bUVBRElmLEtBQUtjLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBUW5DLDhEQUFDa0U7NENBQUlKLFdBQVU7OzhEQUNiLDhEQUFDeEgsdURBQUtBO29EQUFDb0ksU0FBUTs4REFBYzs7Ozs7OzhEQUM3Qiw4REFBQzVHLDhEQUFRQTtvREFDUGtCLElBQUc7b0RBQ0hnQixPQUFPc0IsU0FBUzdCLFdBQVc7b0RBQzNCa0YsVUFBVSxDQUFDQyxJQUFNckQsWUFBWTs0REFBRSxHQUFHRCxRQUFROzREQUFFN0IsYUFBYW1GLEVBQUVDLE1BQU0sQ0FBQzdFLEtBQUs7d0RBQUM7b0RBQ3hFOEUsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtsQiw4REFBQzlHLDZEQUFXQTtvQ0FBQ2dDLE9BQU07b0NBQWE4RCxXQUFVOztzREFDeEMsOERBQUNJOzRDQUFJSixXQUFVOzs4REFDYiw4REFBQ0k7b0RBQUlKLFdBQVU7O3NFQUNiLDhEQUFDeEgsdURBQUtBOzREQUFDb0ksU0FBUTtzRUFBTzs7Ozs7O3NFQUN0Qiw4REFBQ3JJLHVEQUFLQTs0REFDSjJDLElBQUc7NERBQ0hnQixPQUFPc0IsU0FBU25DLElBQUk7NERBQ3BCd0YsVUFBVSxDQUFDQyxJQUFNckQsWUFBWTtvRUFBRSxHQUFHRCxRQUFRO29FQUFFbkMsTUFBTXlGLEVBQUVDLE1BQU0sQ0FBQzdFLEtBQUs7Z0VBQUM7NERBQ2pFOEUsYUFBWTs7Ozs7Ozs7Ozs7OzhEQUdoQiw4REFBQ1o7b0RBQUlKLFdBQVU7O3NFQUNiLDhEQUFDeEgsdURBQUtBOzREQUFDb0ksU0FBUTtzRUFBTzs7Ozs7O3NFQUN0Qiw4REFBQ3JJLHVEQUFLQTs0REFDSjJDLElBQUc7NERBQ0hFLE1BQUs7NERBQ0xjLE9BQU9zQixTQUFTbEMsSUFBSTs0REFDcEJ1RixVQUFVLENBQUNDLElBQU1yRCxZQUFZO29FQUFFLEdBQUdELFFBQVE7b0VBQUVsQyxNQUFNNEYsT0FBT0MsUUFBUSxDQUFDTCxFQUFFQyxNQUFNLENBQUM3RSxLQUFLO2dFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBS3hGLDhEQUFDa0U7NENBQUlKLFdBQVU7OzhEQUNiLDhEQUFDeEgsdURBQUtBO29EQUFDb0ksU0FBUTs4REFBVzs7Ozs7OzhEQUMxQiw4REFBQ3JJLHVEQUFLQTtvREFDSjJDLElBQUc7b0RBQ0hnQixPQUFPc0IsU0FBU2pDLFFBQVE7b0RBQ3hCc0YsVUFBVSxDQUFDQyxJQUFNckQsWUFBWTs0REFBRSxHQUFHRCxRQUFROzREQUFFakMsVUFBVXVGLEVBQUVDLE1BQU0sQ0FBQzdFLEtBQUs7d0RBQUM7b0RBQ3JFOEUsYUFBWTs7Ozs7Ozs7Ozs7O3NEQUloQiw4REFBQ1o7NENBQUlKLFdBQVU7OzhEQUNiLDhEQUFDSTtvREFBSUosV0FBVTs7c0VBQ2IsOERBQUN4SCx1REFBS0E7NERBQUNvSSxTQUFRO3NFQUFXOzs7Ozs7c0VBQzFCLDhEQUFDckksdURBQUtBOzREQUNKMkMsSUFBRzs0REFDSGdCLE9BQU9zQixTQUFTaEMsUUFBUTs0REFDeEJxRixVQUFVLENBQUNDLElBQU1yRCxZQUFZO29FQUFFLEdBQUdELFFBQVE7b0VBQUVoQyxVQUFVc0YsRUFBRUMsTUFBTSxDQUFDN0UsS0FBSztnRUFBQzs0REFDckU4RSxhQUFZOzs7Ozs7Ozs7Ozs7OERBR2hCLDhEQUFDWjtvREFBSUosV0FBVTs7c0VBQ2IsOERBQUN4SCx1REFBS0E7NERBQUNvSSxTQUFRO3NFQUFXOzs7Ozs7c0VBQzFCLDhEQUFDUjs0REFBSUosV0FBVTs7OEVBQ2IsOERBQUN6SCx1REFBS0E7b0VBQ0oyQyxJQUFHO29FQUNIRSxNQUFNOEIsZUFBZSxTQUFTO29FQUM5QmhCLE9BQU9zQixTQUFTL0IsUUFBUTtvRUFDeEJvRixVQUFVLENBQUNDLElBQU1yRCxZQUFZOzRFQUFFLEdBQUdELFFBQVE7NEVBQUUvQixVQUFVcUYsRUFBRUMsTUFBTSxDQUFDN0UsS0FBSzt3RUFBQztvRUFDckU4RSxhQUFZOzs7Ozs7OEVBRWQsOERBQUMxSSx5REFBTUE7b0VBQ0w4QyxNQUFLO29FQUNMNkUsU0FBUTtvRUFDUm1CLE1BQUs7b0VBQ0xwQixXQUFVO29FQUNWVSxTQUFTLElBQU12RCxnQkFBZ0IsQ0FBQ0Q7OEVBRS9CQSw2QkFBZSw4REFBQ25DLDZLQUFNQTt3RUFBQ2lGLFdBQVU7Ozs7OzZGQUFlLDhEQUFDbEYsNktBQUdBO3dFQUFDa0YsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBUTNFNUMsNEJBQ0MsOERBQUNnRDs0QkFDQ0osV0FBVyxpQ0FBOEksT0FBN0c1QyxXQUFXcUMsT0FBTyxHQUFHLGdEQUFnRDtzQ0FFakgsNEVBQUNXO2dDQUFJSixXQUFVOztvQ0FDWjVDLFdBQVdxQyxPQUFPLGlCQUFHLDhEQUFDOUUsNktBQVdBO3dDQUFDcUYsV0FBVTs7Ozs7NkRBQWUsOERBQUNuRiw2S0FBV0E7d0NBQUNtRixXQUFVOzs7Ozs7a0RBQ25GLDhEQUFDcUI7a0RBQU1qRSxXQUFXd0MsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSy9CLDhEQUFDUTs0QkFBSUosV0FBVTs7OENBQ2IsOERBQUMxSCx5REFBTUE7b0NBQ0w4QyxNQUFLO29DQUNMNkUsU0FBUTtvQ0FDUlMsU0FBUyxJQUFNdEI7b0NBQ2ZrQyxVQUFVaEUsYUFBYSxDQUFDRSxTQUFTbkMsSUFBSSxJQUFJLENBQUNtQyxTQUFTakMsUUFBUTs7c0RBRTNELDhEQUFDZCw2S0FBUUE7NENBQUN1RixXQUFVOzs7Ozs7d0NBQ25CMUMsWUFBWSxXQUFXOzs7Ozs7OzhDQUUxQiw4REFBQ2hGLHlEQUFNQTtvQ0FBQzJILFNBQVE7b0NBQVVTLFNBQVMsSUFBTTNELG9CQUFvQjs4Q0FBUTs7Ozs7OzhDQUdyRSw4REFBQ3pFLHlEQUFNQTtvQ0FDTG9JLFNBQVMzQjtvQ0FDVHVDLFVBQ0UsQ0FBQzlELFNBQVNyQyxJQUFJLENBQUNvRyxJQUFJLE1BQ25CLENBQUMvRCxTQUFTbkMsSUFBSSxDQUFDa0csSUFBSSxNQUNuQixDQUFDL0QsU0FBU2pDLFFBQVEsQ0FBQ2dHLElBQUksTUFDdkIsQ0FBQy9ELFNBQVNoQyxRQUFRLENBQUMrRixJQUFJLE1BQ3ZCLENBQUMvRCxTQUFTL0IsUUFBUSxDQUFDOEYsSUFBSTs4Q0FFMUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7R0F0bkJ3Qm5GOztRQUNMaEUsMkRBQU9BOzs7S0FERmdFIiwic291cmNlcyI6WyJEOlxcd29ya3NwYWNlXFxkc3RwXFxwcm9qZWN0XFxubDJzcWxcXGludGVyZmFjZVxcY29tcG9uZW50c1xcZGF0YS1zb3VyY2UtY29uZmlnLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tIFwiQC9jb250ZXh0cy9hdXRoLWNvbnRleHRcIlxuaW1wb3J0IGFwaUNsaWVudCBmcm9tIFwiQC9saWIvYXBpLWNsaWVudFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2xhYmVsXCJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHtcbiAgRGlhbG9nLFxuICBEaWFsb2dDb250ZW50LFxuICBEaWFsb2dEZXNjcmlwdGlvbixcbiAgRGlhbG9nSGVhZGVyLFxuICBEaWFsb2dUaXRsZSxcbiAgRGlhbG9nVHJpZ2dlcixcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9kaWFsb2dcIlxuaW1wb3J0IHsgVGFibGUsIFRhYmxlQm9keSwgVGFibGVDZWxsLCBUYWJsZUhlYWQsIFRhYmxlSGVhZGVyLCBUYWJsZVJvdyB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGFibGVcIlxuaW1wb3J0IHsgU2VsZWN0LCBTZWxlY3RDb250ZW50LCBTZWxlY3RJdGVtLCBTZWxlY3RUcmlnZ2VyLCBTZWxlY3RWYWx1ZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc2VsZWN0XCJcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiXG5pbXBvcnQgeyBUYWJzLCBUYWJzQ29udGVudCwgVGFic0xpc3QsIFRhYnNUcmlnZ2VyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90YWJzXCJcbmltcG9ydCB7IEZvbGRlclN5bmMsIFBsdXMsIEVkaXQsIFRyYXNoMiwgVGVzdFR1YmUsIFRlc3RUdWJlRGlhZ29uYWwsIENoZWNrQ2lyY2xlLCBYQ2lyY2xlLCBBbGVydENpcmNsZSwgRXllLCBFeWVPZmYgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcbmltcG9ydCB7IFRvb2x0aXAgfSBmcm9tICdyZWFjdC10b29sdGlwJ1xuXG5pbnRlcmZhY2UgRGF0YVNvdXJjZSB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIHR5cGU6IFwiZG9yaXNcIiB8IFwibXlzcWxcIiB8IFwicG9zdGdyZXNxbFwiIHwgXCJvcmFjbGVcIiB8IFwic3Fsc2VydmVyXCIgfCBcInNxbGl0ZVwiXG4gIGhvc3Q6IHN0cmluZ1xuICBwb3J0OiBudW1iZXJcbiAgZGF0YWJhc2U6IHN0cmluZ1xuICB1c2VybmFtZTogc3RyaW5nXG4gIHBhc3N3b3JkOiBzdHJpbmdcbiAgc3RhdHVzOiBcImNvbm5lY3RlZFwiIHwgXCJkaXNjb25uZWN0ZWRcIiB8IFwiZXJyb3JcIlxuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xuICBjcm9uSm9iPzogc3RyaW5nXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIHVwZGF0ZWRBdDogc3RyaW5nXG4gIGxhc3RUZXN0ZWQ/OiBzdHJpbmdcbiAgbGFzdFN5bmM/OiBzdHJpbmdcbn1cblxuY29uc3QgbW9ja0RhdGFTb3VyY2VzOiBEYXRhU291cmNlW10gPSBbXG4gIHtcbiAgICBpZDogXCIxXCIsXG4gICAgbmFtZTogXCLnlJ/kuqfmlbDmja7lupNcIixcbiAgICB0eXBlOiBcIm15c3FsXCIsXG4gICAgaG9zdDogXCJwcm9kLWRiLmV4YW1wbGUuY29tXCIsXG4gICAgcG9ydDogMzMwNixcbiAgICBkYXRhYmFzZTogXCJwcm9kdWN0aW9uXCIsXG4gICAgdXNlcm5hbWU6IFwiYXBwX3VzZXJcIixcbiAgICBwYXNzd29yZDogXCJlbmNyeXB0ZWRfcGFzc3dvcmRcIixcbiAgICBzdGF0dXM6IFwiY29ubmVjdGVkXCIsXG4gICAgZGVzY3JpcHRpb246IFwi55Sf5Lqn546v5aKD5Li75pWw5o2u5bqTXCIsXG4gICAgY3JvbkpvYjogXCIwIDAgKiAqICpcIixcbiAgICBjcmVhdGVkQXQ6IFwiMjAyNC0wMS0wMVwiLFxuICAgIHVwZGF0ZWRBdDogXCIyMDI0LTAxLTE1XCIsXG4gICAgbGFzdFRlc3RlZDogXCIyMDI0LTAxLTE1IDEwOjMwOjAwXCIsXG4gICAgbGFzdFN5bmM6IFwiMjAyNC0wMS0xNSAxMDozMDowMFwiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiMlwiLFxuICAgIG5hbWU6IFwi5rWL6K+V5pWw5o2u5bqTXCIsXG4gICAgdHlwZTogXCJwb3N0Z3Jlc3FsXCIsXG4gICAgaG9zdDogXCJ0ZXN0LWRiLmV4YW1wbGUuY29tXCIsXG4gICAgcG9ydDogNTQzMixcbiAgICBkYXRhYmFzZTogXCJ0ZXN0ZGJcIixcbiAgICB1c2VybmFtZTogXCJ0ZXN0X3VzZXJcIixcbiAgICBwYXNzd29yZDogXCJlbmNyeXB0ZWRfcGFzc3dvcmRcIixcbiAgICBzdGF0dXM6IFwiY29ubmVjdGVkXCIsXG4gICAgZGVzY3JpcHRpb246IFwi5rWL6K+V546v5aKD5pWw5o2u5bqTXCIsXG4gICAgY3JvbkpvYjogXCIwIDAgKiAqICpcIixcbiAgICBjcmVhdGVkQXQ6IFwiMjAyNC0wMS0wMlwiLFxuICAgIHVwZGF0ZWRBdDogXCIyMDI0LTAxLTEwXCIsXG4gICAgbGFzdFRlc3RlZDogXCIyMDI0LTAxLTE0IDE1OjIwOjAwXCIsXG4gICAgbGFzdFN5bmM6IFwiMjAyNC0wMS0xNCAxNToyMDowMFwiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiM1wiLFxuICAgIG5hbWU6IFwi5pWw5o2u5LuT5bqTXCIsXG4gICAgdHlwZTogXCJkb3Jpc1wiLFxuICAgIGhvc3Q6IFwiZHdzLmV4YW1wbGUuY29tXCIsXG4gICAgcG9ydDogOTAzMCxcbiAgICBkYXRhYmFzZTogXCJEV1BST0RcIixcbiAgICB1c2VybmFtZTogXCJkd191c2VyXCIsXG4gICAgcGFzc3dvcmQ6IFwiZW5jcnlwdGVkX3Bhc3N3b3JkXCIsXG4gICAgc3RhdHVzOiBcImVycm9yXCIsXG4gICAgZGVzY3JpcHRpb246IFwi5pWw5o2u5LuT5bqTRG9yaXNcIixcbiAgICBjcm9uSm9iOiBcIjAgMCAqICogKlwiLFxuICAgIGNyZWF0ZWRBdDogXCIyMDI0LTAxLTAzXCIsXG4gICAgdXBkYXRlZEF0OiBcIjIwMjQtMDEtMTJcIixcbiAgICBsYXN0VGVzdGVkOiBcIjIwMjQtMDEtMTIgMDk6NDU6MDBcIixcbiAgICBsYXN0U3luYzogXCIyMDI0LTAxLTE0IDE1OjIwOjAwXCIsXG4gIH0sXG5dXG5cbmNvbnN0IGRhdGFiYXNlVHlwZXMgPSBbXG4gIHsgdmFsdWU6IFwiZG9yaXNcIiwgbGFiZWw6IFwiRG9yaXNcIiB9LFxuICB7IHZhbHVlOiBcIm15c3FsXCIsIGxhYmVsOiBcIk15U1FMXCIgfSxcbiAgeyB2YWx1ZTogXCJwb3N0Z3Jlc3FsXCIsIGxhYmVsOiBcIlBvc3RncmVTUUxcIiB9LFxuICB7IHZhbHVlOiBcIm9yYWNsZVwiLCBsYWJlbDogXCJPcmFjbGVcIiB9LFxuICB7IHZhbHVlOiBcInNxbHNlcnZlclwiLCBsYWJlbDogXCJTUUwgU2VydmVyXCIgfSxcbiAgeyB2YWx1ZTogXCJzcWxpdGVcIiwgbGFiZWw6IFwiU1FMaXRlXCIgfSxcbl1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGF0YVNvdXJjZUNvbmZpZygpIHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKClcbiAgY29uc3QgW2RhdGFTb3VyY2VzLCBzZXREYXRhU291cmNlc10gPSB1c2VTdGF0ZTxEYXRhU291cmNlW10+KFtdKVxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZShcIlwiKVxuICBjb25zdCBbaXNDcmVhdGVEaWFsb2dPcGVuLCBzZXRJc0NyZWF0ZURpYWxvZ09wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc0VkaXREaWFsb2dPcGVuLCBzZXRJc0VkaXREaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2VsZWN0ZWREYXRhU291cmNlLCBzZXRTZWxlY3RlZERhdGFTb3VyY2VdID0gdXNlU3RhdGU8RGF0YVNvdXJjZSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzaG93UGFzc3dvcmQsIHNldFNob3dQYXNzd29yZF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3Rlc3RSZXN1bHQsIHNldFRlc3RSZXN1bHRdID0gdXNlU3RhdGU8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtpc1Rlc3RpbmcsIHNldElzVGVzdGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgbmFtZTogXCJcIixcbiAgICB0eXBlOiBcIm15c3FsXCIgYXMgRGF0YVNvdXJjZVtcInR5cGVcIl0sXG4gICAgaG9zdDogXCJcIixcbiAgICBwb3J0OiAzMzA2LFxuICAgIGRhdGFiYXNlOiBcIlwiLFxuICAgIHVzZXJuYW1lOiBcIlwiLFxuICAgIHBhc3N3b3JkOiBcIlwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIlwiLFxuICB9KVxuXG4gIC8vIOWKoOi9veaVsOaNrua6kOaVsOaNrlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGxvYWREYXRhU291cmNlcyA9IGFzeW5jICgpID0+IHtcbiAgICAgIGlmICghdXNlciB8fCB1c2VyLnJvbGUgIT09ICdhZG1pbicpIHJldHVyblxuXG4gICAgICB0cnkge1xuICAgICAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcbiAgICAgICAgc2V0RXJyb3IoXCJcIilcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KCcvZGF0YXNvdXJjZXMvJylcblxuICAgICAgICAvLyDovazmjaLlkI7nq6/mlbDmja7moLzlvI/kuLrliY3nq6/moLzlvI9cbiAgICAgICAgY29uc3QgdHJhbnNmb3JtZWREYXRhU291cmNlcyA9IHJlc3BvbnNlLm1hcCgoZHM6IGFueSkgPT4gKHtcbiAgICAgICAgICBpZDogZHMuaWQudG9TdHJpbmcoKSxcbiAgICAgICAgICBuYW1lOiBkcy5uYW1lLFxuICAgICAgICAgIHR5cGU6IGRzLmRiX3R5cGUsXG4gICAgICAgICAgaG9zdDogZHMuaG9zdCxcbiAgICAgICAgICBwb3J0OiBkcy5wb3J0LFxuICAgICAgICAgIGRhdGFiYXNlOiBkcy5kYXRhYmFzZV9uYW1lLFxuICAgICAgICAgIHVzZXJuYW1lOiBkcy51c2VybmFtZSxcbiAgICAgICAgICBwYXNzd29yZDogXCIqKioqKipcIiwgLy8g5LiN5pi+56S655yf5a6e5a+G56CBXG4gICAgICAgICAgc3RhdHVzOiBkcy5pc19hY3RpdmUgPyBcImNvbm5lY3RlZFwiIDogXCJkaXNjb25uZWN0ZWRcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZHMuZGVzY3JpcHRpb24gfHwgXCJcIixcbiAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKGRzLmNyZWF0ZWRfYXQpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKGRzLnVwZGF0ZWRfYXQpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgfSkpXG5cbiAgICAgICAgc2V0RGF0YVNvdXJjZXModHJhbnNmb3JtZWREYXRhU291cmNlcylcbiAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295pWw5o2u5rqQ5aSx6LSlOicsIGVycm9yKVxuICAgICAgICBzZXRFcnJvcign5Yqg6L295pWw5o2u5rqQ5aSx6LSl77yM5L2/55So5qih5ouf5pWw5o2uJylcbiAgICAgICAgLy8g5aaC5p6cQVBJ6LCD55So5aSx6LSl77yM5L2/55So5qih5ouf5pWw5o2u5L2c5Li65ZCO5aSHXG4gICAgICAgIHNldERhdGFTb3VyY2VzKG1vY2tEYXRhU291cmNlcylcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICBsb2FkRGF0YVNvdXJjZXMoKVxuICB9LCBbdXNlcl0pXG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlRGF0YVNvdXJjZSA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdEYXRhU291cmNlOiBEYXRhU291cmNlID0ge1xuICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgIC4uLmZvcm1EYXRhLFxuICAgICAgc3RhdHVzOiBcImRpc2Nvbm5lY3RlZFwiLFxuICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoXCJUXCIpWzBdLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoXCJUXCIpWzBdLFxuICAgIH1cblxuICAgIHNldERhdGFTb3VyY2VzKFsuLi5kYXRhU291cmNlcywgbmV3RGF0YVNvdXJjZV0pXG4gICAgc2V0SXNDcmVhdGVEaWFsb2dPcGVuKGZhbHNlKVxuICAgIHJlc2V0Rm9ybSgpXG4gIH1cblxuICBjb25zdCBoYW5kbGVFZGl0RGF0YVNvdXJjZSA9ICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkRGF0YVNvdXJjZSkgcmV0dXJuXG5cbiAgICBjb25zdCB1cGRhdGVkRGF0YVNvdXJjZXMgPSBkYXRhU291cmNlcy5tYXAoKGRzKSA9PlxuICAgICAgZHMuaWQgPT09IHNlbGVjdGVkRGF0YVNvdXJjZS5pZFxuICAgICAgICA/IHtcbiAgICAgICAgICAgIC4uLmRzLFxuICAgICAgICAgICAgLi4uZm9ybURhdGEsXG4gICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdChcIlRcIilbMF0sXG4gICAgICAgICAgfVxuICAgICAgICA6IGRzLFxuICAgIClcblxuICAgIHNldERhdGFTb3VyY2VzKHVwZGF0ZWREYXRhU291cmNlcylcbiAgICBzZXRJc0VkaXREaWFsb2dPcGVuKGZhbHNlKVxuICAgIHJlc2V0Rm9ybSgpXG4gIH1cblxuICBjb25zdCBoYW5kbGVEZWxldGVEYXRhU291cmNlID0gKGlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoY29uZmlybShcIuehruWumuimgeWIoOmZpOi/meS4quaVsOaNrua6kOWQl++8n1wiKSkge1xuICAgICAgc2V0RGF0YVNvdXJjZXMoZGF0YVNvdXJjZXMuZmlsdGVyKChkcykgPT4gZHMuaWQgIT09IGlkKSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVUZXN0Q29ubmVjdGlvbiA9IGFzeW5jIChkYXRhU291cmNlPzogRGF0YVNvdXJjZSkgPT4ge1xuICAgIHNldElzVGVzdGluZyh0cnVlKVxuICAgIHNldFRlc3RSZXN1bHQobnVsbClcblxuICAgIC8vIFNpbXVsYXRlIGNvbm5lY3Rpb24gdGVzdFxuICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDIwMDApKVxuXG4gICAgY29uc3Qgc3VjY2VzcyA9IE1hdGgucmFuZG9tKCkgPiAwLjMgLy8gNzAlIHN1Y2Nlc3MgcmF0ZSBmb3IgZGVtb1xuICAgIHNldFRlc3RSZXN1bHQoe1xuICAgICAgc3VjY2VzcyxcbiAgICAgIG1lc3NhZ2U6IHN1Y2Nlc3MgPyBcIui/nuaOpea1i+ivleaIkOWKn++8geaVsOaNruW6k+i/nuaOpeato+W4uOOAglwiIDogXCLov57mjqXmtYvor5XlpLHotKXvvJrml6Dms5Xov57mjqXliLDmlbDmja7lupPvvIzor7fmo4Dmn6XphY3nva7kv6Hmga/jgIJcIixcbiAgICB9KVxuXG4gICAgaWYgKGRhdGFTb3VyY2UgJiYgc3VjY2Vzcykge1xuICAgICAgLy8gVXBkYXRlIHRoZSBkYXRhIHNvdXJjZSBzdGF0dXNcbiAgICAgIGNvbnN0IHVwZGF0ZWREYXRhU291cmNlcyA9IGRhdGFTb3VyY2VzLm1hcCgoZHMpID0+XG4gICAgICAgIGRzLmlkID09PSBkYXRhU291cmNlLmlkXG4gICAgICAgICAgPyB7XG4gICAgICAgICAgICAgIC4uLmRzLFxuICAgICAgICAgICAgICBzdGF0dXM6IFwiY29ubmVjdGVkXCIgYXMgY29uc3QsXG4gICAgICAgICAgICAgIGxhc3RUZXN0ZWQ6IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKSxcbiAgICAgICAgICAgIH1cbiAgICAgICAgICA6IGRzLFxuICAgICAgKVxuICAgICAgc2V0RGF0YVNvdXJjZXModXBkYXRlZERhdGFTb3VyY2VzKVxuICAgIH1cblxuICAgIHNldElzVGVzdGluZyhmYWxzZSlcbiAgfVxuXG4gIGNvbnN0IG9wZW5FZGl0RGlhbG9nID0gKGRhdGFTb3VyY2U6IERhdGFTb3VyY2UpID0+IHtcbiAgICBzZXRTZWxlY3RlZERhdGFTb3VyY2UoZGF0YVNvdXJjZSlcbiAgICBzZXRGb3JtRGF0YSh7XG4gICAgICBuYW1lOiBkYXRhU291cmNlLm5hbWUsXG4gICAgICB0eXBlOiBkYXRhU291cmNlLnR5cGUsXG4gICAgICBob3N0OiBkYXRhU291cmNlLmhvc3QsXG4gICAgICBwb3J0OiBkYXRhU291cmNlLnBvcnQsXG4gICAgICBkYXRhYmFzZTogZGF0YVNvdXJjZS5kYXRhYmFzZSxcbiAgICAgIHVzZXJuYW1lOiBkYXRhU291cmNlLnVzZXJuYW1lLFxuICAgICAgcGFzc3dvcmQ6IGRhdGFTb3VyY2UucGFzc3dvcmQsXG4gICAgICBkZXNjcmlwdGlvbjogZGF0YVNvdXJjZS5kZXNjcmlwdGlvbiB8fCBcIlwiLFxuICAgIH0pXG4gICAgc2V0SXNFZGl0RGlhbG9nT3Blbih0cnVlKVxuICB9XG5cbiAgY29uc3QgcmVzZXRGb3JtID0gKCkgPT4ge1xuICAgIHNldEZvcm1EYXRhKHtcbiAgICAgIG5hbWU6IFwiXCIsXG4gICAgICB0eXBlOiBcIm15c3FsXCIsXG4gICAgICBob3N0OiBcIlwiLFxuICAgICAgcG9ydDogMzMwNixcbiAgICAgIGRhdGFiYXNlOiBcIlwiLFxuICAgICAgdXNlcm5hbWU6IFwiXCIsXG4gICAgICBwYXNzd29yZDogXCJcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiLFxuICAgIH0pXG4gICAgc2V0U2VsZWN0ZWREYXRhU291cmNlKG51bGwpXG4gICAgc2V0VGVzdFJlc3VsdChudWxsKVxuICAgIHNldFNob3dQYXNzd29yZChmYWxzZSlcbiAgfVxuXG4gIGNvbnN0IGdldFN0YXR1c0JhZGdlID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgXCJjb25uZWN0ZWRcIjpcbiAgICAgICAgcmV0dXJuIDxCYWRnZSBjbGFzc05hbWU9XCJiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDBcIj7lt7Lov57mjqU8L0JhZGdlPlxuICAgICAgY2FzZSBcImRpc2Nvbm5lY3RlZFwiOlxuICAgICAgICByZXR1cm4gPEJhZGdlIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIj7mnKrov57mjqU8L0JhZGdlPlxuICAgICAgY2FzZSBcImVycm9yXCI6XG4gICAgICAgIHJldHVybiA8QmFkZ2UgdmFyaWFudD1cImRlc3RydWN0aXZlXCI+6L+e5o6l6ZSZ6K+vPC9CYWRnZT5cbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIj7mnKrnn6U8L0JhZGdlPlxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFN0YXR1c0ljb24gPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSBcImNvbm5lY3RlZFwiOlxuICAgICAgICByZXR1cm4gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgY2FzZSBcImRpc2Nvbm5lY3RlZFwiOlxuICAgICAgICByZXR1cm4gPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC15ZWxsb3ctNjAwXCIgLz5cbiAgICAgIGNhc2UgXCJlcnJvclwiOlxuICAgICAgICByZXR1cm4gPFhDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC02MDBcIiAvPlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIG51bGxcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXREZWZhdWx0UG9ydCA9ICh0eXBlOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgIGNhc2UgXCJkb3Jpc1wiOlxuICAgICAgICByZXR1cm4gOTAzMFxuICAgICAgY2FzZSBcIm15c3FsXCI6XG4gICAgICAgIHJldHVybiAzMzA2XG4gICAgICBjYXNlIFwicG9zdGdyZXNxbFwiOlxuICAgICAgICByZXR1cm4gNTQzMlxuICAgICAgY2FzZSBcIm9yYWNsZVwiOlxuICAgICAgICByZXR1cm4gMTUyMVxuICAgICAgY2FzZSBcInNxbHNlcnZlclwiOlxuICAgICAgICByZXR1cm4gMTQzM1xuICAgICAgY2FzZSBcInNxbGl0ZVwiOlxuICAgICAgICByZXR1cm4gMFxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIDMzMDZcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGRcIj7mlbDmja7mupDphY3nva48L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj7nrqHnkIbmlbDmja7lupPov57mjqXlkozphY3nva7kv6Hmga88L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8RGlhbG9nIG9wZW49e2lzQ3JlYXRlRGlhbG9nT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRJc0NyZWF0ZURpYWxvZ09wZW59PlxuICAgICAgICAgIDxEaWFsb2dUcmlnZ2VyIGFzQ2hpbGQ+XG4gICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHJlc2V0Rm9ybSgpfT5cbiAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAg5paw5bu65pWw5o2u5rqQXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L0RpYWxvZ1RyaWdnZXI+XG4gICAgICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwibWF4LXctMnhsXCI+XG4gICAgICAgICAgICA8RGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICA8RGlhbG9nVGl0bGU+5Yib5bu65pWw5o2u5rqQPC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICAgICAgPERpYWxvZ0Rlc2NyaXB0aW9uPumFjee9ruaWsOeahOaVsOaNruW6k+i/nuaOpeS/oeaBrzwvRGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cblxuICAgICAgICAgICAgPFRhYnMgZGVmYXVsdFZhbHVlPVwiYmFzaWNcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy0yXCI+XG4gICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiYmFzaWNcIj7ln7rmnKzkv6Hmga88L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImNvbm5lY3Rpb25cIj7ov57mjqXphY3nva48L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgICA8L1RhYnNMaXN0PlxuXG4gICAgICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cImJhc2ljXCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwibmFtZVwiPuaVsOaNrua6kOWQjeensDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgaWQ9XCJuYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgbmFtZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl5pWw5o2u5rqQ5ZCN56ewXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ0eXBlXCI+5pWw5o2u5bqT57G75Z6LPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnR5cGV9XG4gICAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZTogRGF0YVNvdXJjZVtcInR5cGVcIl0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBzZXRGb3JtRGF0YSh7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5mb3JtRGF0YSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6IHZhbHVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgcG9ydDogZ2V0RGVmYXVsdFBvcnQodmFsdWUpLFxuICAgICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSAvPlxuICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgIHtkYXRhYmFzZVR5cGVzLm1hcCgodHlwZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0ga2V5PXt0eXBlLnZhbHVlfSB2YWx1ZT17dHlwZS52YWx1ZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0eXBlLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImRlc2NyaXB0aW9uXCI+5o+P6L+wPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICBpZD1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXmlbDmja7mupDmj4/ov7DvvIjlj6/pgInvvIlcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJjb25uZWN0aW9uXCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImhvc3RcIj7kuLvmnLrlnLDlnYA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cImhvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5ob3N0fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgaG9zdDogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJsb2NhbGhvc3RcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInBvcnRcIj7nq6/lj6M8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cInBvcnRcIlxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wb3J0fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgcG9ydDogTnVtYmVyLnBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB9KX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZGF0YWJhc2VcIj7mlbDmja7lupPlkI08L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgIGlkPVwiZGF0YWJhc2VcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZGF0YWJhc2V9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgZGF0YWJhc2U6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIui+k+WFpeaVsOaNruW6k+WQjeensFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInVzZXJuYW1lXCI+55So5oi35ZCNPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ1c2VybmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnVzZXJuYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgdXNlcm5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl55So5oi35ZCNXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwYXNzd29yZFwiPuWvhueggTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd1Bhc3N3b3JkID8gXCJ0ZXh0XCIgOiBcInBhc3N3b3JkXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGFzc3dvcmR9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIHBhc3N3b3JkOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl5a+G56CBXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0wIHRvcC0wIGgtZnVsbCBweC0zIHB5LTIgaG92ZXI6YmctdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1Bhc3N3b3JkKCFzaG93UGFzc3dvcmQpfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzaG93UGFzc3dvcmQgPyA8RXllT2ZmIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPiA6IDxFeWUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L1RhYnNDb250ZW50PlxuICAgICAgICAgICAgPC9UYWJzPlxuXG4gICAgICAgICAgICB7dGVzdFJlc3VsdCAmJiAoXG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgcm91bmRlZC1sZyBib3JkZXIgdGV4dC1zbSAke3Rlc3RSZXN1bHQuc3VjY2VzcyA/IFwiYmctZ3JlZW4tNTAgYm9yZGVyLWdyZWVuLTIwMCB0ZXh0LWdyZWVuLTgwMFwiIDogXCJiZy1yZWQtNTAgYm9yZGVyLXJlZC0yMDAgdGV4dC1yZWQtODAwXCJ9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIHt0ZXN0UmVzdWx0LnN1Y2Nlc3MgPyA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPn1cbiAgICAgICAgICAgICAgICAgIDxzcGFuPnt0ZXN0UmVzdWx0Lm1lc3NhZ2V9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTIgbXQtNFwiPlxuICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gc2V0SXNDcmVhdGVEaWFsb2dPcGVuKGZhbHNlKX0+XG4gICAgICAgICAgICAgICAg5Y+W5raIXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgLy8gdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgLy8gdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVRlc3RDb25uZWN0aW9uKCl9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e1xuICAgICAgICAgICAgICAgICAgaXNUZXN0aW5nIHx8XG4gICAgICAgICAgICAgICAgICAhZm9ybURhdGEubmFtZS50cmltKCkgfHxcbiAgICAgICAgICAgICAgICAgICFmb3JtRGF0YS5ob3N0LnRyaW0oKSB8fFxuICAgICAgICAgICAgICAgICAgIWZvcm1EYXRhLmRhdGFiYXNlLnRyaW0oKSB8fFxuICAgICAgICAgICAgICAgICAgIWZvcm1EYXRhLnVzZXJuYW1lLnRyaW0oKSB8fFxuICAgICAgICAgICAgICAgICAgIWZvcm1EYXRhLnBhc3N3b3JkLnRyaW0oKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc1Rlc3RpbmcgPyBcIua1i+ivleS4rS4uLlwiIDogXCLmtYvor5Xov57mjqVcIn1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDcmVhdGVEYXRhU291cmNlfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtcbiAgICAgICAgICAgICAgICAgICFmb3JtRGF0YS5uYW1lLnRyaW0oKSB8fFxuICAgICAgICAgICAgICAgICAgIWZvcm1EYXRhLmhvc3QudHJpbSgpIHx8XG4gICAgICAgICAgICAgICAgICAhZm9ybURhdGEuZGF0YWJhc2UudHJpbSgpIHx8XG4gICAgICAgICAgICAgICAgICAhZm9ybURhdGEudXNlcm5hbWUudHJpbSgpIHx8XG4gICAgICAgICAgICAgICAgICAhZm9ybURhdGEucGFzc3dvcmQudHJpbSgpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5Yib5bu65pWw5o2u5rqQXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgICAgICA8L0RpYWxvZz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8Q2FyZD5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAg5pWw5o2u5rqQ5YiX6KGoXG4gICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbj7ns7vnu5/kuK3nmoTmiYDmnInmlbDmja7mupDov57mjqU8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgPFRhYmxlPlxuICAgICAgICAgICAgPFRhYmxlSGVhZGVyPlxuICAgICAgICAgICAgICA8VGFibGVSb3c+XG4gICAgICAgICAgICAgICAgPFRhYmxlSGVhZD7mlbDmja7mupA8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICA8VGFibGVIZWFkPuexu+WeizwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+54q25oCBPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgPFRhYmxlSGVhZD7lkIzmraXkvZzkuJpDcm9uPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgPFRhYmxlSGVhZD7mnIDov5HlkIzmraXml7bpl7Q8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICA8VGFibGVIZWFkPuaTjeS9nDwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgPC9UYWJsZUhlYWRlcj5cbiAgICAgICAgICAgIDxUYWJsZUJvZHk+XG4gICAgICAgICAgICAgIHtkYXRhU291cmNlcy5tYXAoKGRhdGFTb3VyY2UpID0+IChcbiAgICAgICAgICAgICAgICA8VGFibGVSb3cga2V5PXtkYXRhU291cmNlLmlkfT5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57ZGF0YVNvdXJjZS5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e2RhdGFTb3VyY2UuZGVzY3JpcHRpb259PC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj57ZGF0YVNvdXJjZS50eXBlfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNJY29uKGRhdGFTb3VyY2Uuc3RhdHVzKX1cbiAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzQmFkZ2UoZGF0YVNvdXJjZS5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e2RhdGFTb3VyY2UuY3JvbkpvYn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPntkYXRhU291cmNlLmxhc3RTeW5jID8gbmV3IERhdGUoZGF0YVNvdXJjZS5sYXN0U3luYykudG9Mb2NhbGVTdHJpbmcoKSA6IFwiTi9BXCJ9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiIGRhdGEtdG9vbHRpcC1pZD1cInRvb2x0aXAtdGVzdFwiIGRhdGEtdG9vbHRpcC1jb250ZW50PVwi5rWL6K+V6L+e5o6lXCIgb25DbGljaz17KCkgPT4gaGFuZGxlVGVzdENvbm5lY3Rpb24oZGF0YVNvdXJjZSl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGVzdFR1YmVEaWFnb25hbCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgaWQ9XCJ0b29sdGlwLXRlc3RcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwic21cIiBkYXRhLXRvb2x0aXAtaWQ9XCJ0b29sdGlwLXN5bmNcIiBkYXRhLXRvb2x0aXAtY29udGVudD1cIuWFg+aVsOaNruWQjOatpVwiIG9uQ2xpY2s9eygpID0+IGhhbmRsZVRlc3RDb25uZWN0aW9uKGRhdGFTb3VyY2UpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvbGRlclN5bmMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGlkPVwidG9vbHRpcC1zeW5jXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgZGF0YS10b29sdGlwLWlkPVwidG9vbHRpcC1lZGl0XCIgZGF0YS10b29sdGlwLWNvbnRlbnQ9XCLnvJbovpFcIiBvbkNsaWNrPXsoKSA9PiBvcGVuRWRpdERpYWxvZyhkYXRhU291cmNlKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxFZGl0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBpZD1cInRvb2x0aXAtZWRpdFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiIGRhdGEtdG9vbHRpcC1pZD1cInRvb2x0aXAtdHJhc2hcIiBkYXRhLXRvb2x0aXAtY29udGVudD1cIuWIoOmZpFwiIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZURhdGFTb3VyY2UoZGF0YVNvdXJjZS5pZCl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGlkPVwidG9vbHRpcC10cmFzaFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1RhYmxlQm9keT5cbiAgICAgICAgICA8L1RhYmxlPlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuXG4gICAgICA8RGlhbG9nIG9wZW49e2lzRWRpdERpYWxvZ09wZW59IG9uT3BlbkNoYW5nZT17c2V0SXNFZGl0RGlhbG9nT3Blbn0+XG4gICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cIm1heC13LTJ4bFwiPlxuICAgICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICA8RGlhbG9nVGl0bGU+57yW6L6R5pWw5o2u5rqQPC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICAgIDxEaWFsb2dEZXNjcmlwdGlvbj7kv67mlLnmlbDmja7lupPov57mjqXkv6Hmga88L0RpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuXG4gICAgICAgICAgPFRhYnMgZGVmYXVsdFZhbHVlPVwiYmFzaWNcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxUYWJzTGlzdCBjbGFzc05hbWU9XCJncmlkIHctZnVsbCBncmlkLWNvbHMtMlwiPlxuICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJiYXNpY1wiPuWfuuacrOS/oeaBrzwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImNvbm5lY3Rpb25cIj7ov57mjqXphY3nva48L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiYmFzaWNcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm5hbWVcIj7mlbDmja7mupDlkI3np7A8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgaWQ9XCJuYW1lXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl5pWw5o2u5rqQ5ZCN56ewXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidHlwZVwiPuaVsOaNruW6k+exu+WeizwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnR5cGV9XG4gICAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsodmFsdWU6IERhdGFTb3VyY2VbXCJ0eXBlXCJdKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKHtcbiAgICAgICAgICAgICAgICAgICAgICAuLi5mb3JtRGF0YSxcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlOiB2YWx1ZSxcbiAgICAgICAgICAgICAgICAgICAgICBwb3J0OiBnZXREZWZhdWx0UG9ydCh2YWx1ZSksXG4gICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgLz5cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICB7ZGF0YWJhc2VUeXBlcy5tYXAoKHR5cGUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSBrZXk9e3R5cGUudmFsdWV9IHZhbHVlPXt0eXBlLnZhbHVlfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt0eXBlLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJkZXNjcmlwdGlvblwiPuaPj+i/sDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICBpZD1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXmlbDmja7mupDmj4/ov7DvvIjlj6/pgInvvIlcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiY29ubmVjdGlvblwiIGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJob3N0XCI+5Li75py65Zyw5Z2APC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBpZD1cImhvc3RcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuaG9zdH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBob3N0OiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJsb2NhbGhvc3RcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwb3J0XCI+56uv5Y+jPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBpZD1cInBvcnRcIlxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBvcnR9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgcG9ydDogTnVtYmVyLnBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB9KX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJkYXRhYmFzZVwiPuaVsOaNruW6k+WQjTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBpZD1cImRhdGFiYXNlXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kYXRhYmFzZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgZGF0YWJhc2U6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXmlbDmja7lupPlkI3np7BcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInVzZXJuYW1lXCI+55So5oi35ZCNPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBpZD1cInVzZXJuYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnVzZXJuYW1lfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIHVzZXJuYW1lOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXnlKjmiLflkI1cIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwYXNzd29yZFwiPuWvhueggTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9e3Nob3dQYXNzd29yZCA/IFwidGV4dFwiIDogXCJwYXNzd29yZFwifVxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wYXNzd29yZH1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIHBhc3N3b3JkOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIui+k+WFpeWvhueggVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCB0b3AtMCBoLWZ1bGwgcHgtMyBweS0yIGhvdmVyOmJnLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93UGFzc3dvcmQoIXNob3dQYXNzd29yZCl9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7c2hvd1Bhc3N3b3JkID8gPEV5ZU9mZiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz4gOiA8RXllIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPn1cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L1RhYnNDb250ZW50PlxuICAgICAgICAgIDwvVGFicz5cblxuICAgICAgICAgIHt0ZXN0UmVzdWx0ICYmIChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIHJvdW5kZWQtbGcgYm9yZGVyIHRleHQtc20gJHt0ZXN0UmVzdWx0LnN1Y2Nlc3MgPyBcImJnLWdyZWVuLTUwIGJvcmRlci1ncmVlbi0yMDAgdGV4dC1ncmVlbi04MDBcIiA6IFwiYmctcmVkLTUwIGJvcmRlci1yZWQtMjAwIHRleHQtcmVkLTgwMFwifWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICB7dGVzdFJlc3VsdC5zdWNjZXNzID8gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPiA6IDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz59XG4gICAgICAgICAgICAgICAgPHNwYW4+e3Rlc3RSZXN1bHQubWVzc2FnZX08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTIgbXQtNFwiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUZXN0Q29ubmVjdGlvbigpfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNUZXN0aW5nIHx8ICFmb3JtRGF0YS5ob3N0IHx8ICFmb3JtRGF0YS5kYXRhYmFzZX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFRlc3RUdWJlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIHtpc1Rlc3RpbmcgPyBcIua1i+ivleS4rS4uLlwiIDogXCLmtYvor5Xov57mjqVcIn1cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9eygpID0+IHNldElzRWRpdERpYWxvZ09wZW4oZmFsc2UpfT5cbiAgICAgICAgICAgICAg5Y+W5raIXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlRWRpdERhdGFTb3VyY2V9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtcbiAgICAgICAgICAgICAgICAhZm9ybURhdGEubmFtZS50cmltKCkgfHxcbiAgICAgICAgICAgICAgICAhZm9ybURhdGEuaG9zdC50cmltKCkgfHxcbiAgICAgICAgICAgICAgICAhZm9ybURhdGEuZGF0YWJhc2UudHJpbSgpIHx8XG4gICAgICAgICAgICAgICAgIWZvcm1EYXRhLnVzZXJuYW1lLnRyaW0oKSB8fFxuICAgICAgICAgICAgICAgICFmb3JtRGF0YS5wYXNzd29yZC50cmltKClcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDkv53lrZhcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG4gICAgICA8L0RpYWxvZz5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXV0aCIsImFwaUNsaWVudCIsIkJ1dHRvbiIsIklucHV0IiwiTGFiZWwiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQmFkZ2UiLCJEaWFsb2ciLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nRGVzY3JpcHRpb24iLCJEaWFsb2dIZWFkZXIiLCJEaWFsb2dUaXRsZSIsIkRpYWxvZ1RyaWdnZXIiLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2VsbCIsIlRhYmxlSGVhZCIsIlRhYmxlSGVhZGVyIiwiVGFibGVSb3ciLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsIlRleHRhcmVhIiwiVGFicyIsIlRhYnNDb250ZW50IiwiVGFic0xpc3QiLCJUYWJzVHJpZ2dlciIsIkZvbGRlclN5bmMiLCJQbHVzIiwiRWRpdCIsIlRyYXNoMiIsIlRlc3RUdWJlIiwiVGVzdFR1YmVEaWFnb25hbCIsIkNoZWNrQ2lyY2xlIiwiWENpcmNsZSIsIkFsZXJ0Q2lyY2xlIiwiRXllIiwiRXllT2ZmIiwiVG9vbHRpcCIsIm1vY2tEYXRhU291cmNlcyIsImlkIiwibmFtZSIsInR5cGUiLCJob3N0IiwicG9ydCIsImRhdGFiYXNlIiwidXNlcm5hbWUiLCJwYXNzd29yZCIsInN0YXR1cyIsImRlc2NyaXB0aW9uIiwiY3JvbkpvYiIsImNyZWF0ZWRBdCIsInVwZGF0ZWRBdCIsImxhc3RUZXN0ZWQiLCJsYXN0U3luYyIsImRhdGFiYXNlVHlwZXMiLCJ2YWx1ZSIsImxhYmVsIiwiRGF0YVNvdXJjZUNvbmZpZyIsInVzZXIiLCJkYXRhU291cmNlcyIsInNldERhdGFTb3VyY2VzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImlzQ3JlYXRlRGlhbG9nT3BlbiIsInNldElzQ3JlYXRlRGlhbG9nT3BlbiIsImlzRWRpdERpYWxvZ09wZW4iLCJzZXRJc0VkaXREaWFsb2dPcGVuIiwic2VsZWN0ZWREYXRhU291cmNlIiwic2V0U2VsZWN0ZWREYXRhU291cmNlIiwic2hvd1Bhc3N3b3JkIiwic2V0U2hvd1Bhc3N3b3JkIiwidGVzdFJlc3VsdCIsInNldFRlc3RSZXN1bHQiLCJpc1Rlc3RpbmciLCJzZXRJc1Rlc3RpbmciLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwibG9hZERhdGFTb3VyY2VzIiwicm9sZSIsInJlc3BvbnNlIiwiZ2V0IiwidHJhbnNmb3JtZWREYXRhU291cmNlcyIsIm1hcCIsImRzIiwidG9TdHJpbmciLCJkYl90eXBlIiwiZGF0YWJhc2VfbmFtZSIsImlzX2FjdGl2ZSIsIkRhdGUiLCJjcmVhdGVkX2F0IiwidG9JU09TdHJpbmciLCJzcGxpdCIsInVwZGF0ZWRfYXQiLCJjb25zb2xlIiwiaGFuZGxlQ3JlYXRlRGF0YVNvdXJjZSIsIm5ld0RhdGFTb3VyY2UiLCJub3ciLCJyZXNldEZvcm0iLCJoYW5kbGVFZGl0RGF0YVNvdXJjZSIsInVwZGF0ZWREYXRhU291cmNlcyIsImhhbmRsZURlbGV0ZURhdGFTb3VyY2UiLCJjb25maXJtIiwiZmlsdGVyIiwiaGFuZGxlVGVzdENvbm5lY3Rpb24iLCJkYXRhU291cmNlIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0Iiwic3VjY2VzcyIsIk1hdGgiLCJyYW5kb20iLCJtZXNzYWdlIiwidG9Mb2NhbGVTdHJpbmciLCJvcGVuRWRpdERpYWxvZyIsImdldFN0YXR1c0JhZGdlIiwiY2xhc3NOYW1lIiwidmFyaWFudCIsImdldFN0YXR1c0ljb24iLCJnZXREZWZhdWx0UG9ydCIsImRpdiIsImgyIiwicCIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJhc0NoaWxkIiwib25DbGljayIsImRlZmF1bHRWYWx1ZSIsImh0bWxGb3IiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsIm9uVmFsdWVDaGFuZ2UiLCJOdW1iZXIiLCJwYXJzZUludCIsInNpemUiLCJzcGFuIiwiZGlzYWJsZWQiLCJ0cmltIiwiZGF0YS10b29sdGlwLWlkIiwiZGF0YS10b29sdGlwLWNvbnRlbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/data-source-config.tsx\n"));

/***/ })

});