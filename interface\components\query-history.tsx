"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { Clock, Star, StarOff } from "lucide-react"
// Removed Tabs, TabsContent, TabsList, TabsTrigger imports as they are no longer needed

// Mock history data
const mockHistory = [
  {
    id: "h1",
    query: "查询过去30天销售额最高的10个产品",
    timestamp: "2023-07-15 14:30",
    isFavorite: true,
  },
  {
    id: "h2",
    query: "统计各地区客户数量分布",
    timestamp: "2023-07-14 10:15",
    isFavorite: false,
  },
  {
    id: "h3",
    query: "分析不同产品类别的销售趋势",
    timestamp: "2023-07-13 16:45",
    isFavorite: true,
  },
  {
    id: "h4",
    query: "查询客户购买频率最高的产品",
    timestamp: "2023-07-12 09:20",
    isFavorite: false,
  },
  {
    id: "h5",
    query: "统计每月销售额变化情况",
    timestamp: "2023-07-11 11:05",
    isFavorite: false,
  },
]

export default function QueryHistory() {
  const { user } = useAuth()
  const [history, setHistory] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  // 加载查询历史
  useEffect(() => {
    const loadHistory = async () => {
      if (!user) return

      try {
        setIsLoading(true)
        const apiClient = (await import("@/lib/api-client")).default
        const response = await apiClient.get('/query/history')

        // 转换后端数据格式
        const transformedHistory = response.map((item: any) => ({
          id: item.id.toString(),
          query: item.natural_query || item.generated_sql,
          timestamp: new Date(item.created_at).toLocaleString(),
          isFavorite: false, // 这里需要根据实际的收藏功能来设置
        }))

        setHistory(transformedHistory)
      } catch (error) {
        console.error('加载查询历史失败:', error)
        // 如果API调用失败，使用模拟数据作为后备
        setHistory(mockHistory)
      } finally {
        setIsLoading(false)
      }
    }

    loadHistory()
  }, [user])

  const toggleFavorite = (id: string) => {
    setHistory((prev) => prev.map((item) => (item.id === id ? { ...item, isFavorite: !item.isFavorite } : item)))
  }

  const filteredHistory = history

  // 加载状态
  if (isLoading) {
    return (
      <div className="p-2">
        <div className="text-sm font-medium text-gray-500 mb-2 px-2">历史查询记录</div>
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-3 border border-gray-100 rounded-md animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-2">
      <div className="text-sm font-medium text-gray-500 mb-2 px-2">历史查询记录</div> {/* Added a title */}
      <div className="space-y-2">
        {filteredHistory.map((item) => (
          <div key={item.id} className="p-3 text-sm border border-gray-100 rounded-md hover:bg-gray-50 cursor-pointer">
            <div className="flex justify-between items-start">
              <div className="font-medium line-clamp-2">{item.query}</div>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  toggleFavorite(item.id)
                }}
                className="ml-2 flex-shrink-0"
              >
                {item.isFavorite ? (
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                ) : (
                  <StarOff className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
            <div className="flex items-center mt-2 text-xs text-gray-500">
              <Clock className="h-3 w-3 mr-1" />
              {item.timestamp}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
