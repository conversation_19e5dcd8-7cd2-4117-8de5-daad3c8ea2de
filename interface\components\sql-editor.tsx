"use client"

import { useEffect, useRef, useCallback } from "react"

interface SqlEditorProps {
  value: string
  onChange?: (value: string) => void
  placeholder?: string
  readOnly?: boolean
  height?: string
  darkMode?: boolean
}

export default function SqlEditor({
  value,
  onChange,
  placeholder,
  readOnly = false,
  height = "auto",
  darkMode = false,
}: SqlEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)

  // Apply syntax highlighting
  const highlightSql = useCallback(
    (sql: string) => {
      if (!sql) return ""

      const keywordColor = darkMode ? "#60A5FA" : "#2563EB" // blue-400 : blue-600
      const functionColor = darkMode ? "#A78BFA" : "#7C3AED" // violet-400 : violet-600
      const stringColor = darkMode ? "#34D399" : "#059669" // emerald-400 : emerald-600
      const numberColor = darkMode ? "#FB923C" : "#EA580C" // orange-400 : orange-600
      const operatorColor = darkMode ? "#9CA3AF" : "#6B7280" // gray-400 : gray-500

      return sql
        .replace(
          /\b(SELECT|FROM|WHERE|GROUP BY|ORDER BY|LIMIT|JOIN|ON|AND|OR|AS|HAVING|UNION|ALL|INSERT|UPDATE|DELETE|CREATE|TABLE|INDEX|VIEW|PROCEDURE|FUNCTION|TRIGGER|CASE|WHEN|THEN|ELSE|END|IS|NULL|NOT|IN|BETWEEN|LIKE|EXISTS|ANY|SOME|ALL)\b/gi,
          `<span style="color: ${keywordColor}; font-weight: 600;">$1</span>`,
        )
        .replace(
          /\b(SUM|COUNT|AVG|MIN|MAX|DISTINCT|TOP)\b/gi,
          `<span style="color: ${functionColor}; font-weight: 600;">$1</span>`,
        )
        .replace(/('[^']*')/g, `<span style="color: ${stringColor};">$1</span>`)
        .replace(/\b(\d+)\b/g, `<span style="color: ${numberColor};">$1</span>`)
        .replace(/(=|<|>|\*|,|;)/g, `<span style="color: ${operatorColor};">$1</span>`)
    },
    [darkMode],
  )

  // Update content when value changes
  useEffect(() => {
    if (editorRef.current) {
      const currentHtml = editorRef.current.innerHTML
      const newHtml = highlightSql(value) || (placeholder ? `<span class="text-gray-400">${placeholder}</span>` : "")

      // Only update if content has actually changed to prevent cursor jump
      if (currentHtml !== newHtml) {
        editorRef.current.innerHTML = newHtml
      }
    }
  }, [value, placeholder, highlightSql])

  // Handle input changes from contentEditable div
  const handleInput = useCallback(() => {
    if (editorRef.current && onChange) {
      // Get plain text content, remove placeholder if present
      const textContent = editorRef.current.innerText
      if (textContent === placeholder) {
        onChange("")
      } else {
        onChange(textContent)
      }
    }
  }, [onChange, placeholder])

  // Handle focus/blur to manage placeholder visibility
  const handleFocus = useCallback(() => {
    if (editorRef.current && editorRef.current.innerText === placeholder) {
      editorRef.current.innerHTML = "" // Clear placeholder on focus
    }
  }, [placeholder])

  const handleBlur = useCallback(() => {
    if (editorRef.current && editorRef.current.innerText.trim() === "" && placeholder) {
      editorRef.current.innerHTML = `<span class="text-gray-400">${placeholder}</span>` // Restore placeholder if empty
    }
  }, [placeholder])

  const baseClasses = darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-900"
  const readOnlyClasses = readOnly ? "bg-gray-50 cursor-not-allowed" : ""

  return (
    <div
      ref={editorRef}
      contentEditable={!readOnly}
      onInput={handleInput}
      onFocus={handleFocus}
      onBlur={handleBlur}
      className={`font-mono text-sm p-3 whitespace-pre-wrap overflow-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pt-4 pl-4 pr-4 pb-4 ${baseClasses} ${readOnlyClasses}`} // Changed pb-3.5 to pb-[56px]
      style={{ height, minHeight: "80px" }} // Ensure a minimum height
      suppressContentEditableWarning={true} // Suppress React warning for contentEditable
    >
      {/* Initial content set by useEffect */}
    </div>
  )
}
