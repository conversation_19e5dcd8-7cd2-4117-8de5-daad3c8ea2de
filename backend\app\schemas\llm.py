from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class LLMProviderCreate(BaseModel):
    name: str
    provider_type: str  # openai, anthropic, groq
    api_key: str
    base_url: Optional[str] = None
    models: Optional[List[str]] = None
    config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = True
    is_default: Optional[bool] = False

class LLMProviderUpdate(BaseModel):
    name: Optional[str] = None
    provider_type: Optional[str] = None
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    models: Optional[List[str]] = None
    config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None

class LLMProviderResponse(BaseModel):
    id: int
    name: str
    provider_type: str
    base_url: Optional[str]
    models: Optional[List[str]]
    config: Optional[Dict[str, Any]]
    is_active: bool
    is_default: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True