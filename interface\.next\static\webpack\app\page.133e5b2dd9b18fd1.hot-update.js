"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/favorite-queries.tsx":
/*!*****************************************!*\
  !*** ./components/favorite-queries.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FavoriteQueries)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst mockFavoriteQueries = [\n    {\n        id: \"1\",\n        title: \"销售额前10的产品\",\n        query: \"SELECT product_name, SUM(sales_amount) as total_sales FROM sales_data GROUP BY product_name ORDER BY total_sales DESC LIMIT 10\",\n        createdAt: \"2024-01-15\",\n        tags: [\n            \"销售\",\n            \"产品\"\n        ]\n    },\n    {\n        id: \"2\",\n        title: \"月度用户增长趋势\",\n        query: \"SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as new_users FROM users GROUP BY month ORDER BY month\",\n        createdAt: \"2024-01-14\",\n        tags: [\n            \"用户\",\n            \"增长\"\n        ]\n    },\n    {\n        id: \"3\",\n        title: \"订单状态分布\",\n        query: \"SELECT status, COUNT(*) as count FROM orders GROUP BY status\",\n        createdAt: \"2024-01-13\",\n        tags: [\n            \"订单\",\n            \"状态\"\n        ]\n    }\n];\nfunction FavoriteQueries() {\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [favoriteQueries, setFavoriteQueries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 加载收藏查询\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FavoriteQueries.useEffect\": ()=>{\n            const loadFavorites = {\n                \"FavoriteQueries.useEffect.loadFavorites\": async ()=>{\n                    if (!user) return;\n                    try {\n                        setIsLoading(true);\n                        const apiClient = (await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\"))).default;\n                        const response = await apiClient.get('/favorites/');\n                        // 转换后端数据格式\n                        const transformedFavorites = response.map({\n                            \"FavoriteQueries.useEffect.loadFavorites.transformedFavorites\": (item)=>({\n                                    id: item.id.toString(),\n                                    title: item.name,\n                                    query: item.query_id ? '' : '',\n                                    createdAt: new Date(item.created_at).toISOString().split('T')[0],\n                                    tags: item.tags ? JSON.parse(item.tags).tags || [] : []\n                                })\n                        }[\"FavoriteQueries.useEffect.loadFavorites.transformedFavorites\"]);\n                        setFavoriteQueries(transformedFavorites);\n                    } catch (error) {\n                        console.error('加载收藏查询失败:', error);\n                        // 如果API调用失败，使用模拟数据作为后备\n                        setFavoriteQueries(mockFavoriteQueries);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FavoriteQueries.useEffect.loadFavorites\"];\n            loadFavorites();\n        }\n    }[\"FavoriteQueries.useEffect\"], [\n        user\n    ]);\n    const filteredQueries = favoriteQueries.filter((query)=>query.title.toLowerCase().includes(searchTerm.toLowerCase()) || query.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n    // 加载状态\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2 px-2\",\n                    children: \"收藏的成功查询\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border border-gray-200 rounded-lg animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm font-medium text-gray-500 mb-2 px-2\",\n                children: \"收藏的成功查询\"\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: [\n                    filteredQueries.map((query)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-sm text-gray-900 line-clamp-1\",\n                                            children: query.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4 text-yellow-500 fill-current flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 mb-2 line-clamp-2 font-mono bg-gray-50 p-2 rounded\",\n                                    children: query.query\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: query.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\",\n                                                    children: tag\n                                                }, tag, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, this),\n                                                query.createdAt\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, query.id, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)),\n                    filteredQueries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-2 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"暂无收藏的查询\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400 mt-1\",\n                                children: searchTerm ? \"未找到匹配的查询\" : \"开始收藏您常用的查询吧\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s(FavoriteQueries, \"wg63UEV1XwuhtuxvegIrohi2FfI=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = FavoriteQueries;\nvar _c;\n$RefreshReg$(_c, \"FavoriteQueries\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/favorite-queries.tsx\n"));

/***/ })

});