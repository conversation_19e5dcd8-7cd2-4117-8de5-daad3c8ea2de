"""
系统设置数据模型

用于存储和管理系统的各种配置设置
"""

from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, JSON
from sqlalchemy.sql import func
from app.core.database import Base


class SystemSetting(Base):
    """系统设置模型"""
    __tablename__ = "system_settings"

    id = Column(Integer, primary_key=True, index=True)
    category = Column(String(50), nullable=False, index=True, comment="设置分类")
    key = Column(String(100), nullable=False, unique=True, index=True, comment="设置键名")
    value = Column(Text, nullable=True, comment="设置值")
    value_type = Column(String(20), nullable=False, default="string", comment="值类型: string, integer, boolean, json")
    description = Column(String(255), nullable=True, comment="设置描述")
    is_system = Column(Boolean, default=False, comment="是否为系统内置设置")
    is_editable = Column(Boolean, default=True, comment="是否可编辑")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<SystemSetting(key='{self.key}', category='{self.category}', value='{self.value}')>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "category": self.category,
            "key": self.key,
            "value": self.get_typed_value(),
            "value_type": self.value_type,
            "description": self.description,
            "is_system": self.is_system,
            "is_editable": self.is_editable,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def get_typed_value(self):
        """根据值类型返回正确类型的值"""
        if self.value is None:
            return None
        
        if self.value_type == "boolean":
            return self.value.lower() in ("true", "1", "yes", "on")
        elif self.value_type == "integer":
            try:
                return int(self.value)
            except (ValueError, TypeError):
                return 0
        elif self.value_type == "float":
            try:
                return float(self.value)
            except (ValueError, TypeError):
                return 0.0
        elif self.value_type == "json":
            try:
                import json
                return json.loads(self.value)
            except (ValueError, TypeError):
                return {}
        else:  # string
            return self.value

    def set_typed_value(self, value):
        """根据值类型设置正确格式的值"""
        if value is None:
            self.value = None
            return

        if self.value_type == "boolean":
            self.value = "true" if value else "false"
        elif self.value_type in ("integer", "float"):
            self.value = str(value)
        elif self.value_type == "json":
            import json
            self.value = json.dumps(value, ensure_ascii=False)
        else:  # string
            self.value = str(value)


# 默认系统设置配置
DEFAULT_SETTINGS = [
    # 常规设置
    {
        "category": "general",
        "key": "app_name",
        "value": "NL2SQL 数据智能分析系统",
        "value_type": "string",
        "description": "应用程序名称",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "general",
        "key": "app_description",
        "value": "自然语言转SQL的数据智能分析平台",
        "value_type": "string",
        "description": "应用程序描述",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "general",
        "key": "default_language",
        "value": "zh-CN",
        "value_type": "string",
        "description": "默认语言",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "general",
        "key": "timezone",
        "value": "Asia/Shanghai",
        "value_type": "string",
        "description": "时区设置",
        "is_system": True,
        "is_editable": True
    },

    # 查询设置
    {
        "category": "query",
        "key": "max_query_timeout",
        "value": "30",
        "value_type": "integer",
        "description": "查询超时时间（秒）",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "query",
        "key": "max_result_rows",
        "value": "1000",
        "value_type": "integer",
        "description": "最大结果行数",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "query",
        "key": "enable_query_cache",
        "value": "true",
        "value_type": "boolean",
        "description": "启用查询缓存",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "query",
        "key": "cache_expiration",
        "value": "3600",
        "value_type": "integer",
        "description": "缓存过期时间（秒）",
        "is_system": True,
        "is_editable": True
    },

    # 安全设置
    {
        "category": "security",
        "key": "enable_audit_log",
        "value": "true",
        "value_type": "boolean",
        "description": "启用审计日志",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "security",
        "key": "session_timeout",
        "value": "1800",
        "value_type": "integer",
        "description": "会话超时时间（秒）",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "security",
        "key": "max_login_attempts",
        "value": "5",
        "value_type": "integer",
        "description": "最大登录尝试次数",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "security",
        "key": "enable_two_factor",
        "value": "false",
        "value_type": "boolean",
        "description": "启用双因素认证",
        "is_system": True,
        "is_editable": True
    },

    # 通知设置
    {
        "category": "notifications",
        "key": "enable_email_notifications",
        "value": "true",
        "value_type": "boolean",
        "description": "启用邮件通知",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "notifications",
        "key": "enable_system_alerts",
        "value": "true",
        "value_type": "boolean",
        "description": "启用系统警报",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "notifications",
        "key": "notification_email",
        "value": "<EMAIL>",
        "value_type": "string",
        "description": "通知邮箱",
        "is_system": True,
        "is_editable": True
    },

    # 外观设置
    {
        "category": "appearance",
        "key": "default_theme",
        "value": "system",
        "value_type": "string",
        "description": "默认主题",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "appearance",
        "key": "enable_dark_mode",
        "value": "true",
        "value_type": "boolean",
        "description": "启用深色模式",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "appearance",
        "key": "compact_mode",
        "value": "false",
        "value_type": "boolean",
        "description": "紧凑模式",
        "is_system": True,
        "is_editable": True
    },
    {
        "category": "appearance",
        "key": "show_welcome_message",
        "value": "true",
        "value_type": "boolean",
        "description": "显示欢迎消息",
        "is_system": True,
        "is_editable": True
    },
]
