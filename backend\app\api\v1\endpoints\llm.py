from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.security import get_current_user, encrypt_password
from app.models.user import User
from app.models.llm_config import LLMProvider
from app.schemas.llm import LLMProviderCreate, LLMProviderUpdate, LLMProviderResponse

router = APIRouter()

@router.get("/providers", response_model=List[LLMProviderResponse])
async def get_llm_providers(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取LLM提供商列表"""
    providers = db.query(LLMProvider).all()
    return providers

@router.post("/providers", response_model=LLMProviderResponse)
async def create_llm_provider(
    provider: LLMProviderCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建LLM提供商"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="权限不足")
    
    # 加密API密钥
    encrypted_api_key = encrypt_password(provider.api_key)
    
    db_provider = LLMProvider(
        **provider.dict(exclude={"api_key"}),
        api_key=encrypted_api_key
    )
    
    db.add(db_provider)
    db.commit()
    db.refresh(db_provider)
    
    return db_provider

@router.put("/providers/{provider_id}", response_model=LLMProviderResponse)
async def update_llm_provider(
    provider_id: int,
    provider: LLMProviderUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新LLM提供商"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="权限不足")
    
    db_provider = db.query(LLMProvider).filter(LLMProvider.id == provider_id).first()
    if not db_provider:
        raise HTTPException(status_code=404, detail="提供商不存在")
    
    update_data = provider.dict(exclude_unset=True)
    if "api_key" in update_data:
        update_data["api_key"] = encrypt_password(update_data["api_key"])
    
    for field, value in update_data.items():
        setattr(db_provider, field, value)
    
    db.commit()
    db.refresh(db_provider)
    
    return db_provider

@router.post("/providers/{provider_id}/test")
async def test_llm_provider(
    provider_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """测试LLM提供商连接"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="权限不足")
    
    # 这里实现测试逻辑
    return {"success": True, "message": "连接测试成功"}