"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import apiClient from "@/lib/api-client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs" // Import Tabs components
import PermissionTree from "@/components/permission-tree"
import DataPermissionTree from "@/components/data-permission-tree" // Import new component
import { Plus, Edit, Trash2, Users, Shield } from "lucide-react" // Added Database icon
import { Tooltip } from 'react-tooltip'

interface Role {
  id: string
  name: string
  description: string
  permissions: string[] // Operational permissions
  dataPermissions: string[] // New: Data permissions
  userCount: number
  createdAt: string
  updatedAt: string
}

const mockRoles: Role[] = [
  {
    id: "1",
    name: "管理员",
    description: "系统管理员，拥有所有权限",
    permissions: [
      "query.read",
      "query.create",
      "query.edit",
      "query.delete",
      "data.view",
      "data.export",
      "user.view",
      "user.create",
      "user.edit",
      "system.config",
    ],
    dataPermissions: ["ds1", "db1", "table1", "table2", "db2", "table3", "ds2", "db3", "table4"], // Example: All data access
    userCount: 2,
    createdAt: "2024-01-01",
    updatedAt: "2024-01-15",
  },
  {
    id: "2",
    name: "数据分析师",
    description: "数据分析人员，可以查询和分析数据",
    permissions: ["query.read", "query.create", "query.edit", "data.view", "data.export"],
    dataPermissions: ["ds1", "db1", "table1", "table2"], // Example: Only sales data
    userCount: 8,
    createdAt: "2024-01-02",
    updatedAt: "2024-01-10",
  },
  {
    id: "3",
    name: "普通用户",
    description: "基础用户，只能查看数据",
    permissions: ["query.read", "data.view"],
    dataPermissions: ["ds1", "db1", "table1"], // Example: Only one table
    userCount: 15,
    createdAt: "2024-01-03",
    updatedAt: "2024-01-05",
  },
]

export default function RoleManagement() {
  const { user } = useAuth()
  const [roles, setRoles] = useState<Role[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
    dataPermissions: [] as string[], // Initialize new field
  })

  // 加载角色数据
  useEffect(() => {
    const loadRoles = async () => {
      if (!user || user.role !== 'admin') return

      try {
        setIsLoading(true)
        setError("")
        const apiClient = (await import("@/lib/api-client")).default
        const response = await apiClient.get('/roles/')

        setRoles(response)
      } catch (error: any) {
        console.error('加载角色失败:', error)
        setError('加载角色失败，请稍后重试')
        // 如果API调用失败，使用模拟数据作为后备
        setRoles(mockRoles)
      } finally {
        setIsLoading(false)
      }
    }

    loadRoles()
  }, [user])

  const handleCreateRole = async () => {
    if (!user || user.role !== 'admin') {
      setError('只有管理员可以创建角色')
      return
    }

    try {
      setError("")
      const apiClient = (await import("@/lib/api-client")).default

      const roleData = {
        name: formData.name,
        description: formData.description,
        permissions: formData.permissions,
        data_permissions: formData.dataPermissions,
        is_active: true
      }

      const newRole = await apiClient.post('/roles/', roleData)

      setRoles([...roles, newRole])
      setIsCreateDialogOpen(false)
      resetForm()
    } catch (error: any) {
      console.error('创建角色失败:', error)
      setError(error.message || '创建角色失败，请稍后重试')
    }
  }

  const handleEditRole = async () => {
    if (!selectedRole || !user || user.role !== 'admin') {
      setError('只有管理员可以编辑角色')
      return
    }

    try {
      setError("")
      const apiClient = (await import("@/lib/api-client")).default

      const roleData = {
        name: formData.name,
        description: formData.description,
        permissions: formData.permissions,
        data_permissions: formData.dataPermissions,
      }

      const updatedRole = await apiClient.put(`/roles/${selectedRole.id}`, roleData)

      const updatedRoles = roles.map((role) =>
        role.id === selectedRole.id ? updatedRole : role
      )

      setRoles(updatedRoles)
      setIsEditDialogOpen(false)
      resetForm()
    } catch (error: any) {
      console.error('更新角色失败:', error)
      setError(error.message || '更新角色失败，请稍后重试')
    }
  }

  const handleDeleteRole = async (roleId: string) => {
    if (!user || user.role !== 'admin') {
      setError('只有管理员可以删除角色')
      return
    }

    if (!confirm("确定要删除这个角色吗？")) {
      return
    }

    try {
      setError("")
      const apiClient = (await import("@/lib/api-client")).default

      await apiClient.delete(`/roles/${roleId}`)

      setRoles(roles.filter((role) => role.id !== roleId))
    } catch (error: any) {
      console.error('删除角色失败:', error)
      setError(error.message || '删除角色失败，请稍后重试')
    }
  }

  const openEditDialog = (role: Role) => {
    setSelectedRole(role)
    setFormData({
      name: role.name,
      description: role.description,
      permissions: role.permissions,
      dataPermissions: role.dataPermissions, // Load data permissions
    })
    setIsEditDialogOpen(true)
  }

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      permissions: [],
      dataPermissions: [],
    })
    setSelectedRole(null)
  }

  const getPermissionCount = (permissions: string[]) => {
    return permissions.length
  }

  // 加载状态
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold">角色管理</h2>
            <p className="text-gray-600">管理系统角色和权限分配</p>
          </div>
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-6 border border-gray-200 rounded-lg animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">角色管理</h2>
          <p className="text-gray-600">管理系统角色和权限分配</p>
          {user?.role !== 'admin' && (
            <p className="text-amber-600 text-sm mt-1">
              注意：只有管理员可以管理角色
            </p>
          )}
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => resetForm()}
              disabled={user?.role !== 'admin'}
            >
              <Plus className="h-4 w-4 mr-2" />
              新建角色
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>创建新角色</DialogTitle>
              <DialogDescription>设置角色名称、描述和权限</DialogDescription>
            </DialogHeader>
            <Tabs defaultValue="basic" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">基本信息</TabsTrigger>
                <TabsTrigger value="operational">操作权限</TabsTrigger>
                <TabsTrigger value="data">数据权限</TabsTrigger> {/* New Tab */}
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="roleName">角色名称</Label>
                  <Input
                    id="roleName"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="输入角色名称"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="roleDescription">角色描述</Label>
                  <Textarea
                    id="roleDescription"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="输入角色描述"
                  />
                </div>
              </TabsContent>

              <TabsContent value="operational">
                <PermissionTree
                  selectedPermissions={formData.permissions}
                  onPermissionChange={(permissions) => setFormData({ ...formData, permissions })}
                />
              </TabsContent>

              <TabsContent value="data">
                <DataPermissionTree
                  selectedDataPermissions={formData.dataPermissions}
                  onDataPermissionChange={(dataPermissions) => setFormData({ ...formData, dataPermissions })}
                />
              </TabsContent>
            </Tabs>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleCreateRole} disabled={!formData.name.trim()}>
                创建角色
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            角色列表
          </CardTitle>
          <CardDescription>系统中的所有角色和权限配置</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>角色名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>操作权限</TableHead>
                <TableHead>数据权限</TableHead>
                <TableHead>用户数量</TableHead>
                <TableHead>更新时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-blue-600" />
                      <span className="font-medium">{role.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-600">{role.description}</span>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{getPermissionCount(role.permissions)} 个权限</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{getPermissionCount(role.dataPermissions)} 个数据项</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-gray-500" />
                      <span>{role.userCount}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-500">{role.updatedAt}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" data-tooltip-id="tooltip-edit" data-tooltip-content="编辑" onClick={() => openEditDialog(role)}>
                        <Edit className="h-4 w-4" />
                        <Tooltip id="tooltip-edit" />
                      </Button>
                      <Button variant="outline" size="sm" data-tooltip-id="tooltip-trash" data-tooltip-content="删除" onClick={() => handleDeleteRole(role.id)} disabled={role.userCount > 0}>
                        <Trash2 className="h-4 w-4" />
                        <Tooltip id="tooltip-trash" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>编辑角色</DialogTitle>
            <DialogDescription>修改角色信息和权限设置</DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="basic" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="operational">操作权限</TabsTrigger>
              <TabsTrigger value="data">数据权限</TabsTrigger> {/* New Tab */}
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="editRoleName">角色名称</Label>
                <Input
                  id="editRoleName"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="输入角色名称"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="editRoleDescription">角色描述</Label>
                <Textarea
                  id="editRoleDescription"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="输入角色描述"
                />
              </div>
            </TabsContent>

            <TabsContent value="operational">
              <PermissionTree
                selectedPermissions={formData.permissions}
                onPermissionChange={(permissions) => setFormData({ ...formData, permissions })}
              />
            </TabsContent>

            <TabsContent value="data">
              <DataPermissionTree
                selectedDataPermissions={formData.dataPermissions}
                onDataPermissionChange={(dataPermissions) => setFormData({ ...formData, dataPermissions })}
              />
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditRole} disabled={!formData.name.trim()}>
              保存更改
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
