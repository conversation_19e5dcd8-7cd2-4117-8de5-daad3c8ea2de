from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime

class QueryRequest(BaseModel):
    natural_query: Optional[str] = None
    sql_query: Optional[str] = None
    input_mode: str = "nl"  # nl 或 sql
    data_source_id: int

class QueryResponse(BaseModel):
    sql_query: str
    results: Dict[str, Any]
    execution_status: str
    query_id: int
    execution_time: Optional[int] = None

class QueryHistoryResponse(BaseModel):
    id: int
    natural_query: str
    generated_sql: str
    execution_status: str
    created_at: datetime
    data_source_id: int
    
    class Config:
        from_attributes = True