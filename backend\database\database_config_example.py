# 数据库配置示例文件
# 将此文件复制到项目的配置目录，并根据实际情况修改

# MySQL数据库配置
MYSQL_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "your_username",
    "password": "your_password",
    "database": "nl2sql",
    "charset": "utf8mb4"
}

# SQLAlchemy数据库URL
# 格式：mysql+pymysql://username:password@host:port/database
DATABASE_URL = f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"

# 数据库连接池配置
POOL_SIZE = 5  # 连接池大小
MAX_OVERFLOW = 10  # 最大溢出连接数
POOL_TIMEOUT = 30  # 连接池超时时间（秒）
POOL_RECYCLE = 1800  # 连接回收时间（秒）

# 完整的SQLAlchemy引擎配置示例
"""
from sqlalchemy import create_engine

engine = create_engine(
    DATABASE_URL,
    pool_size=POOL_SIZE,
    max_overflow=MAX_OVERFLOW,
    pool_timeout=POOL_TIMEOUT,
    pool_recycle=POOL_RECYCLE,
    echo=False  # 设置为True可以查看SQL语句执行日志
)
"""