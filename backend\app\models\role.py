"""
角色管理数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, JSON, ForeignKey, Table
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


# 角色权限关联表
role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', Integer, ForeignKey('permissions.id'), primary_key=True)
)

# 角色数据权限关联表
role_data_permissions = Table(
    'role_data_permissions',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True),
    Column('data_permission_id', Integer, ForeignKey('data_permissions.id'), primary_key=True)
)

# 用户角色关联表
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('role_id', Integer, Foreign<PERSON><PERSON>('roles.id'), primary_key=True)
)


class Role(Base):
    """角色模型"""
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, comment="角色名称")
    description = Column(Text, nullable=True, comment="角色描述")
    is_system = Column(Boolean, default=False, comment="是否为系统内置角色")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    permissions = relationship("Permission", secondary=role_permissions, back_populates="roles")
    data_permissions = relationship("DataPermission", secondary=role_data_permissions, back_populates="roles")
    users = relationship("User", secondary=user_roles)

    def __repr__(self):
        return f"<Role(name='{self.name}', description='{self.description}')>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "is_system": self.is_system,
            "is_active": self.is_active,
            "permissions": [perm.code for perm in self.permissions],
            "data_permissions": [dp.code for dp in self.data_permissions],
            "user_count": len(self.users),
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Permission(Base):
    """权限模型"""
    __tablename__ = "permissions"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(100), nullable=False, unique=True, comment="权限代码")
    name = Column(String(100), nullable=False, comment="权限名称")
    description = Column(Text, nullable=True, comment="权限描述")
    category = Column(String(50), nullable=False, comment="权限分类")
    is_system = Column(Boolean, default=True, comment="是否为系统权限")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关联关系
    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")

    def __repr__(self):
        return f"<Permission(code='{self.code}', name='{self.name}')>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "code": self.code,
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "is_system": self.is_system,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class DataPermission(Base):
    """数据权限模型"""
    __tablename__ = "data_permissions"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(100), nullable=False, unique=True, comment="数据权限代码")
    name = Column(String(100), nullable=False, comment="数据权限名称")
    description = Column(Text, nullable=True, comment="数据权限描述")
    resource_type = Column(String(50), nullable=False, comment="资源类型: datasource, database, table")
    resource_id = Column(String(100), nullable=False, comment="资源ID")
    parent_id = Column(Integer, ForeignKey('data_permissions.id'), nullable=True, comment="父级权限ID")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关联关系
    roles = relationship("Role", secondary=role_data_permissions, back_populates="data_permissions")
    children = relationship("DataPermission", backref="parent", remote_side=[id])

    def __repr__(self):
        return f"<DataPermission(code='{self.code}', resource_type='{self.resource_type}')>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "code": self.code,
            "name": self.name,
            "description": self.description,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "parent_id": self.parent_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


# 默认权限配置
DEFAULT_PERMISSIONS = [
    # 查询权限
    {"code": "query.read", "name": "查看查询", "description": "查看查询历史和结果", "category": "query"},
    {"code": "query.create", "name": "创建查询", "description": "创建新的查询", "category": "query"},
    {"code": "query.edit", "name": "编辑查询", "description": "编辑现有查询", "category": "query"},
    {"code": "query.delete", "name": "删除查询", "description": "删除查询记录", "category": "query"},
    
    # 数据权限
    {"code": "data.view", "name": "查看数据", "description": "查看数据内容", "category": "data"},
    {"code": "data.export", "name": "导出数据", "description": "导出查询结果", "category": "data"},
    
    # 用户管理权限
    {"code": "user.view", "name": "查看用户", "description": "查看用户列表和信息", "category": "user"},
    {"code": "user.create", "name": "创建用户", "description": "创建新用户", "category": "user"},
    {"code": "user.edit", "name": "编辑用户", "description": "编辑用户信息", "category": "user"},
    {"code": "user.delete", "name": "删除用户", "description": "删除用户账户", "category": "user"},
    
    # 角色管理权限
    {"code": "role.view", "name": "查看角色", "description": "查看角色列表和权限", "category": "role"},
    {"code": "role.create", "name": "创建角色", "description": "创建新角色", "category": "role"},
    {"code": "role.edit", "name": "编辑角色", "description": "编辑角色和权限", "category": "role"},
    {"code": "role.delete", "name": "删除角色", "description": "删除角色", "category": "role"},
    
    # 数据源管理权限
    {"code": "datasource.view", "name": "查看数据源", "description": "查看数据源配置", "category": "datasource"},
    {"code": "datasource.create", "name": "创建数据源", "description": "创建新数据源", "category": "datasource"},
    {"code": "datasource.edit", "name": "编辑数据源", "description": "编辑数据源配置", "category": "datasource"},
    {"code": "datasource.delete", "name": "删除数据源", "description": "删除数据源", "category": "datasource"},
    
    # 系统管理权限
    {"code": "system.config", "name": "系统配置", "description": "管理系统设置", "category": "system"},
    {"code": "system.audit", "name": "审计日志", "description": "查看审计日志", "category": "system"},
    {"code": "system.backup", "name": "系统备份", "description": "执行系统备份", "category": "system"},
]

# 默认角色配置
DEFAULT_ROLES = [
    {
        "name": "超级管理员",
        "description": "系统超级管理员，拥有所有权限",
        "is_system": True,
        "permissions": [perm["code"] for perm in DEFAULT_PERMISSIONS]
    },
    {
        "name": "管理员",
        "description": "系统管理员，拥有大部分管理权限",
        "is_system": True,
        "permissions": [
            "query.read", "query.create", "query.edit", "query.delete",
            "data.view", "data.export",
            "user.view", "user.create", "user.edit",
            "datasource.view", "datasource.create", "datasource.edit",
            "system.config", "system.audit"
        ]
    },
    {
        "name": "数据分析师",
        "description": "数据分析人员，可以查询和分析数据",
        "is_system": True,
        "permissions": [
            "query.read", "query.create", "query.edit",
            "data.view", "data.export"
        ]
    },
    {
        "name": "普通用户",
        "description": "基础用户，只能查看数据",
        "is_system": True,
        "permissions": [
            "query.read", "data.view"
        ]
    }
]
