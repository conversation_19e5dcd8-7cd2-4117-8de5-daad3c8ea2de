"use client"

import { useState, useEffect } from "react" // Import useEffect
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Brain, TestTube, Save, AlertCircle, CheckCircle, Zap, Plus, MoreVertical, Eye, EyeOff } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog, // Import Dialog
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

interface LLMProvider {
  id: string
  name: string
  type: "openai" | "azure" | "anthropic" | "local" | "groq" | "xai" | "fal" | "deepinfra" // Added more types
  status: "active" | "inactive" | "error"
  models: string[]
  apiKey: string // Added
  baseUrl: string // Added
}

const mockProviders: LLMProvider[] = [
  {
    id: "1",
    name: "OpenAI GPT",
    type: "openai",
    status: "active",
    models: ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"],
    apiKey: "sk-openai-mock-1",
    baseUrl: "https://api.openai.com/v1",
  },
  {
    id: "2",
    name: "Azure OpenAI",
    type: "azure",
    status: "inactive",
    models: ["gpt-4", "gpt-35-turbo"],
    apiKey: "sk-azure-mock-2",
    baseUrl: "https://azure.openai.com/v1",
  },
  {
    id: "3",
    name: "Claude",
    type: "anthropic",
    status: "error",
    models: ["claude-3-opus-20240229", "claude-3-sonnet-20240229"],
    apiKey: "sk-anthropic-mock-3",
    baseUrl: "https://api.anthropic.com/v1",
  },
  {
    id: "4",
    name: "Groq",
    type: "groq",
    status: "active",
    models: ["llama3-8b-8192", "llama3-70b-8192"],
    apiKey: "sk-groq-mock-4",
    baseUrl: "https://api.groq.com/openai/v1",
  },
]

const llmTypes = [
  { value: "openai", label: "OpenAI" },
  { value: "azure", label: "Azure OpenAI" },
  { value: "anthropic", label: "Anthropic" },
  { value: "groq", label: "Groq" },
  { value: "xai", label: "xAI (Grok)" },
  { value: "fal", label: "Fal AI" },
  { value: "deepinfra", label: "DeepInfra" },
  { value: "local", label: "Local (Ollama, etc.)" },
]

export default function LLMConfig() {
  const [providers, setProviders] = useState(mockProviders)
  const [selectedProviderId, setSelectedProviderId] = useState<string>(mockProviders[0]?.id || "")
  const [config, setConfig] = useState({
    model: "", // Will be set from selectedProvider
    temperature: 0.7,
    maxTokens: 2048,
    timeout: 30,
    systemPrompt: "You are a helpful SQL assistant that converts natural language to SQL queries.",
    enableStreaming: true,
    enableCache: true,
    cacheExpiration: 3600,
  })
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)
  const [isTesting, setIsTesting] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const [isAddProviderDialogOpen, setIsAddProviderDialogOpen] = useState(false)
  const [newProviderFormData, setNewProviderFormData] = useState({
    name: "",
    type: "openai" as LLMProvider["type"],
    apiKey: "",
    baseUrl: "",
    models: "", // Comma-separated string
  })
  const [showNewProviderPassword, setShowNewProviderPassword] = useState(false)

  // Effect to update config when selectedProvider changes
  useEffect(() => {
    const currentProvider = providers.find((p) => p.id === selectedProviderId)
    if (currentProvider) {
      setConfig((prevConfig) => ({
        ...prevConfig,
        model: currentProvider.models[0] || "", // Set default model from provider's list
        // apiKey and baseUrl are now part of the provider object, not global config
        // We'll use them directly from currentProvider when needed for display/test
      }))
    } else if (providers.length > 0) {
      // If selectedProviderId is invalid, default to the first provider
      setSelectedProviderId(providers[0].id)
    } else {
      // No providers available
      setConfig((prevConfig) => ({
        ...prevConfig,
        model: "",
      }))
    }
  }, [selectedProviderId, providers])

  const currentSelectedProvider = providers.find((p) => p.id === selectedProviderId)

  const handleTest = async () => {
    if (!currentSelectedProvider) {
      setTestResult({ success: false, message: "请先选择一个模型提供商。" })
      return
    }

    setIsTesting(true)
    setTestResult(null)

    // Simulate API test using currentSelectedProvider's details
    console.log(
      `Testing connection for ${currentSelectedProvider.name} with API Key: ${currentSelectedProvider.apiKey} and Base URL: ${currentSelectedProvider.baseUrl}`,
    )
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const success = Math.random() > 0.3 // 70% success rate for demo
    setTestResult({
      success,
      message: success ? "连接测试成功！模型响应正常。" : "连接测试失败：API密钥无效或网络连接问题。",
    })
    setIsTesting(false)
  }

  const handleSave = async () => {
    if (!currentSelectedProvider) return

    setIsSaving(true)
    // In a real app, you'd send currentSelectedProvider and config to backend
    const updatedProviders = providers.map((p) =>
      p.id === selectedProviderId
        ? {
            ...p,
            // Update provider-specific config if needed, e.g., models list
            // For now, only general config is updated, which is not stored per provider in mock
          }
        : p,
    )
    setProviders(updatedProviders) // This line doesn't actually change anything for mock data
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setIsSaving(false)
  }

  const handleDeleteProvider = (id: string) => {
    if (confirm("确定要删除此模型提供商吗？此操作不可逆。")) {
      setProviders((prev) => {
        const updated = prev.filter((p) => p.id !== id)
        // If the deleted provider was selected, select the first available one or null
        if (selectedProviderId === id) {
          setSelectedProviderId(updated.length > 0 ? updated[0].id : "")
        }
        return updated
      })
    }
  }

  const handleToggleProviderStatus = (id: string, newStatus: "active" | "inactive") => {
    setProviders((prev) => prev.map((p) => (p.id === id ? { ...p, status: newStatus } : p)))
  }

  const handleAddNewProvider = () => {
    if (!newProviderFormData.name.trim() || !newProviderFormData.apiKey.trim() || !newProviderFormData.baseUrl.trim()) {
      alert("请填写所有必填字段。")
      return
    }

    const newProvider: LLMProvider = {
      id: Date.now().toString(), // Simple unique ID
      name: newProviderFormData.name.trim(),
      type: newProviderFormData.type,
      apiKey: newProviderFormData.apiKey.trim(),
      baseUrl: newProviderFormData.baseUrl.trim(),
      models: newProviderFormData.models
        .split(",")
        .map((m) => m.trim())
        .filter(Boolean), // Split by comma, trim, remove empty
      status: "inactive", // New providers start as inactive
    }

    setProviders((prev) => [...prev, newProvider])
    setIsAddProviderDialogOpen(false)
    setNewProviderFormData({
      name: "",
      type: "openai",
      apiKey: "",
      baseUrl: "",
      models: "",
    })
    setShowNewProviderPassword(false)
    setSelectedProviderId(newProvider.id) // Automatically select the newly added provider
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">运行中</Badge>
      case "inactive":
        return <Badge variant="secondary">未激活</Badge>
      case "error":
        return <Badge variant="destructive">错误</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">AI模型配置</h2>
          <p className="text-gray-600">配置和管理自然语言转SQL的AI模型</p>
        </div>
        <div className="flex space-x-2">
          <Dialog open={isAddProviderDialogOpen} onOpenChange={setIsAddProviderDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                添加提供商
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>添加新的AI模型提供商</DialogTitle>
                <DialogDescription>配置新的AI模型提供商的连接信息。</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="providerName">提供商名称</Label>
                  <Input
                    id="providerName"
                    value={newProviderFormData.name}
                    onChange={(e) => setNewProviderFormData({ ...newProviderFormData, name: e.target.value })}
                    placeholder="例如：OpenAI GPT, Groq API"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="providerType">类型</Label>
                  <Select
                    value={newProviderFormData.type}
                    onValueChange={(value: LLMProvider["type"]) =>
                      setNewProviderFormData({ ...newProviderFormData, type: value })
                    }
                  >
                    <SelectTrigger id="providerType">
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      {llmTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="providerApiKey">API密钥</Label>
                  <div className="relative">
                    <Input
                      id="providerApiKey"
                      type={showNewProviderPassword ? "text" : "password"}
                      value={newProviderFormData.apiKey}
                      onChange={(e) => setNewProviderFormData({ ...newProviderFormData, apiKey: e.target.value })}
                      placeholder="输入API密钥"
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowNewProviderPassword(!showNewProviderPassword)}
                    >
                      {showNewProviderPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="providerBaseUrl">API基础URL</Label>
                  <Input
                    id="providerBaseUrl"
                    value={newProviderFormData.baseUrl}
                    onChange={(e) => setNewProviderFormData({ ...newProviderFormData, baseUrl: e.target.value })}
                    placeholder="例如：https://api.openai.com/v1"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="providerModels">支持的模型 (逗号分隔)</Label>
                  <Textarea
                    id="providerModels"
                    value={newProviderFormData.models}
                    onChange={(e) => setNewProviderFormData({ ...newProviderFormData, models: e.target.value })}
                    placeholder="例如：gpt-4o, gpt-3.5-turbo"
                    rows={3}
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddProviderDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAddNewProvider}>添加提供商</Button>
              </div>
            </DialogContent>
          </Dialog>

          <Button variant="outline" onClick={handleTest} disabled={isTesting}>
            <TestTube className="h-4 w-4 mr-2" />
            {isTesting ? "测试中..." : "测试连接"}
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? "保存中..." : "保存配置"}
          </Button>
        </div>
      </div>

      {testResult && (
        <div
          className={`p-4 rounded-lg border ${testResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}`}
        >
          <div className="flex items-center gap-2">
            {testResult.success ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-600" />
            )}
            <span className={testResult.success ? "text-green-800" : "text-red-800"}>{testResult.message}</span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Provider List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              模型提供商
            </CardTitle>
            <CardDescription>选择和管理AI模型提供商</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {providers.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                <p>暂无模型提供商。</p>
                <p>点击“添加提供商”按钮开始配置。</p>
              </div>
            )}
            {providers.map((provider) => (
              <div
                key={provider.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors relative ${
                  selectedProviderId === provider.id ? "border-blue-500 bg-blue-50" : "hover:bg-gray-50"
                }`}
                onClick={() => setSelectedProviderId(provider.id)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{provider.name}</h4>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(provider.status)}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation()
                            alert(`编辑 ${provider.name} 功能待实现`)
                          }}
                        >
                          编辑
                        </DropdownMenuItem>
                        {provider.status !== "active" && (
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()
                              handleToggleProviderStatus(provider.id, "active")
                            }}
                          >
                            激活
                          </DropdownMenuItem>
                        )}
                        {provider.status === "active" && (
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()
                              handleToggleProviderStatus(provider.id, "inactive")
                            }}
                          >
                            禁用
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteProvider(provider.id)
                          }}
                        >
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-2">{provider.models.length} 个可用模型</p>
                <div className="flex flex-wrap gap-1">
                  {provider.models.slice(0, 2).map((model) => (
                    <Badge key={model} variant="outline" className="text-xs">
                      {model}
                    </Badge>
                  ))}
                  {provider.models.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{provider.models.length - 2}
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Configuration */}
        <div className="lg:col-span-2">
          {currentSelectedProvider ? (
            <Tabs defaultValue="connection" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="connection">连接配置</TabsTrigger>
                <TabsTrigger value="model">模型参数</TabsTrigger>
                <TabsTrigger value="advanced">高级设置</TabsTrigger>
              </TabsList>

              <TabsContent value="connection" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>连接设置</CardTitle>
                    <CardDescription>配置API连接参数</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="apiKey">API密钥</Label>
                      <Input
                        id="apiKey"
                        type="password"
                        value={currentSelectedProvider.apiKey} // Display from selected provider
                        readOnly // Make read-only as it's part of provider data
                        placeholder="API密钥"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="baseUrl">API基础URL</Label>
                      <Input
                        id="baseUrl"
                        value={currentSelectedProvider.baseUrl} // Display from selected provider
                        readOnly // Make read-only
                        placeholder="https://api.openai.com/v1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="timeout">请求超时 (秒)</Label>
                      <Input
                        id="timeout"
                        type="number"
                        value={config.timeout}
                        onChange={(e) => setConfig({ ...config, timeout: Number.parseInt(e.target.value) })}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="model" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>模型参数</CardTitle>
                    <CardDescription>调整模型的行为和输出质量</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="model">模型选择</Label>
                      <Select value={config.model} onValueChange={(value) => setConfig({ ...config, model: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {currentSelectedProvider.models.map((model) => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="temperature">温度 (创造性)</Label>
                      <div className="flex items-center space-x-4">
                        <Input
                          id="temperature"
                          type="number"
                          min="0"
                          max="2"
                          step="0.1"
                          value={config.temperature}
                          onChange={(e) => setConfig({ ...config, temperature: Number.parseFloat(e.target.value) })}
                          className="w-20"
                        />
                        <span className="text-sm text-gray-600">
                          {config.temperature < 0.3 ? "保守" : config.temperature < 0.7 ? "平衡" : "创新"}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maxTokens">最大令牌数</Label>
                      <Input
                        id="maxTokens"
                        type="number"
                        value={config.maxTokens}
                        onChange={(e) => setConfig({ ...config, maxTokens: Number.parseInt(e.target.value) })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="systemPrompt">系统提示词</Label>
                      <Textarea
                        id="systemPrompt"
                        value={config.systemPrompt}
                        onChange={(e) => setConfig({ ...config, systemPrompt: e.target.value })}
                        placeholder="定义AI助手的角色和行为..."
                        className="min-h-[100px]"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>高级设置</CardTitle>
                    <CardDescription>性能优化和缓存配置</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>启用流式响应</Label>
                        <p className="text-sm text-gray-600">实时显示AI生成的内容</p>
                      </div>
                      <Switch
                        checked={config.enableStreaming}
                        onCheckedChange={(checked) => setConfig({ ...config, enableStreaming: checked })}
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>启用响应缓存</Label>
                        <p className="text-sm text-gray-600">缓存相似查询的结果</p>
                      </div>
                      <Switch
                        checked={config.enableCache}
                        onCheckedChange={(checked) => setConfig({ ...config, enableCache: checked })}
                      />
                    </div>

                    {config.enableCache && (
                      <div className="space-y-2">
                        <Label htmlFor="cacheExpiration">缓存过期时间 (秒)</Label>
                        <Input
                          id="cacheExpiration"
                          type="number"
                          value={config.cacheExpiration}
                          onChange={(e) => setConfig({ ...config, cacheExpiration: Number.parseInt(e.target.value) })}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      性能监控
                    </CardTitle>
                    <CardDescription>模型使用情况和性能指标</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">1,234</div>
                        <div className="text-sm text-gray-600">今日查询数</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">98.5%</div>
                        <div className="text-sm text-gray-600">成功率</div>
                      </div>
                      <div className="text-center p-4 bg-yellow-50 rounded-lg">
                        <div className="text-2xl font-bold text-yellow-600">1.2s</div>
                        <div className="text-sm text-gray-600">平均响应时间</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">$45.67</div>
                        <div className="text-sm text-gray-600">本月费用</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <Card className="h-full flex flex-col items-center justify-center p-8 text-gray-500">
              <Brain className="h-12 w-12 mb-4" />
              <h3 className="text-xl font-medium mb-2">暂无AI模型提供商</h3>
              <p className="text-center max-w-md mb-4">请点击上方的“添加提供商”按钮，配置您的第一个AI模型提供商。</p>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
