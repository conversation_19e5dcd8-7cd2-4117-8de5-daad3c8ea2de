from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import pandas as pd
import json
import csv
import io
from typing import List
from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.query import QueryHistory
from app.services.database import DatabaseService

router = APIRouter()

@router.post("/query/{query_id}/csv")
async def export_query_csv(
    query_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """导出查询结果为CSV"""
    query = db.query(QueryHistory).filter(
        QueryHistory.id == query_id,
        QueryHistory.user_id == current_user.id
    ).first()
    
    if not query or not query.results:
        raise HTTPException(status_code=404, detail="查询结果不存在")
    
    # 转换为DataFrame并导出CSV
    df = pd.DataFrame(query.results.get('rows', []))
    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False, encoding='utf-8-sig')
    
    filename = f"query_{query_id}_results.csv"
    
    return {
        "filename": filename,
        "content": csv_buffer.getvalue(),
        "content_type": "text/csv"
    }

@router.post("/query/{query_id}/excel")
async def export_query_excel(
    query_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """导出查询结果为Excel"""
    query = db.query(QueryHistory).filter(
        QueryHistory.id == query_id,
        QueryHistory.user_id == current_user.id
    ).first()
    
    if not query or not query.results:
        raise HTTPException(status_code=404, detail="查询结果不存在")
    
    df = pd.DataFrame(query.results.get('rows', []))
    excel_buffer = io.BytesIO()
    df.to_excel(excel_buffer, index=False, engine='openpyxl')
    
    filename = f"query_{query_id}_results.xlsx"
    
    return {
        "filename": filename,
        "content": excel_buffer.getvalue(),
        "content_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    }