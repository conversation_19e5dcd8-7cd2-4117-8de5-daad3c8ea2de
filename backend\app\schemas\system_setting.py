"""
系统设置相关的Pydantic模型
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime


class SystemSettingBase(BaseModel):
    """系统设置基础模型"""
    category: str = Field(..., description="设置分类")
    key: str = Field(..., description="设置键名")
    value: Optional[str] = Field(None, description="设置值")
    value_type: str = Field(default="string", description="值类型")
    description: Optional[str] = Field(None, description="设置描述")
    is_editable: bool = Field(default=True, description="是否可编辑")


class SystemSettingCreate(SystemSettingBase):
    """创建系统设置的模型"""
    pass


class SystemSettingUpdate(BaseModel):
    """更新系统设置的模型"""
    value: Optional[str] = Field(None, description="设置值")
    description: Optional[str] = Field(None, description="设置描述")
    is_editable: Optional[bool] = Field(None, description="是否可编辑")


class SystemSetting(SystemSettingBase):
    """系统设置响应模型"""
    id: int
    is_system: bool = Field(description="是否为系统内置设置")
    created_at: Optional[datetime] = Field(description="创建时间")
    updated_at: Optional[datetime] = Field(description="更新时间")

    class Config:
        from_attributes = True


class SystemSettingValue(BaseModel):
    """系统设置值模型（用于批量更新）"""
    key: str = Field(..., description="设置键名")
    value: Union[str, int, bool, Dict, List] = Field(..., description="设置值")


class SystemSettingsBatch(BaseModel):
    """批量系统设置模型"""
    settings: List[SystemSettingValue] = Field(..., description="设置列表")


class ApplicationSettings(BaseModel):
    """应用程序设置模型（前端使用的格式）"""
    # 常规设置
    appName: str = Field(default="NL2SQL 数据智能分析系统", description="应用名称")
    appDescription: str = Field(default="自然语言转SQL的数据智能分析平台", description="应用描述")
    defaultLanguage: str = Field(default="zh-CN", description="默认语言")
    timezone: str = Field(default="Asia/Shanghai", description="时区")

    # 查询设置
    maxQueryTimeout: int = Field(default=30, description="查询超时时间（秒）")
    maxResultRows: int = Field(default=1000, description="最大结果行数")
    enableQueryCache: bool = Field(default=True, description="启用查询缓存")
    cacheExpiration: int = Field(default=3600, description="缓存过期时间（秒）")

    # 安全设置
    enableAuditLog: bool = Field(default=True, description="启用审计日志")
    sessionTimeout: int = Field(default=1800, description="会话超时时间（秒）")
    maxLoginAttempts: int = Field(default=5, description="最大登录尝试次数")
    enableTwoFactor: bool = Field(default=False, description="启用双因素认证")

    # 通知设置
    enableEmailNotifications: bool = Field(default=True, description="启用邮件通知")
    enableSystemAlerts: bool = Field(default=True, description="启用系统警报")
    notificationEmail: str = Field(default="<EMAIL>", description="通知邮箱")

    # 外观设置
    defaultTheme: str = Field(default="system", description="默认主题")
    enableDarkMode: bool = Field(default=True, description="启用深色模式")
    compactMode: bool = Field(default=False, description="紧凑模式")
    showWelcomeMessage: bool = Field(default=True, description="显示欢迎消息")


class ApplicationSettingsUpdate(BaseModel):
    """应用程序设置更新模型"""
    # 常规设置
    appName: Optional[str] = None
    appDescription: Optional[str] = None
    defaultLanguage: Optional[str] = None
    timezone: Optional[str] = None

    # 查询设置
    maxQueryTimeout: Optional[int] = None
    maxResultRows: Optional[int] = None
    enableQueryCache: Optional[bool] = None
    cacheExpiration: Optional[int] = None

    # 安全设置
    enableAuditLog: Optional[bool] = None
    sessionTimeout: Optional[int] = None
    maxLoginAttempts: Optional[int] = None
    enableTwoFactor: Optional[bool] = None

    # 通知设置
    enableEmailNotifications: Optional[bool] = None
    enableSystemAlerts: Optional[bool] = None
    notificationEmail: Optional[str] = None

    # 外观设置
    defaultTheme: Optional[str] = None
    enableDarkMode: Optional[bool] = None
    compactMode: Optional[bool] = None
    showWelcomeMessage: Optional[bool] = None


class SettingsResponse(BaseModel):
    """设置响应模型"""
    success: bool = Field(description="操作是否成功")
    message: str = Field(description="响应消息")
    data: Optional[ApplicationSettings] = Field(None, description="设置数据")


# 前端字段名到后端键名的映射
FRONTEND_TO_BACKEND_MAPPING = {
    # 常规设置
    "appName": "app_name",
    "appDescription": "app_description", 
    "defaultLanguage": "default_language",
    "timezone": "timezone",

    # 查询设置
    "maxQueryTimeout": "max_query_timeout",
    "maxResultRows": "max_result_rows",
    "enableQueryCache": "enable_query_cache",
    "cacheExpiration": "cache_expiration",

    # 安全设置
    "enableAuditLog": "enable_audit_log",
    "sessionTimeout": "session_timeout",
    "maxLoginAttempts": "max_login_attempts",
    "enableTwoFactor": "enable_two_factor",

    # 通知设置
    "enableEmailNotifications": "enable_email_notifications",
    "enableSystemAlerts": "enable_system_alerts",
    "notificationEmail": "notification_email",

    # 外观设置
    "defaultTheme": "default_theme",
    "enableDarkMode": "enable_dark_mode",
    "compactMode": "compact_mode",
    "showWelcomeMessage": "show_welcome_message",
}

# 后端键名到前端字段名的映射
BACKEND_TO_FRONTEND_MAPPING = {v: k for k, v in FRONTEND_TO_BACKEND_MAPPING.items()}
