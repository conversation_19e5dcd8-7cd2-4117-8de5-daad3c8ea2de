from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.system_settings import SystemSettings
from app.schemas.settings import SystemSettingsUpdate, SystemSettingsResponse

router = APIRouter()

@router.get("/", response_model=SystemSettingsResponse)
async def get_system_settings(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取系统设置"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="权限不足")
    
    settings = db.query(SystemSettings).first()
    if not settings:
        # 创建默认设置
        settings = SystemSettings()
        db.add(settings)
        db.commit()
        db.refresh(settings)
    
    return settings

@router.put("/", response_model=SystemSettingsResponse)
async def update_system_settings(
    settings_update: SystemSettingsUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新系统设置"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="权限不足")
    
    settings = db.query(SystemSettings).first()
    if not settings:
        settings = SystemSettings()
        db.add(settings)
    
    update_data = settings_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(settings, field, value)
    
    db.commit()
    db.refresh(settings)
    
    return settings