from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.query import Favorite, QueryHistory
from app.schemas.favorite import FavoriteCreate, FavoriteUpdate, FavoriteResponse

router = APIRouter()

@router.get("/", response_model=List[FavoriteResponse])
async def get_favorites(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取收藏夹列表"""
    try:
        favorites = db.query(Favorite).filter(
            Favorite.user_id == current_user.id
        ).order_by(Favorite.created_at.desc()).all()

        return favorites
    except Exception as e:
        # 如果查询失败，返回空列表而不是错误
        print(f"获取收藏夹失败: {e}")
        return []

@router.post("/", response_model=FavoriteResponse)
async def create_favorite(
    favorite: FavoriteCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """添加到收藏夹"""
    # 检查查询是否存在
    query = db.query(QueryHistory).filter(QueryHistory.id == favorite.query_id).first()
    if not query:
        raise HTTPException(status_code=404, detail="查询不存在")
    
    # 检查是否已收藏
    existing = db.query(Favorite).filter(
        Favorite.user_id == current_user.id,
        Favorite.query_id == favorite.query_id
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="已经收藏过了")
    
    db_favorite = Favorite(
        **favorite.dict(),
        user_id=current_user.id
    )
    
    db.add(db_favorite)
    db.commit()
    db.refresh(db_favorite)
    
    return db_favorite

@router.put("/{favorite_id}", response_model=FavoriteResponse)
async def update_favorite(
    favorite_id: int,
    favorite: FavoriteUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新收藏"""
    db_favorite = db.query(Favorite).filter(
        Favorite.id == favorite_id,
        Favorite.user_id == current_user.id
    ).first()
    
    if not db_favorite:
        raise HTTPException(status_code=404, detail="收藏不存在")
    
    update_data = favorite.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_favorite, field, value)
    
    db.commit()
    db.refresh(db_favorite)
    
    return db_favorite

@router.delete("/{favorite_id}")
async def delete_favorite(
    favorite_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除收藏"""
    db_favorite = db.query(Favorite).filter(
        Favorite.id == favorite_id,
        Favorite.user_id == current_user.id
    ).first()
    
    if not db_favorite:
        raise HTTPException(status_code=404, detail="收藏不存在")
    
    db.delete(db_favorite)
    db.commit()
    
    return {"message": "删除成功"}