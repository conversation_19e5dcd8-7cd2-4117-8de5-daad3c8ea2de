"use client"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { MessageSquare, ArrowRight } from "lucide-react" // Removed Lightbulb

// Example queries by category
const exampleQueries = {
  sales: [
    "查询过去30天销售额最高的10个产品",
    "统计各地区本季度的销售额和同比增长率",
    "分析不同产品类别在各销售渠道的销售占比",
    "查询客户购买频率与客单价的关系",
  ],
  customers: [
    "统计各地区客户数量分布",
    "分析高价值客户的购买行为特征",
    "查询过去一年流失客户的共同特点",
    "统计新客户与老客户的消费差异",
  ],
  products: [
    "查询库存周转率最低的产品",
    "分析产品价格与销量的相关性",
    "统计各产品类别的毛利率",
    "查询季节性产品的销售趋势",
  ],
}

// Removed usageTips as it's now in app/docs/page.tsx

interface ExampleQueriesProps {
  onSelectQuery: (query: string) => void
}

export default function ExampleQueries({ onSelectQuery }: ExampleQueriesProps) {
  // Removed activeTab state as there's only one tab now
  // const [activeTab, setActiveTab] = useState("examples")

  return (
    <Card className="w-full max-w-2xl">
      <Tabs defaultValue="examples" className="w-full">
        <TabsList className="grid grid-cols-1 w-full">
          {" "}
          {/* Changed to grid-cols-1 */}
          <TabsTrigger value="examples">
            <MessageSquare className="h-4 w-4 mr-2" />
            示例查询
          </TabsTrigger>
          {/* Removed TabsTrigger for "tips" */}
        </TabsList>

        <TabsContent value="examples" className="m-0">
          <CardContent className="p-4">
            <Tabs defaultValue="sales" className="w-full">
              <TabsList className="grid grid-cols-3 w-full mb-4">
                <TabsTrigger value="sales">销售分析</TabsTrigger>
                <TabsTrigger value="customers">客户分析</TabsTrigger>
                <TabsTrigger value="products">产品分析</TabsTrigger>
              </TabsList>

              <TabsContent value="sales" className="m-0 space-y-2">
                {exampleQueries.sales.map((query, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="w-full justify-between text-left h-auto py-2 px-3 bg-transparent"
                    onClick={() => onSelectQuery(query)}
                  >
                    <span>{query}</span>
                    <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" />
                  </Button>
                ))}
              </TabsContent>

              <TabsContent value="customers" className="m-0 space-y-2">
                {exampleQueries.customers.map((query, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="w-full justify-between text-left h-auto py-2 px-3 bg-transparent"
                    onClick={() => onSelectQuery(query)}
                  >
                    <span>{query}</span>
                    <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" />
                  </Button>
                ))}
              </TabsContent>

              <TabsContent value="products" className="m-0 space-y-2">
                {exampleQueries.products.map((query, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="w-full justify-between text-left h-auto py-2 px-3 bg-transparent"
                    onClick={() => onSelectQuery(query)}
                  >
                    <span>{query}</span>
                    <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" />
                  </Button>
                ))}
              </TabsContent>
            </Tabs>
          </CardContent>
        </TabsContent>

        {/* Removed TabsContent for "tips" */}
      </Tabs>
    </Card>
  )
}
