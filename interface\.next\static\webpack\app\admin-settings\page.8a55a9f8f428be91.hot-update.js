"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-settings/page",{

/***/ "(app-pages-browser)/./components/user-management.tsx":
/*!****************************************!*\
  !*** ./components/user-management.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,Phone,Plus,Search,Trash2,UserCheck,UserX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,Phone,Plus,Search,Trash2,UserCheck,UserX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,Phone,Plus,Search,Trash2,UserCheck,UserX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,Phone,Plus,Search,Trash2,UserCheck,UserX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,Phone,Plus,Search,Trash2,UserCheck,UserX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,Phone,Plus,Search,Trash2,UserCheck,UserX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,Phone,Plus,Search,Trash2,UserCheck,UserX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Mail,Phone,Plus,Search,Trash2,UserCheck,UserX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst mockUsers = [\n    {\n        id: \"1\",\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        name: \"系统管理员\",\n        phone: \"138-0000-0001\",\n        role: \"管理员\",\n        status: \"active\",\n        lastLogin: \"2024-01-15 10:30:00\",\n        createdAt: \"2024-01-01\",\n        avatar: \"/placeholder.svg?height=40&width=40\"\n    },\n    {\n        id: \"2\",\n        username: \"analyst1\",\n        email: \"<EMAIL>\",\n        name: \"张三\",\n        phone: \"138-0000-0002\",\n        role: \"数据分析师\",\n        status: \"active\",\n        lastLogin: \"2024-01-15 09:15:00\",\n        createdAt: \"2024-01-02\"\n    },\n    {\n        id: \"3\",\n        username: \"user1\",\n        email: \"<EMAIL>\",\n        name: \"李四\",\n        role: \"普通用户\",\n        status: \"inactive\",\n        lastLogin: \"2024-01-10 14:20:00\",\n        createdAt: \"2024-01-03\"\n    },\n    {\n        id: \"4\",\n        username: \"analyst2\",\n        email: \"<EMAIL>\",\n        name: \"王五\",\n        phone: \"138-0000-0004\",\n        role: \"数据分析师\",\n        status: \"suspended\",\n        lastLogin: \"2024-01-12 16:45:00\",\n        createdAt: \"2024-01-04\"\n    }\n];\nconst roles = [\n    \"管理员\",\n    \"数据分析师\",\n    \"普通用户\"\n];\nfunction UserManagement() {\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [roleFilter, setRoleFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"\",\n        email: \"\",\n        name: \"\",\n        phone: \"\",\n        role: \"\",\n        status: \"active\"\n    });\n    // 加载用户数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            const loadUsers = {\n                \"UserManagement.useEffect.loadUsers\": async ()=>{\n                    if (!user || user.role !== 'admin') return;\n                    try {\n                        setIsLoading(true);\n                        setError(\"\");\n                        const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('/users/');\n                        // 转换后端数据格式为前端格式\n                        const transformedUsers = response.map({\n                            \"UserManagement.useEffect.loadUsers.transformedUsers\": (user)=>({\n                                    id: user.id.toString(),\n                                    username: user.username,\n                                    email: user.email,\n                                    name: user.name,\n                                    phone: \"\",\n                                    role: user.role === 'admin' ? '管理员' : user.role === 'analyst' ? '数据分析师' : '普通用户',\n                                    status: user.is_active ? 'active' : 'inactive',\n                                    lastLogin: user.last_login || '',\n                                    createdAt: user.created_at ? new Date(user.created_at).toISOString().split('T')[0] : '',\n                                    avatar: user.avatar || ''\n                                })\n                        }[\"UserManagement.useEffect.loadUsers.transformedUsers\"]);\n                        setUsers(transformedUsers);\n                    } catch (error) {\n                        console.error('加载用户数据失败:', error);\n                        // 如果API调用失败，使用模拟数据作为后备\n                        setUsers(mockUsers);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"UserManagement.useEffect.loadUsers\"];\n            loadUsers();\n        }\n    }[\"UserManagement.useEffect\"], []);\n    const filteredUsers = users.filter((user)=>{\n        const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) || user.username.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesStatus = statusFilter === \"all\" || user.status === statusFilter;\n        const matchesRole = roleFilter === \"all\" || user.role === roleFilter;\n        return matchesSearch && matchesStatus && matchesRole;\n    });\n    const handleCreateUser = ()=>{\n        const newUser = {\n            id: Date.now().toString(),\n            username: formData.username,\n            email: formData.email,\n            name: formData.name,\n            phone: formData.phone,\n            role: formData.role,\n            status: formData.status,\n            createdAt: new Date().toISOString().split(\"T\")[0]\n        };\n        setUsers([\n            ...users,\n            newUser\n        ]);\n        setIsCreateDialogOpen(false);\n        resetForm();\n    };\n    const handleEditUser = ()=>{\n        if (!selectedUser) return;\n        const updatedUsers = users.map((user)=>user.id === selectedUser.id ? {\n                ...user,\n                username: formData.username,\n                email: formData.email,\n                name: formData.name,\n                phone: formData.phone,\n                role: formData.role,\n                status: formData.status\n            } : user);\n        setUsers(updatedUsers);\n        setIsEditDialogOpen(false);\n        resetForm();\n    };\n    const handleDeleteUser = (userId)=>{\n        if (confirm(\"确定要删除这个用户吗？\")) {\n            setUsers(users.filter((user)=>user.id !== userId));\n        }\n    };\n    const handleStatusChange = (userId, newStatus)=>{\n        const updatedUsers = users.map((user)=>user.id === userId ? {\n                ...user,\n                status: newStatus\n            } : user);\n        setUsers(updatedUsers);\n    };\n    const openEditDialog = (user)=>{\n        setSelectedUser(user);\n        setFormData({\n            username: user.username,\n            email: user.email,\n            name: user.name,\n            phone: user.phone || \"\",\n            role: user.role,\n            status: user.status\n        });\n        setIsEditDialogOpen(true);\n    };\n    const resetForm = ()=>{\n        setFormData({\n            username: \"\",\n            email: \"\",\n            name: \"\",\n            phone: \"\",\n            role: \"\",\n            status: \"active\"\n        });\n        setSelectedUser(null);\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"正常\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 16\n                }, this);\n            case \"inactive\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    variant: \"secondary\",\n                    children: \"未激活\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 16\n                }, this);\n            case \"suspended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    variant: \"destructive\",\n                    children: \"已停用\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    variant: \"outline\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 16\n                }, this);\n            case \"inactive\":\n            case \"suspended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    // 加载状态\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-64 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-semibold text-xl\",\n                                children: \"用户管理\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"管理系统用户账户和权限\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                        open: isCreateDialogOpen,\n                        onOpenChange: setIsCreateDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>resetForm(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"新建用户\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                                children: \"创建新用户\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                                children: \"填写用户基本信息和角色权限\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"username\",\n                                                                children: \"用户名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"username\",\n                                                                value: formData.username,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        username: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入用户名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"name\",\n                                                                children: \"姓名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"name\",\n                                                                value: formData.name,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        name: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入真实姓名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"email\",\n                                                        children: \"邮箱\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        value: formData.email,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                email: e.target.value\n                                                            }),\n                                                        placeholder: \"输入邮箱地址\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"phone\",\n                                                        children: \"电话\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"phone\",\n                                                        value: formData.phone,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                phone: e.target.value\n                                                            }),\n                                                        placeholder: \"输入电话号码（可选）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"role\",\n                                                                children: \"角色\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                value: formData.role,\n                                                                onValueChange: (value)=>setFormData({\n                                                                        ...formData,\n                                                                        role: value\n                                                                    }),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                            placeholder: \"选择角色\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                        children: roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                value: role,\n                                                                                children: role\n                                                                            }, role, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"status\",\n                                                                children: \"状态\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                value: formData.status,\n                                                                onValueChange: (value)=>setFormData({\n                                                                        ...formData,\n                                                                        status: value\n                                                                    }),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                value: \"active\",\n                                                                                children: \"正常\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                value: \"inactive\",\n                                                                                children: \"未激活\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                                lineNumber: 357,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                value: \"suspended\",\n                                                                                children: \"已停用\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setIsCreateDialogOpen(false),\n                                                        children: \"取消\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        onClick: handleCreateUser,\n                                                        disabled: !formData.username.trim() || !formData.email.trim(),\n                                                        children: \"创建用户\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: \"用户列表\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                children: [\n                                    \"共 \",\n                                    filteredUsers.length,\n                                    \" 个用户\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-[200px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"搜索用户名、姓名或邮箱...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pl-9\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                    value: statusFilter,\n                                    onValueChange: setStatusFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                            className: \"w-[120px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                    value: \"all\",\n                                                    children: \"所有状态\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                    value: \"active\",\n                                                    children: \"正常\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                    value: \"inactive\",\n                                                    children: \"未激活\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                    value: \"suspended\",\n                                                    children: \"已停用\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                    value: roleFilter,\n                                    onValueChange: setRoleFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                            className: \"w-[140px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                    value: \"all\",\n                                                    children: \"所有角色\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 17\n                                                }, this),\n                                                roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: role,\n                                                        children: role\n                                                    }, role, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"用户\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"联系方式\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"角色\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"最后登录\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"创建时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableHead, {\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableBody, {\n                                    children: filteredUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                                                className: \"h-8 w-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarImage, {\n                                                                        src: user.avatar || \"/placeholder.svg\",\n                                                                        alt: user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                                                        children: user.name.charAt(0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"@\",\n                                                                            user.username\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    user.email\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            user.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    user.phone\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: user.role\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            getStatusIcon(user.status),\n                                                            getStatusBadge(user.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: user.lastLogin || \"从未登录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: user.createdAt\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                value: user.status,\n                                                                onValueChange: (value)=>handleStatusChange(user.id, value),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                value: \"active\",\n                                                                                children: \"启用\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                value: \"suspended\",\n                                                                                children: \"停用\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 485,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                        className: \"w-[100px] h-8\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-edit\",\n                                                                \"data-tooltip-content\": \"编辑\",\n                                                                onClick: ()=>openEditDialog(user),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                        id: \"tooltip-edit\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-trash\",\n                                                                \"data-tooltip-content\": \"删除\",\n                                                                onClick: ()=>handleDeleteUser(user.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Mail_Phone_Plus_Search_Trash2_UserCheck_UserX_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                        id: \"tooltip-trash\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                        lineNumber: 499,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isEditDialogOpen,\n                onOpenChange: setIsEditDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"编辑用户\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: \"修改用户信息和权限设置\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"editUsername\",\n                                                    children: \"用户名\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"editUsername\",\n                                                    value: formData.username,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            username: e.target.value\n                                                        }),\n                                                    placeholder: \"输入用户名\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"editName\",\n                                                    children: \"姓名\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"editName\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"输入真实姓名\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"editEmail\",\n                                            children: \"邮箱\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"editEmail\",\n                                            type: \"email\",\n                                            value: formData.email,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    email: e.target.value\n                                                }),\n                                            placeholder: \"输入邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"editPhone\",\n                                            children: \"电话\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"editPhone\",\n                                            value: formData.phone,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    phone: e.target.value\n                                                }),\n                                            placeholder: \"输入电话号码（可选）\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"editRole\",\n                                                    children: \"角色\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                    value: formData.role,\n                                                    onValueChange: (value)=>setFormData({\n                                                            ...formData,\n                                                            role: value\n                                                        }),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                placeholder: \"选择角色\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                            children: roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: role,\n                                                                    children: role\n                                                                }, role, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"editStatus\",\n                                                    children: \"状态\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>setFormData({\n                                                            ...formData,\n                                                            status: value\n                                                        }),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: \"active\",\n                                                                    children: \"正常\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: \"inactive\",\n                                                                    children: \"未激活\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: \"suspended\",\n                                                                    children: \"已停用\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setIsEditDialogOpen(false),\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleEditUser,\n                                            disabled: !formData.username.trim() || !formData.email.trim(),\n                                            children: \"保存更改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n                lineNumber: 511,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\user-management.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s(UserManagement, \"1R8SSe/MNGkl/IBxMpebNmaGUPs=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/user-management.tsx\n"));

/***/ })

});