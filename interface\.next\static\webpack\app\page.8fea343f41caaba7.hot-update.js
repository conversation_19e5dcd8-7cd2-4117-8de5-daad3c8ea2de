"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/favorite-queries.tsx":
/*!*****************************************!*\
  !*** ./components/favorite-queries.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FavoriteQueries)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst mockFavoriteQueries = [\n    {\n        id: \"1\",\n        title: \"销售额前10的产品\",\n        query: \"SELECT product_name, SUM(sales_amount) as total_sales FROM sales_data GROUP BY product_name ORDER BY total_sales DESC LIMIT 10\",\n        createdAt: \"2024-01-15\",\n        tags: [\n            \"销售\",\n            \"产品\"\n        ]\n    },\n    {\n        id: \"2\",\n        title: \"月度用户增长趋势\",\n        query: \"SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as new_users FROM users GROUP BY month ORDER BY month\",\n        createdAt: \"2024-01-14\",\n        tags: [\n            \"用户\",\n            \"增长\"\n        ]\n    },\n    {\n        id: \"3\",\n        title: \"订单状态分布\",\n        query: \"SELECT status, COUNT(*) as count FROM orders GROUP BY status\",\n        createdAt: \"2024-01-13\",\n        tags: [\n            \"订单\",\n            \"状态\"\n        ]\n    }\n];\nfunction FavoriteQueries() {\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [searchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [favoriteQueries, setFavoriteQueries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 加载收藏查询\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FavoriteQueries.useEffect\": ()=>{\n            const loadFavorites = {\n                \"FavoriteQueries.useEffect.loadFavorites\": async ()=>{\n                    if (!user) return;\n                    try {\n                        setIsLoading(true);\n                        const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('/favorites/');\n                        // 转换后端数据格式\n                        const transformedFavorites = response.map({\n                            \"FavoriteQueries.useEffect.loadFavorites.transformedFavorites\": (item)=>({\n                                    id: item.id.toString(),\n                                    title: item.name,\n                                    query: item.query_id ? '' : '',\n                                    createdAt: new Date(item.created_at).toISOString().split('T')[0],\n                                    tags: item.tags ? JSON.parse(item.tags).tags || [] : []\n                                })\n                        }[\"FavoriteQueries.useEffect.loadFavorites.transformedFavorites\"]);\n                        setFavoriteQueries(transformedFavorites);\n                    } catch (error) {\n                        console.error('加载收藏查询失败:', error);\n                        // 如果API调用失败，使用模拟数据作为后备\n                        setFavoriteQueries(mockFavoriteQueries);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FavoriteQueries.useEffect.loadFavorites\"];\n            loadFavorites();\n        }\n    }[\"FavoriteQueries.useEffect\"], [\n        user\n    ]);\n    const filteredQueries = favoriteQueries.filter((query)=>query.title.toLowerCase().includes(searchTerm.toLowerCase()) || query.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n    // 加载状态\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2 px-2\",\n                    children: \"收藏的成功查询\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border border-gray-200 rounded-lg animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm font-medium text-gray-500 mb-2 px-2\",\n                children: \"收藏的成功查询\"\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: [\n                    filteredQueries.map((query)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-sm text-gray-900 line-clamp-1\",\n                                            children: query.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4 text-yellow-500 fill-current flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 mb-2 line-clamp-2 font-mono bg-gray-50 p-2 rounded\",\n                                    children: query.query\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: query.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\",\n                                                    children: tag\n                                                }, tag, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                query.createdAt\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, query.id, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this)),\n                    filteredQueries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-2 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"暂无收藏的查询\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400 mt-1\",\n                                children: searchTerm ? \"未找到匹配的查询\" : \"开始收藏您常用的查询吧\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\favorite-queries.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n_s(FavoriteQueries, \"DTFN01FyULzmg6bJOVd1VkHfv/Q=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = FavoriteQueries;\nvar _c;\n$RefreshReg$(_c, \"FavoriteQueries\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/favorite-queries.tsx\n"));

/***/ })

});