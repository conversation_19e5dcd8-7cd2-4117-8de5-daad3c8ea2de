"""
角色管理服务层
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.role import Role, Permission, DataPermission, DEFAULT_PERMISSIONS, DEFAULT_ROLES
from app.models.user import User
from app.schemas.role import (
    RoleCreate, RoleUpdate, RoleStatistics, 
    SystemHealthCheck, SecurityAlert, RecentActivity
)


class RoleService:
    """角色管理服务"""

    def __init__(self, db: Session):
        self.db = db

    def init_default_data(self) -> bool:
        """初始化默认权限和角色"""
        try:
            # 检查是否已经初始化过
            existing_permissions = self.db.query(Permission).count()
            if existing_permissions > 0:
                return True

            # 创建默认权限
            for perm_data in DEFAULT_PERMISSIONS:
                permission = Permission(**perm_data)
                self.db.add(permission)

            self.db.commit()

            # 创建默认角色
            for role_data in DEFAULT_ROLES:
                permissions = role_data.pop("permissions", [])
                role = Role(**role_data)
                self.db.add(role)
                self.db.flush()  # 获取role.id

                # 分配权限
                for perm_code in permissions:
                    permission = self.db.query(Permission).filter(Permission.code == perm_code).first()
                    if permission:
                        role.permissions.append(permission)

            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise e

    def get_all_roles(self, include_inactive: bool = False) -> List[Role]:
        """获取所有角色"""
        query = self.db.query(Role)
        if not include_inactive:
            query = query.filter(Role.is_active == True)
        return query.all()

    def get_role_by_id(self, role_id: int) -> Optional[Role]:
        """根据ID获取角色"""
        return self.db.query(Role).filter(Role.id == role_id).first()

    def get_role_by_name(self, name: str) -> Optional[Role]:
        """根据名称获取角色"""
        return self.db.query(Role).filter(Role.name == name).first()

    def create_role(self, role_create: RoleCreate) -> Role:
        """创建角色"""
        # 检查角色名是否已存在
        existing = self.get_role_by_name(role_create.name)
        if existing:
            raise ValueError(f"角色名称 '{role_create.name}' 已存在")

        # 创建角色
        role = Role(
            name=role_create.name,
            description=role_create.description,
            is_active=role_create.is_active,
            is_system=False
        )
        self.db.add(role)
        self.db.flush()

        # 分配权限
        self._assign_permissions(role, role_create.permissions, role_create.data_permissions)

        self.db.commit()
        self.db.refresh(role)
        return role

    def update_role(self, role_id: int, role_update: RoleUpdate) -> Optional[Role]:
        """更新角色"""
        role = self.get_role_by_id(role_id)
        if not role:
            return None

        # 检查是否为系统角色
        if role.is_system and role_update.name and role_update.name != role.name:
            raise ValueError("系统角色名称不能修改")

        # 更新基本信息
        if role_update.name is not None:
            # 检查新名称是否已存在
            existing = self.get_role_by_name(role_update.name)
            if existing and existing.id != role_id:
                raise ValueError(f"角色名称 '{role_update.name}' 已存在")
            role.name = role_update.name

        if role_update.description is not None:
            role.description = role_update.description

        if role_update.is_active is not None:
            role.is_active = role_update.is_active

        # 更新权限
        if role_update.permissions is not None or role_update.data_permissions is not None:
            permissions = role_update.permissions if role_update.permissions is not None else [p.code for p in role.permissions]
            data_permissions = role_update.data_permissions if role_update.data_permissions is not None else [dp.code for dp in role.data_permissions]
            self._assign_permissions(role, permissions, data_permissions)

        self.db.commit()
        self.db.refresh(role)
        return role

    def delete_role(self, role_id: int) -> bool:
        """删除角色"""
        role = self.get_role_by_id(role_id)
        if not role:
            return False

        # 检查是否为系统角色
        if role.is_system:
            raise ValueError("系统角色不能删除")

        # 检查是否有用户使用此角色
        if len(role.users) > 0:
            raise ValueError(f"角色 '{role.name}' 正在被 {len(role.users)} 个用户使用，无法删除")

        self.db.delete(role)
        self.db.commit()
        return True

    def _assign_permissions(self, role: Role, permission_codes: List[str], data_permission_codes: List[str]):
        """分配权限给角色"""
        # 清除现有权限
        role.permissions.clear()
        role.data_permissions.clear()

        # 分配操作权限
        for perm_code in permission_codes:
            permission = self.db.query(Permission).filter(Permission.code == perm_code).first()
            if permission:
                role.permissions.append(permission)

        # 分配数据权限
        for dp_code in data_permission_codes:
            data_permission = self.db.query(DataPermission).filter(DataPermission.code == dp_code).first()
            if data_permission:
                role.data_permissions.append(data_permission)

    def get_all_permissions(self) -> List[Permission]:
        """获取所有权限"""
        return self.db.query(Permission).all()

    def get_permissions_by_category(self) -> Dict[str, List[Permission]]:
        """按分类获取权限"""
        permissions = self.get_all_permissions()
        result = {}
        for perm in permissions:
            if perm.category not in result:
                result[perm.category] = []
            result[perm.category].append(perm)
        return result

    def get_all_data_permissions(self) -> List[DataPermission]:
        """获取所有数据权限"""
        return self.db.query(DataPermission).all()

    def get_role_statistics(self) -> RoleStatistics:
        """获取角色统计信息"""
        total_roles = self.db.query(Role).count()
        system_roles = self.db.query(Role).filter(Role.is_system == True).count()
        custom_roles = total_roles - system_roles
        active_roles = self.db.query(Role).filter(Role.is_active == True).count()
        total_permissions = self.db.query(Permission).count()
        total_data_permissions = self.db.query(DataPermission).count()

        return RoleStatistics(
            total_roles=total_roles,
            system_roles=system_roles,
            custom_roles=custom_roles,
            active_roles=active_roles,
            total_permissions=total_permissions,
            total_data_permissions=total_data_permissions
        )

    def assign_roles_to_user(self, user_id: int, role_ids: List[int]) -> bool:
        """为用户分配角色"""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError(f"用户 ID {user_id} 不存在")

        # 清除现有角色
        user.roles.clear()

        # 分配新角色
        for role_id in role_ids:
            role = self.get_role_by_id(role_id)
            if role and role.is_active:
                user.roles.append(role)

        self.db.commit()
        return True

    def get_user_roles(self, user_id: int) -> List[Role]:
        """获取用户的角色"""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            return []
        return user.roles

    def check_user_permission(self, user_id: int, permission_code: str) -> bool:
        """检查用户是否有指定权限"""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            return False

        for role in user.roles:
            if not role.is_active:
                continue
            for permission in role.permissions:
                if permission.code == permission_code:
                    return True
        return False

    def get_system_health(self) -> SystemHealthCheck:
        """获取系统健康状态"""
        import psutil
        import time
        from datetime import datetime

        # 获取系统资源使用情况
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # 检查数据库连接
        try:
            self.db.execute("SELECT 1")
            db_status = "healthy"
        except:
            db_status = "error"

        return SystemHealthCheck(
            database_status=db_status,
            cache_status="healthy",  # 简化实现
            api_status="healthy",
            disk_usage=disk.percent,
            memory_usage=memory.percent,
            cpu_usage=cpu_usage,
            uptime=int(time.time()),  # 简化实现
            last_check=datetime.now()
        )

    def get_recent_activities(self, limit: int = 10) -> List[RecentActivity]:
        """获取最近活动"""
        # 这里应该从审计日志表获取数据，简化实现返回模拟数据
        from datetime import datetime, timedelta
        
        activities = []
        for i in range(limit):
            activities.append(RecentActivity(
                id=i + 1,
                user_id=1,
                username="admin",
                action="login",
                resource_type="user",
                resource_id=1,
                description="用户登录系统",
                ip_address="127.0.0.1",
                created_at=datetime.now() - timedelta(minutes=i * 10),
                status="success"
            ))
        
        return activities

    def get_security_alerts(self) -> List[SecurityAlert]:
        """获取安全警报"""
        # 简化实现，返回模拟数据
        from datetime import datetime
        
        return [
            SecurityAlert(
                id=1,
                type="login_attempt",
                severity="medium",
                message="检测到异常登录尝试",
                details={"ip": "*************", "attempts": 3},
                created_at=datetime.now(),
                resolved=False
            )
        ]
