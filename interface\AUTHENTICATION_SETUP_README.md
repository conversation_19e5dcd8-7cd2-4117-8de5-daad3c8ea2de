# NL2SQL 用户认证系统设置指南

本文档说明了NL2SQL系统的用户认证和安全机制的实现。

## 🔐 认证系统概述

NL2SQL系统现在具有完整的用户认证和权限控制系统，包括：

- **JWT Token认证**：基于JSON Web Token的无状态认证
- **角色权限控制**：支持管理员、分析师、普通用户三种角色
- **路由保护**：前端和后端双重路由保护
- **会话管理**：自动token验证和刷新
- **多标签页同步**：跨标签页的登录状态同步

## 🏗️ 系统架构

### 前端认证组件

1. **认证上下文** (`contexts/auth-context.tsx`)
   - 全局用户状态管理
   - 登录/登出功能
   - Token验证和刷新
   - 权限检查

2. **路由保护组件** (`components/protected-route.tsx`)
   - `ProtectedRoute`: 通用路由保护
   - `AdminRoute`: 管理员专用路由
   - `AnalystRoute`: 分析师及以上权限路由

3. **认证中间件** (`middleware.ts`)
   - 服务器端路由保护
   - Token验证
   - 权限检查

### 后端认证系统

1. **JWT Token管理** (`app/core/security.py`)
   - Token生成和验证
   - 密码加密和验证
   - 用户认证依赖注入

2. **用户模型** (`app/models/user.py`)
   - 用户信息存储
   - 角色权限定义

## 🚀 快速开始

### 1. 确保后端认证系统正常运行

```bash
cd backend
python verify_database_config.py
python main.py
```

### 2. 启动前端应用

```bash
cd interface
npm run dev
```

### 3. 测试认证流程

1. 访问 `http://localhost:3000`
2. 系统会自动重定向到登录页面
3. 使用测试账户登录（需要先在数据库中创建用户）
4. 登录成功后可以访问受保护的页面

## 👥 用户角色和权限

### 角色层级

1. **管理员 (admin)**
   - 完整系统权限
   - 可以访问所有页面和功能
   - 可以管理用户和系统设置

2. **分析师 (analyst)**
   - 数据分析权限
   - 可以访问查询、文档等功能
   - 不能访问系统管理功能

3. **普通用户 (user)**
   - 基础查询权限
   - 可以进行数据查询和查看结果
   - 不能访问高级功能

### 页面权限控制

| 页面 | 普通用户 | 分析师 | 管理员 |
|------|----------|--------|--------|
| 首页 (/) | ✅ | ✅ | ✅ |
| 个人中心 (/profile) | ✅ | ✅ | ✅ |
| 应用设置 (/settings) | ✅ | ✅ | ✅ |
| 帮助文档 (/docs) | ❌ | ✅ | ✅ |
| 系统管理 (/admin-settings) | ❌ | ❌ | ✅ |

## 🔧 配置说明

### 环境变量配置

确保后端 `.env` 文件包含以下配置：

```env
# JWT配置
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置
DB_TYPE=mysql
DB_HOST=your_db_host
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=nl2sql_system
```

### 前端API配置

前端通过 `next.config.mjs` 配置API代理：

```javascript
async rewrites() {
  return [
    {
      source: '/api/:path*',
      destination: 'http://localhost:8000/api/:path*',
    },
  ];
}
```

## 🛡️ 安全特性

### 1. Token安全

- **自动过期**：Token有效期30分钟（可配置）
- **安全存储**：Token存储在localStorage中
- **自动清理**：登出时清除所有认证信息

### 2. 路由保护

- **双重保护**：前端和后端都有路由保护
- **权限检查**：基于用户角色的细粒度权限控制
- **自动重定向**：未认证用户自动重定向到登录页

### 3. 会话管理

- **状态同步**：多标签页之间的登录状态同步
- **自动验证**：页面加载时自动验证token有效性
- **优雅降级**：认证失败时的优雅处理

## 🔍 故障排除

### 常见问题

#### 1. 无法登录

**可能原因：**
- 数据库连接问题
- 用户不存在或密码错误
- JWT配置错误

**解决方案：**
```bash
# 检查数据库连接
cd backend
python verify_database_config.py

# 检查用户是否存在
mysql -u your_user -p
USE nl2sql_system;
SELECT * FROM users;
```

#### 2. 登录后立即退出

**可能原因：**
- Token验证失败
- 用户信息获取失败
- 权限检查错误

**解决方案：**
- 检查浏览器控制台错误信息
- 验证后端API是否正常响应
- 检查用户角色配置

#### 3. 页面访问被拒绝

**可能原因：**
- 用户权限不足
- 路由保护配置错误

**解决方案：**
- 检查用户角色是否正确
- 验证路由保护配置
- 查看中间件日志

### 调试模式

启用调试模式查看详细日志：

```env
# 后端调试
DEBUG=true

# 前端调试
NEXT_PUBLIC_DEBUG=true
```

## 📝 开发指南

### 添加新的受保护页面

1. **创建页面组件**：
```tsx
"use client"
import ProtectedRoute from "@/components/protected-route"

export default function NewPage() {
  return (
    <ProtectedRoute requiredRole="analyst">
      <div>受保护的内容</div>
    </ProtectedRoute>
  )
}
```

2. **更新中间件配置**：
```typescript
// middleware.ts
const protectedRoutes = [
  // ... 现有路由
  '/new-page'
]
```

### 添加新的用户角色

1. **更新角色层级**：
```typescript
// contexts/auth-context.tsx
const roleHierarchy = {
  'admin': 4,
  'manager': 3,  // 新角色
  'analyst': 2,
  'user': 1
}
```

2. **更新权限检查**：
```typescript
// components/protected-route.tsx
const checkUserPermission = (userRole: string, requiredRole: string): boolean => {
  // 更新权限检查逻辑
}
```

## 🔄 更新和维护

### 定期安全检查

1. **更新依赖包**：
```bash
npm audit
npm update
```

2. **检查Token安全**：
- 定期更换SECRET_KEY
- 调整Token过期时间
- 监控异常登录

3. **权限审计**：
- 定期检查用户权限
- 清理无效用户
- 更新角色定义

### 性能优化

1. **Token缓存**：
- 实现Token刷新机制
- 优化认证状态检查频率

2. **路由优化**：
- 减少不必要的权限检查
- 优化中间件性能

## 📞 支持

如果在使用过程中遇到问题：

1. 检查本文档的故障排除部分
2. 查看浏览器控制台和网络请求
3. 检查后端日志
4. 联系技术支持团队

---

**注意**：在生产环境中部署之前，请务必：
- 使用强密码和安全的SECRET_KEY
- 启用HTTPS
- 配置适当的CORS策略
- 实施日志监控和告警
