"use client"

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: string
  fallback?: React.ReactNode
  redirectTo?: string
}

// 加载组件
function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <p className="text-sm text-gray-600">正在验证身份...</p>
      </div>
    </div>
  )
}

// 未授权组件
function UnauthorizedAccess({ requiredRole }: { requiredRole?: string }) {
  const router = useRouter()
  
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
        <div className="mb-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          访问被拒绝
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {requiredRole 
            ? `您需要 ${requiredRole} 权限才能访问此页面。`
            : '您没有权限访问此页面。'
          }
        </p>
        <div className="flex space-x-3">
          <button
            onClick={() => router.back()}
            className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded transition-colors"
          >
            返回
          </button>
          <button
            onClick={() => router.push('/')}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors"
          >
            回到首页
          </button>
        </div>
      </div>
    </div>
  )
}

// 路由保护组件
export default function ProtectedRoute({
  children,
  requiredRole,
  fallback,
  redirectTo = '/login'
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated, checkAuth } = useAuth()
  const router = useRouter()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const verifyAuth = async () => {
      try {
        setIsChecking(true)
        
        // 如果正在加载认证状态，等待完成
        if (isLoading) {
          return
        }

        // 如果未认证，重定向到登录页
        if (!isAuthenticated) {
          console.log('用户未认证，重定向到登录页')
          router.push(redirectTo)
          return
        }

        // 再次验证认证状态
        const isValid = await checkAuth()
        if (!isValid) {
          console.log('认证验证失败，重定向到登录页')
          router.push(redirectTo)
          return
        }

        // 检查角色权限
        if (requiredRole && user) {
          const hasPermission = checkUserPermission(user.role, requiredRole)
          if (!hasPermission) {
            console.log(`用户权限不足，需要: ${requiredRole}, 当前: ${user.role}`)
            // 权限不足时不重定向，显示未授权页面
            return
          }
        }

      } catch (error) {
        console.error('认证验证过程中发生错误:', error)
        router.push(redirectTo)
      } finally {
        setIsChecking(false)
      }
    }

    verifyAuth()
  }, [isLoading, isAuthenticated, user, requiredRole, router, redirectTo, checkAuth])

  // 检查用户权限的辅助函数
  const checkUserPermission = (userRole: string, requiredRole: string): boolean => {
    // 管理员拥有所有权限
    if (userRole === 'admin') return true
    
    // 角色层级定义
    const roleHierarchy = {
      'admin': 3,
      'analyst': 2,
      'user': 1
    }
    
    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0
    
    return userLevel >= requiredLevel
  }

  // 如果正在加载认证状态或正在检查权限，显示加载界面
  if (isLoading || isChecking) {
    return fallback || <LoadingSpinner />
  }

  // 如果未认证，不渲染任何内容（等待重定向）
  if (!isAuthenticated) {
    return null
  }

  // 如果需要特定角色但用户权限不足，显示未授权页面
  if (requiredRole && user && !checkUserPermission(user.role, requiredRole)) {
    return <UnauthorizedAccess requiredRole={requiredRole} />
  }

  // 认证通过且权限足够，渲染子组件
  return <>{children}</>
}

// 高阶组件版本的路由保护
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requiredRole?: string
    redirectTo?: string
  }
) {
  const WrappedComponent = (props: P) => {
    return (
      <ProtectedRoute
        requiredRole={options?.requiredRole}
        redirectTo={options?.redirectTo}
      >
        <Component {...props} />
      </ProtectedRoute>
    )
  }

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// 管理员专用路由保护组件
export function AdminRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole="admin">
      {children}
    </ProtectedRoute>
  )
}

// 分析师及以上权限路由保护组件
export function AnalystRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRole="analyst">
      {children}
    </ProtectedRoute>
  )
}
