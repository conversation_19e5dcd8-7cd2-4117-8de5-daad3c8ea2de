"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/reselect";
exports.ids = ["vendor-chunks/reselect"];
exports.modules = {

/***/ "(ssr)/./node_modules/reselect/dist/reselect.mjs":
/*!*************************************************!*\
  !*** ./node_modules/reselect/dist/reselect.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSelector: () => (/* binding */ createSelector),\n/* harmony export */   createSelectorCreator: () => (/* binding */ createSelectorCreator),\n/* harmony export */   createStructuredSelector: () => (/* binding */ createStructuredSelector),\n/* harmony export */   lruMemoize: () => (/* binding */ lruMemoize),\n/* harmony export */   referenceEqualityCheck: () => (/* binding */ referenceEqualityCheck),\n/* harmony export */   setGlobalDevModeChecks: () => (/* binding */ setGlobalDevModeChecks),\n/* harmony export */   unstable_autotrackMemoize: () => (/* binding */ autotrackMemoize),\n/* harmony export */   weakMapMemoize: () => (/* binding */ weakMapMemoize)\n/* harmony export */ });\n// src/devModeChecks/identityFunctionCheck.ts\nvar runIdentityFunctionCheck = (resultFunc, inputSelectorsResults, outputSelectorResult) => {\n  if (inputSelectorsResults.length === 1 && inputSelectorsResults[0] === outputSelectorResult) {\n    let isInputSameAsOutput = false;\n    try {\n      const emptyObject = {};\n      if (resultFunc(emptyObject) === emptyObject)\n        isInputSameAsOutput = true;\n    } catch {\n    }\n    if (isInputSameAsOutput) {\n      let stack = void 0;\n      try {\n        throw new Error();\n      } catch (e) {\n        ;\n        ({ stack } = e);\n      }\n      console.warn(\n        \"The result function returned its own inputs without modification. e.g\\n`createSelector([state => state.todos], todos => todos)`\\nThis could lead to inefficient memoization and unnecessary re-renders.\\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.\",\n        { stack }\n      );\n    }\n  }\n};\n\n// src/devModeChecks/inputStabilityCheck.ts\nvar runInputStabilityCheck = (inputSelectorResultsObject, options, inputSelectorArgs) => {\n  const { memoize, memoizeOptions } = options;\n  const { inputSelectorResults, inputSelectorResultsCopy } = inputSelectorResultsObject;\n  const createAnEmptyObject = memoize(() => ({}), ...memoizeOptions);\n  const areInputSelectorResultsEqual = createAnEmptyObject.apply(null, inputSelectorResults) === createAnEmptyObject.apply(null, inputSelectorResultsCopy);\n  if (!areInputSelectorResultsEqual) {\n    let stack = void 0;\n    try {\n      throw new Error();\n    } catch (e) {\n      ;\n      ({ stack } = e);\n    }\n    console.warn(\n      \"An input selector returned a different result when passed same arguments.\\nThis means your output selector will likely run more frequently than intended.\\nAvoid returning a new reference inside your input selector, e.g.\\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`\",\n      {\n        arguments: inputSelectorArgs,\n        firstInputs: inputSelectorResults,\n        secondInputs: inputSelectorResultsCopy,\n        stack\n      }\n    );\n  }\n};\n\n// src/devModeChecks/setGlobalDevModeChecks.ts\nvar globalDevModeChecks = {\n  inputStabilityCheck: \"once\",\n  identityFunctionCheck: \"once\"\n};\nvar setGlobalDevModeChecks = (devModeChecks) => {\n  Object.assign(globalDevModeChecks, devModeChecks);\n};\n\n// src/utils.ts\nvar NOT_FOUND = /* @__PURE__ */ Symbol(\"NOT_FOUND\");\nfunction assertIsFunction(func, errorMessage = `expected a function, instead received ${typeof func}`) {\n  if (typeof func !== \"function\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsObject(object, errorMessage = `expected an object, instead received ${typeof object}`) {\n  if (typeof object !== \"object\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsArrayOfFunctions(array, errorMessage = `expected all items to be functions, instead received the following types: `) {\n  if (!array.every((item) => typeof item === \"function\")) {\n    const itemTypes = array.map(\n      (item) => typeof item === \"function\" ? `function ${item.name || \"unnamed\"}()` : typeof item\n    ).join(\", \");\n    throw new TypeError(`${errorMessage}[${itemTypes}]`);\n  }\n}\nvar ensureIsArray = (item) => {\n  return Array.isArray(item) ? item : [item];\n};\nfunction getDependencies(createSelectorArgs) {\n  const dependencies = Array.isArray(createSelectorArgs[0]) ? createSelectorArgs[0] : createSelectorArgs;\n  assertIsArrayOfFunctions(\n    dependencies,\n    `createSelector expects all input-selectors to be functions, but received the following types: `\n  );\n  return dependencies;\n}\nfunction collectInputSelectorResults(dependencies, inputSelectorArgs) {\n  const inputSelectorResults = [];\n  const { length } = dependencies;\n  for (let i = 0; i < length; i++) {\n    inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs));\n  }\n  return inputSelectorResults;\n}\nvar getDevModeChecksExecutionInfo = (firstRun, devModeChecks) => {\n  const { identityFunctionCheck, inputStabilityCheck } = {\n    ...globalDevModeChecks,\n    ...devModeChecks\n  };\n  return {\n    identityFunctionCheck: {\n      shouldRun: identityFunctionCheck === \"always\" || identityFunctionCheck === \"once\" && firstRun,\n      run: runIdentityFunctionCheck\n    },\n    inputStabilityCheck: {\n      shouldRun: inputStabilityCheck === \"always\" || inputStabilityCheck === \"once\" && firstRun,\n      run: runInputStabilityCheck\n    }\n  };\n};\n\n// src/autotrackMemoize/autotracking.ts\nvar $REVISION = 0;\nvar CURRENT_TRACKER = null;\nvar Cell = class {\n  revision = $REVISION;\n  _value;\n  _lastValue;\n  _isEqual = tripleEq;\n  constructor(initialValue, isEqual = tripleEq) {\n    this._value = this._lastValue = initialValue;\n    this._isEqual = isEqual;\n  }\n  // Whenever a storage value is read, it'll add itself to the current tracker if\n  // one exists, entangling its state with that cache.\n  get value() {\n    CURRENT_TRACKER?.add(this);\n    return this._value;\n  }\n  // Whenever a storage value is updated, we bump the global revision clock,\n  // assign the revision for this storage to the new value, _and_ we schedule a\n  // rerender. This is important, and it's what makes autotracking  _pull_\n  // based. We don't actively tell the caches which depend on the storage that\n  // anything has happened. Instead, we recompute the caches when needed.\n  set value(newValue) {\n    if (this.value === newValue)\n      return;\n    this._value = newValue;\n    this.revision = ++$REVISION;\n  }\n};\nfunction tripleEq(a, b) {\n  return a === b;\n}\nvar TrackingCache = class {\n  _cachedValue;\n  _cachedRevision = -1;\n  _deps = [];\n  hits = 0;\n  fn;\n  constructor(fn) {\n    this.fn = fn;\n  }\n  clear() {\n    this._cachedValue = void 0;\n    this._cachedRevision = -1;\n    this._deps = [];\n    this.hits = 0;\n  }\n  get value() {\n    if (this.revision > this._cachedRevision) {\n      const { fn } = this;\n      const currentTracker = /* @__PURE__ */ new Set();\n      const prevTracker = CURRENT_TRACKER;\n      CURRENT_TRACKER = currentTracker;\n      this._cachedValue = fn();\n      CURRENT_TRACKER = prevTracker;\n      this.hits++;\n      this._deps = Array.from(currentTracker);\n      this._cachedRevision = this.revision;\n    }\n    CURRENT_TRACKER?.add(this);\n    return this._cachedValue;\n  }\n  get revision() {\n    return Math.max(...this._deps.map((d) => d.revision), 0);\n  }\n};\nfunction getValue(cell) {\n  if (!(cell instanceof Cell)) {\n    console.warn(\"Not a valid cell! \", cell);\n  }\n  return cell.value;\n}\nfunction setValue(storage, value) {\n  if (!(storage instanceof Cell)) {\n    throw new TypeError(\n      \"setValue must be passed a tracked store created with `createStorage`.\"\n    );\n  }\n  storage.value = storage._lastValue = value;\n}\nfunction createCell(initialValue, isEqual = tripleEq) {\n  return new Cell(initialValue, isEqual);\n}\nfunction createCache(fn) {\n  assertIsFunction(\n    fn,\n    \"the first parameter to `createCache` must be a function\"\n  );\n  return new TrackingCache(fn);\n}\n\n// src/autotrackMemoize/tracking.ts\nvar neverEq = (a, b) => false;\nfunction createTag() {\n  return createCell(null, neverEq);\n}\nfunction dirtyTag(tag, value) {\n  setValue(tag, value);\n}\nvar consumeCollection = (node) => {\n  let tag = node.collectionTag;\n  if (tag === null) {\n    tag = node.collectionTag = createTag();\n  }\n  getValue(tag);\n};\nvar dirtyCollection = (node) => {\n  const tag = node.collectionTag;\n  if (tag !== null) {\n    dirtyTag(tag, null);\n  }\n};\n\n// src/autotrackMemoize/proxy.ts\nvar REDUX_PROXY_LABEL = Symbol();\nvar nextId = 0;\nvar proto = Object.getPrototypeOf({});\nvar ObjectTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy(this, objectProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar objectProxyHandler = {\n  get(node, key) {\n    function calculateResult() {\n      const { value } = node;\n      const childValue = Reflect.get(value, key);\n      if (typeof key === \"symbol\") {\n        return childValue;\n      }\n      if (key in proto) {\n        return childValue;\n      }\n      if (typeof childValue === \"object\" && childValue !== null) {\n        let childNode = node.children[key];\n        if (childNode === void 0) {\n          childNode = node.children[key] = createNode(childValue);\n        }\n        if (childNode.tag) {\n          getValue(childNode.tag);\n        }\n        return childNode.proxy;\n      } else {\n        let tag = node.tags[key];\n        if (tag === void 0) {\n          tag = node.tags[key] = createTag();\n          tag.value = childValue;\n        }\n        getValue(tag);\n        return childValue;\n      }\n    }\n    const res = calculateResult();\n    return res;\n  },\n  ownKeys(node) {\n    consumeCollection(node);\n    return Reflect.ownKeys(node.value);\n  },\n  getOwnPropertyDescriptor(node, prop) {\n    return Reflect.getOwnPropertyDescriptor(node.value, prop);\n  },\n  has(node, prop) {\n    return Reflect.has(node.value, prop);\n  }\n};\nvar ArrayTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy([this], arrayProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar arrayProxyHandler = {\n  get([node], key) {\n    if (key === \"length\") {\n      consumeCollection(node);\n    }\n    return objectProxyHandler.get(node, key);\n  },\n  ownKeys([node]) {\n    return objectProxyHandler.ownKeys(node);\n  },\n  getOwnPropertyDescriptor([node], prop) {\n    return objectProxyHandler.getOwnPropertyDescriptor(node, prop);\n  },\n  has([node], prop) {\n    return objectProxyHandler.has(node, prop);\n  }\n};\nfunction createNode(value) {\n  if (Array.isArray(value)) {\n    return new ArrayTreeNode(value);\n  }\n  return new ObjectTreeNode(value);\n}\nfunction updateNode(node, newValue) {\n  const { value, tags, children } = node;\n  node.value = newValue;\n  if (Array.isArray(value) && Array.isArray(newValue) && value.length !== newValue.length) {\n    dirtyCollection(node);\n  } else {\n    if (value !== newValue) {\n      let oldKeysSize = 0;\n      let newKeysSize = 0;\n      let anyKeysAdded = false;\n      for (const _key in value) {\n        oldKeysSize++;\n      }\n      for (const key in newValue) {\n        newKeysSize++;\n        if (!(key in value)) {\n          anyKeysAdded = true;\n          break;\n        }\n      }\n      const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize;\n      if (isDifferent) {\n        dirtyCollection(node);\n      }\n    }\n  }\n  for (const key in tags) {\n    const childValue = value[key];\n    const newChildValue = newValue[key];\n    if (childValue !== newChildValue) {\n      dirtyCollection(node);\n      dirtyTag(tags[key], newChildValue);\n    }\n    if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      delete tags[key];\n    }\n  }\n  for (const key in children) {\n    const childNode = children[key];\n    const newChildValue = newValue[key];\n    const childValue = childNode.value;\n    if (childValue === newChildValue) {\n      continue;\n    } else if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      updateNode(childNode, newChildValue);\n    } else {\n      deleteNode(childNode);\n      delete children[key];\n    }\n  }\n}\nfunction deleteNode(node) {\n  if (node.tag) {\n    dirtyTag(node.tag, null);\n  }\n  dirtyCollection(node);\n  for (const key in node.tags) {\n    dirtyTag(node.tags[key], null);\n  }\n  for (const key in node.children) {\n    deleteNode(node.children[key]);\n  }\n}\n\n// src/lruMemoize.ts\nfunction createSingletonCache(equals) {\n  let entry;\n  return {\n    get(key) {\n      if (entry && equals(entry.key, key)) {\n        return entry.value;\n      }\n      return NOT_FOUND;\n    },\n    put(key, value) {\n      entry = { key, value };\n    },\n    getEntries() {\n      return entry ? [entry] : [];\n    },\n    clear() {\n      entry = void 0;\n    }\n  };\n}\nfunction createLruCache(maxSize, equals) {\n  let entries = [];\n  function get(key) {\n    const cacheIndex = entries.findIndex((entry) => equals(key, entry.key));\n    if (cacheIndex > -1) {\n      const entry = entries[cacheIndex];\n      if (cacheIndex > 0) {\n        entries.splice(cacheIndex, 1);\n        entries.unshift(entry);\n      }\n      return entry.value;\n    }\n    return NOT_FOUND;\n  }\n  function put(key, value) {\n    if (get(key) === NOT_FOUND) {\n      entries.unshift({ key, value });\n      if (entries.length > maxSize) {\n        entries.pop();\n      }\n    }\n  }\n  function getEntries() {\n    return entries;\n  }\n  function clear() {\n    entries = [];\n  }\n  return { get, put, getEntries, clear };\n}\nvar referenceEqualityCheck = (a, b) => a === b;\nfunction createCacheKeyComparator(equalityCheck) {\n  return function areArgumentsShallowlyEqual(prev, next) {\n    if (prev === null || next === null || prev.length !== next.length) {\n      return false;\n    }\n    const { length } = prev;\n    for (let i = 0; i < length; i++) {\n      if (!equalityCheck(prev[i], next[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n}\nfunction lruMemoize(func, equalityCheckOrOptions) {\n  const providedOptions = typeof equalityCheckOrOptions === \"object\" ? equalityCheckOrOptions : { equalityCheck: equalityCheckOrOptions };\n  const {\n    equalityCheck = referenceEqualityCheck,\n    maxSize = 1,\n    resultEqualityCheck\n  } = providedOptions;\n  const comparator = createCacheKeyComparator(equalityCheck);\n  let resultsCount = 0;\n  const cache = maxSize <= 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator);\n  function memoized() {\n    let value = cache.get(arguments);\n    if (value === NOT_FOUND) {\n      value = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const entries = cache.getEntries();\n        const matchingEntry = entries.find(\n          (entry) => resultEqualityCheck(entry.value, value)\n        );\n        if (matchingEntry) {\n          value = matchingEntry.value;\n          resultsCount !== 0 && resultsCount--;\n        }\n      }\n      cache.put(arguments, value);\n    }\n    return value;\n  }\n  memoized.clearCache = () => {\n    cache.clear();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/autotrackMemoize/autotrackMemoize.ts\nfunction autotrackMemoize(func) {\n  const node = createNode(\n    []\n  );\n  let lastArgs = null;\n  const shallowEqual = createCacheKeyComparator(referenceEqualityCheck);\n  const cache = createCache(() => {\n    const res = func.apply(null, node.proxy);\n    return res;\n  });\n  function memoized() {\n    if (!shallowEqual(lastArgs, arguments)) {\n      updateNode(node, arguments);\n      lastArgs = arguments;\n    }\n    return cache.value;\n  }\n  memoized.clearCache = () => {\n    return cache.clear();\n  };\n  return memoized;\n}\n\n// src/weakMapMemoize.ts\nvar StrongRef = class {\n  constructor(value) {\n    this.value = value;\n  }\n  deref() {\n    return this.value;\n  }\n};\nvar Ref = typeof WeakRef !== \"undefined\" ? WeakRef : StrongRef;\nvar UNTERMINATED = 0;\nvar TERMINATED = 1;\nfunction createCacheNode() {\n  return {\n    s: UNTERMINATED,\n    v: void 0,\n    o: null,\n    p: null\n  };\n}\nfunction weakMapMemoize(func, options = {}) {\n  let fnNode = createCacheNode();\n  const { resultEqualityCheck } = options;\n  let lastResult;\n  let resultsCount = 0;\n  function memoized() {\n    let cacheNode = fnNode;\n    const { length } = arguments;\n    for (let i = 0, l = length; i < l; i++) {\n      const arg = arguments[i];\n      if (typeof arg === \"function\" || typeof arg === \"object\" && arg !== null) {\n        let objectCache = cacheNode.o;\n        if (objectCache === null) {\n          cacheNode.o = objectCache = /* @__PURE__ */ new WeakMap();\n        }\n        const objectNode = objectCache.get(arg);\n        if (objectNode === void 0) {\n          cacheNode = createCacheNode();\n          objectCache.set(arg, cacheNode);\n        } else {\n          cacheNode = objectNode;\n        }\n      } else {\n        let primitiveCache = cacheNode.p;\n        if (primitiveCache === null) {\n          cacheNode.p = primitiveCache = /* @__PURE__ */ new Map();\n        }\n        const primitiveNode = primitiveCache.get(arg);\n        if (primitiveNode === void 0) {\n          cacheNode = createCacheNode();\n          primitiveCache.set(arg, cacheNode);\n        } else {\n          cacheNode = primitiveNode;\n        }\n      }\n    }\n    const terminatedNode = cacheNode;\n    let result;\n    if (cacheNode.s === TERMINATED) {\n      result = cacheNode.v;\n    } else {\n      result = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const lastResultValue = lastResult?.deref?.() ?? lastResult;\n        if (lastResultValue != null && resultEqualityCheck(lastResultValue, result)) {\n          result = lastResultValue;\n          resultsCount !== 0 && resultsCount--;\n        }\n        const needsWeakRef = typeof result === \"object\" && result !== null || typeof result === \"function\";\n        lastResult = needsWeakRef ? new Ref(result) : result;\n      }\n    }\n    terminatedNode.s = TERMINATED;\n    terminatedNode.v = result;\n    return result;\n  }\n  memoized.clearCache = () => {\n    fnNode = createCacheNode();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/createSelectorCreator.ts\nfunction createSelectorCreator(memoizeOrOptions, ...memoizeOptionsFromArgs) {\n  const createSelectorCreatorOptions = typeof memoizeOrOptions === \"function\" ? {\n    memoize: memoizeOrOptions,\n    memoizeOptions: memoizeOptionsFromArgs\n  } : memoizeOrOptions;\n  const createSelector2 = (...createSelectorArgs) => {\n    let recomputations = 0;\n    let dependencyRecomputations = 0;\n    let lastResult;\n    let directlyPassedOptions = {};\n    let resultFunc = createSelectorArgs.pop();\n    if (typeof resultFunc === \"object\") {\n      directlyPassedOptions = resultFunc;\n      resultFunc = createSelectorArgs.pop();\n    }\n    assertIsFunction(\n      resultFunc,\n      `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`\n    );\n    const combinedOptions = {\n      ...createSelectorCreatorOptions,\n      ...directlyPassedOptions\n    };\n    const {\n      memoize,\n      memoizeOptions = [],\n      argsMemoize = weakMapMemoize,\n      argsMemoizeOptions = [],\n      devModeChecks = {}\n    } = combinedOptions;\n    const finalMemoizeOptions = ensureIsArray(memoizeOptions);\n    const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions);\n    const dependencies = getDependencies(createSelectorArgs);\n    const memoizedResultFunc = memoize(function recomputationWrapper() {\n      recomputations++;\n      return resultFunc.apply(\n        null,\n        arguments\n      );\n    }, ...finalMemoizeOptions);\n    let firstRun = true;\n    const selector = argsMemoize(function dependenciesChecker() {\n      dependencyRecomputations++;\n      const inputSelectorResults = collectInputSelectorResults(\n        dependencies,\n        arguments\n      );\n      lastResult = memoizedResultFunc.apply(null, inputSelectorResults);\n      if (true) {\n        const { identityFunctionCheck, inputStabilityCheck } = getDevModeChecksExecutionInfo(firstRun, devModeChecks);\n        if (identityFunctionCheck.shouldRun) {\n          identityFunctionCheck.run(\n            resultFunc,\n            inputSelectorResults,\n            lastResult\n          );\n        }\n        if (inputStabilityCheck.shouldRun) {\n          const inputSelectorResultsCopy = collectInputSelectorResults(\n            dependencies,\n            arguments\n          );\n          inputStabilityCheck.run(\n            { inputSelectorResults, inputSelectorResultsCopy },\n            { memoize, memoizeOptions: finalMemoizeOptions },\n            arguments\n          );\n        }\n        if (firstRun)\n          firstRun = false;\n      }\n      return lastResult;\n    }, ...finalArgsMemoizeOptions);\n    return Object.assign(selector, {\n      resultFunc,\n      memoizedResultFunc,\n      dependencies,\n      dependencyRecomputations: () => dependencyRecomputations,\n      resetDependencyRecomputations: () => {\n        dependencyRecomputations = 0;\n      },\n      lastResult: () => lastResult,\n      recomputations: () => recomputations,\n      resetRecomputations: () => {\n        recomputations = 0;\n      },\n      memoize,\n      argsMemoize\n    });\n  };\n  Object.assign(createSelector2, {\n    withTypes: () => createSelector2\n  });\n  return createSelector2;\n}\nvar createSelector = /* @__PURE__ */ createSelectorCreator(weakMapMemoize);\n\n// src/createStructuredSelector.ts\nvar createStructuredSelector = Object.assign(\n  (inputSelectorsObject, selectorCreator = createSelector) => {\n    assertIsObject(\n      inputSelectorsObject,\n      `createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof inputSelectorsObject}`\n    );\n    const inputSelectorKeys = Object.keys(inputSelectorsObject);\n    const dependencies = inputSelectorKeys.map(\n      (key) => inputSelectorsObject[key]\n    );\n    const structuredSelector = selectorCreator(\n      dependencies,\n      (...inputSelectorResults) => {\n        return inputSelectorResults.reduce((composition, value, index) => {\n          composition[inputSelectorKeys[index]] = value;\n          return composition;\n        }, {});\n      }\n    );\n    return structuredSelector;\n  },\n  { withTypes: () => createStructuredSelector }\n);\n\n//# sourceMappingURL=reselect.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/reselect/dist/reselect.mjs\n");

/***/ })

};
;