"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import apiClient from "@/lib/api-client"
import { ChevronRight, ChevronDown, Database, Table, FileText, Server } from "lucide-react" // Added Server icon
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Define the new structure for columns, tables, databases, and data sources
interface Column {
  id: string
  name: string
  type: string
  description: string
}

interface TableNode {
  id: string
  name: string
  description: string
  columns: Column[]
}

interface DatabaseNode {
  id: string
  name: string
  description: string
  tables: TableNode[]
}

interface DataSourceNode {
  id: string
  name: string
  description: string
  databases: DatabaseNode[]
}

// Mock metadata structure with Data Source level
const mockMetadata: DataSourceNode[] = [
  {
    id: "ds1",
    name: "数据源A",
    description: "第一个数据源，包含销售和客户数据",
    databases: [
      {
        id: "db1",
        name: "销售数据库",
        description: "包含所有销售相关的数据表",
        tables: [
          {
            id: "table1",
            name: "sales_data",
            description: "销售交易明细表",
            columns: [
              { id: "col1", name: "transaction_id", type: "VARCHAR", description: "交易ID" },
              { id: "col2", name: "product_id", type: "VARCHAR", description: "产品ID" },
              { id: "col3", name: "product_name", type: "VARCHAR", description: "产品名称" },
              { id: "col4", name: "sales_amount", type: "DECIMAL", description: "销售金额" },
              { id: "col5", name: "sales_date", type: "DATE", description: "销售日期" },
              { id: "col6", name: "customer_id", type: "VARCHAR", description: "客户ID" },
            ],
          },
          {
            id: "table2",
            name: "products",
            description: "产品信息表",
            columns: [
              { id: "col7", name: "product_id", type: "VARCHAR", description: "产品ID" },
              { id: "col8", name: "product_name", type: "VARCHAR", description: "产品名称" },
              { id: "col9", name: "category", type: "VARCHAR", description: "产品类别" },
              { id: "col10", name: "price", type: "DECIMAL", description: "产品价格" },
            ],
          },
        ],
      },
      {
        id: "db2",
        name: "客户数据库",
        description: "包含所有客户相关的数据表",
        tables: [
          {
            id: "table3",
            name: "customers",
            description: "客户信息表",
            columns: [
              { id: "col11", name: "customer_id", type: "VARCHAR", description: "客户ID" },
              { id: "col12", name: "customer_name", type: "VARCHAR", description: "客户名称" },
              { id: "col13", name: "customer_type", type: "VARCHAR", description: "客户类型" },
              { id: "col14", name: "region", type: "VARCHAR", description: "所在地区" },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "ds2",
    name: "数据源B",
    description: "第二个数据源，包含订单数据",
    databases: [
      {
        id: "db3",
        name: "订单数据库",
        description: "包含订单信息",
        tables: [
          {
            id: "table4",
            name: "orders",
            description: "订单主表",
            columns: [
              { id: "col15", name: "order_id", type: "VARCHAR", description: "订单ID" },
              { id: "col16", name: "order_date", type: "DATE", description: "订单日期" },
            ],
          },
        ],
      },
    ],
  },
]

type ExpandedState = {
  [key: string]: boolean
}

export default function MetadataExplorer() {
  const { user } = useAuth()
  const [expanded, setExpanded] = useState<ExpandedState>({})
  const [metadata, setMetadata] = useState<DataSourceNode[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  // 加载元数据
  useEffect(() => {
    const loadMetadata = async () => {
      if (!user) return

      try {
        setIsLoading(true)
        setError("")
        console.log('开始加载元数据...')
        const response = await apiClient.get('/metadata/datasources')
        console.log('元数据API响应:', response)

        // 检查响应格式
        if (!Array.isArray(response)) {
          console.warn('元数据API返回的不是数组格式:', response)
          setError('元数据格式错误，使用模拟数据')
          setMetadata(mockMetadata)
          return
        }

        // 如果返回空数组，使用模拟数据
        if (response.length === 0) {
          console.warn('元数据API返回空数组，使用模拟数据')
          setError('暂无元数据，使用模拟数据')
          setMetadata(mockMetadata)
          return
        }

        setMetadata(response)
        console.log('元数据加载成功')
      } catch (error: any) {
        console.error('加载元数据失败:', error)

        // 根据错误类型设置不同的错误信息
        if (error.message?.includes('500')) {
          setError('服务器内部错误，使用模拟数据')
        } else if (error.message?.includes('403')) {
          setError('权限不足，使用模拟数据')
        } else if (error.message?.includes('404')) {
          setError('元数据接口不存在，使用模拟数据')
        } else {
          setError('加载元数据失败，使用模拟数据')
        }

        // 如果API调用失败，使用模拟数据作为后备
        setMetadata(mockMetadata)
      } finally {
        setIsLoading(false)
      }
    }

    loadMetadata()
  }, [user])

  const toggleExpand = (id: string) => {
    setExpanded((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  // 加载状态
  if (isLoading) {
    return (
      <div className="p-2">
        <div className="text-sm font-medium text-gray-500 mb-2 px-2">数据仓库元数据</div>
        <div className="space-y-1">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse p-2">
              <div className="h-6 bg-gray-200 rounded mb-2"></div>
              <div className="ml-4 space-y-1">
                <div className="h-4 bg-gray-100 rounded w-3/4"></div>
                <div className="h-4 bg-gray-100 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-2">
      <div className="text-sm font-medium text-gray-500 mb-2 px-2">数据仓库元数据</div>
      {error && (
        <div className="mb-2 mx-2 p-2 bg-yellow-50 border border-yellow-200 text-yellow-700 rounded text-xs">
          {error}
        </div>
      )}
      <div className="space-y-1">
        {(metadata.length > 0 ? metadata : mockMetadata).map((dataSource) => (
          <div key={dataSource.id} className="text-sm">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => toggleExpand(dataSource.id)}
                    className="flex items-center w-full p-2 hover:bg-gray-100 rounded-md text-left"
                  >
                    {expanded[dataSource.id] ? (
                      <ChevronDown className="h-4 w-4 mr-1" />
                    ) : (
                      <ChevronRight className="h-4 w-4 mr-1" />
                    )}
                    <Server className="h-4 w-4 mr-2 text-purple-600" /> {/* Data Source Icon */}
                    <span>{dataSource.name}</span>
                  </button>
                </TooltipTrigger>
                <TooltipContent side="right">{dataSource.description}</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {expanded[dataSource.id] && (
              <div className="ml-6 space-y-1 mt-1">
                {dataSource.databases.map((database) => (
                  <div key={database.id}>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button
                            onClick={() => toggleExpand(database.id)}
                            className="flex items-center w-full p-2 hover:bg-gray-100 rounded-md text-left"
                          >
                            {expanded[database.id] ? (
                              <ChevronDown className="h-4 w-4 mr-1" />
                            ) : (
                              <ChevronRight className="h-4 w-4 mr-1" />
                            )}
                            <Database className="h-4 w-4 mr-2 text-blue-600" /> {/* Database Icon */}
                            <span>{database.name}</span>
                          </button>
                        </TooltipTrigger>
                        <TooltipContent side="right">{database.description}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    {expanded[database.id] && (
                      <div className="ml-6 space-y-1 mt-1">
                        {database.tables.map((table) => (
                          <div key={table.id}>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <button
                                    onClick={() => toggleExpand(table.id)}
                                    className="flex items-center w-full p-2 hover:bg-gray-100 rounded-md text-left"
                                  >
                                    {expanded[table.id] ? (
                                      <ChevronDown className="h-4 w-4 mr-1" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4 mr-1" />
                                    )}
                                    <Table className="h-4 w-4 mr-2 text-green-600" /> {/* Table Icon */}
                                    <span>{table.name}</span>
                                  </button>
                                </TooltipTrigger>
                                <TooltipContent side="right">{table.description}</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>

                            {expanded[table.id] && (
                              <div className="ml-6 space-y-1 mt-1">
                                {table.columns.map((column) => (
                                  <TooltipProvider key={column.id}>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <div className="flex items-center p-2 hover:bg-gray-100 rounded-md">
                                          <FileText className="h-4 w-4 mr-2 text-gray-500" /> {/* Column Icon */}
                                          <span>{column.name}</span>
                                          <span className="ml-2 text-xs text-gray-500">{column.type}</span>
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent side="right">{column.description}</TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
