# NL2SQL 数据库脚本

本目录包含NL2SQL系统所需的数据库脚本。

## 脚本文件

### MySQL DDL脚本 (`mysql_ddl.sql`)

包含创建所有必要表的DDL语句，包括：

- users - 用户表
- data_sources - 数据源表
- database_metadata - 数据库元数据表
- query_history - 查询历史表
- favorites - 收藏表
- llm_providers - LLM提供商表
- system_settings - 系统设置表
- audit_logs - 审计日志表

### 初始化数据脚本 (`init_data.sql`)

包含系统初始化所需的基本数据，包括：

- 系统设置初始化
- 默认LLM提供商配置
- 示例数据源
- 测试用户
- 示例查询和收藏（仅用于演示）

### 数据库配置示例 (`database_config_example.py`)

提供了一个Python配置文件示例，包含：

- MySQL数据库连接配置
- SQLAlchemy数据库URL格式
- 数据库连接池配置
- 完整的SQLAlchemy引擎配置示例

## 使用方法

### 方法一：使用MySQL命令行客户端

```bash
# 登录MySQL
mysql -u your_username -p

# 在MySQL命令行中执行DDL脚本创建表结构
source /path/to/mysql_ddl.sql

# 执行初始化数据脚本（可选）
source /path/to/init_data.sql
```

### 方法二：使用MySQL Workbench

1. 打开MySQL Workbench
2. 连接到您的MySQL服务器
3. 选择 File > Open SQL Script
4. 浏览并选择 `mysql_ddl.sql` 文件
5. 点击执行按钮运行脚本
6. 重复步骤3-5，选择并执行 `init_data.sql` 文件（可选）

### 方法三：使用其他MySQL客户端工具

大多数MySQL客户端工具都支持导入和执行SQL脚本文件。请参考您使用的工具的文档。

### 执行顺序

请按照以下顺序执行脚本：

1. 首先执行 `mysql_ddl.sql` 创建数据库和表结构
2. 然后执行 `init_data.sql` 初始化基本数据（可选）

## 注意事项

- 脚本会创建一个名为 `nl2sql` 的数据库，如果您想使用不同的数据库名，请修改脚本中的相应部分。
- 脚本会创建一个默认的管理员用户，用户名为 `admin`，邮箱为 `<EMAIL>`。请确保在应用程序中更新密码哈希。
- 所有表都使用 UTF-8 (utf8mb4) 字符集和 utf8mb4_unicode_ci 排序规则，以支持完整的Unicode字符集。
- 初始化数据脚本中的密码和API密钥是占位符，实际使用时需要替换为真实值。
- 数据库配置示例文件需要根据实际环境进行修改。

## 数据库配置

在项目中使用这些脚本创建的数据库时，需要进行以下配置：

1. 复制 `database_config_example.py` 文件到项目的配置目录
2. 根据实际情况修改数据库连接参数
3. 在应用程序中引用这些配置

示例：

```python
# 在应用程序的配置文件中
from database_config import DATABASE_URL

# 在数据库初始化文件中
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import DATABASE_URL

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
```