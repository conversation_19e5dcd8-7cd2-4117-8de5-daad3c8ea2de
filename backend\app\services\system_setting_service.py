"""
系统设置服务层
"""

from typing import Dict, List, Optional, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.system_setting import SystemSetting, DEFAULT_SETTINGS
from app.schemas.system_setting import (
    ApplicationSettings, 
    ApplicationSettingsUpdate,
    FRONTEND_TO_BACKEND_MAPPING,
    BACKEND_TO_FRONTEND_MAPPING
)


class SystemSettingService:
    """系统设置服务"""

    def __init__(self, db: Session):
        self.db = db

    def init_default_settings(self) -> bool:
        """初始化默认设置"""
        try:
            # 检查是否已经初始化过
            existing_count = self.db.query(SystemSetting).count()
            if existing_count > 0:
                return True

            # 创建默认设置
            for setting_data in DEFAULT_SETTINGS:
                setting = SystemSetting(**setting_data)
                self.db.add(setting)

            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise e

    def get_setting_by_key(self, key: str) -> Optional[SystemSetting]:
        """根据键名获取设置"""
        return self.db.query(SystemSetting).filter(SystemSetting.key == key).first()

    def get_settings_by_category(self, category: str) -> List[SystemSetting]:
        """根据分类获取设置"""
        return self.db.query(SystemSetting).filter(SystemSetting.category == category).all()

    def get_all_settings(self) -> List[SystemSetting]:
        """获取所有设置"""
        return self.db.query(SystemSetting).all()

    def update_setting(self, key: str, value: Any) -> Optional[SystemSetting]:
        """更新单个设置"""
        setting = self.get_setting_by_key(key)
        if not setting:
            return None

        if not setting.is_editable:
            raise ValueError(f"设置 '{key}' 不可编辑")

        setting.set_typed_value(value)
        self.db.commit()
        self.db.refresh(setting)
        return setting

    def update_settings_batch(self, settings_data: Dict[str, Any]) -> bool:
        """批量更新设置"""
        try:
            for key, value in settings_data.items():
                setting = self.get_setting_by_key(key)
                if setting and setting.is_editable:
                    setting.set_typed_value(value)

            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise e

    def reset_settings_to_default(self) -> bool:
        """重置设置为默认值"""
        try:
            # 删除所有现有设置
            self.db.query(SystemSetting).delete()
            
            # 重新创建默认设置
            for setting_data in DEFAULT_SETTINGS:
                setting = SystemSetting(**setting_data)
                self.db.add(setting)

            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise e

    def get_application_settings(self) -> ApplicationSettings:
        """获取应用程序设置（前端格式）"""
        # 确保默认设置已初始化
        self.init_default_settings()

        # 获取所有设置
        settings = self.get_all_settings()
        settings_dict = {setting.key: setting.get_typed_value() for setting in settings}

        # 转换为前端格式
        frontend_settings = {}
        for frontend_key, backend_key in FRONTEND_TO_BACKEND_MAPPING.items():
            if backend_key in settings_dict:
                frontend_settings[frontend_key] = settings_dict[backend_key]

        # 创建ApplicationSettings对象，使用默认值填充缺失的字段
        return ApplicationSettings(**frontend_settings)

    def update_application_settings(self, settings_update: ApplicationSettingsUpdate) -> ApplicationSettings:
        """更新应用程序设置（前端格式）"""
        # 转换前端字段名为后端键名
        backend_updates = {}
        for frontend_key, value in settings_update.dict(exclude_unset=True).items():
            if frontend_key in FRONTEND_TO_BACKEND_MAPPING:
                backend_key = FRONTEND_TO_BACKEND_MAPPING[frontend_key]
                backend_updates[backend_key] = value

        # 批量更新设置
        self.update_settings_batch(backend_updates)

        # 返回更新后的设置
        return self.get_application_settings()

    def create_setting(self, category: str, key: str, value: Any, value_type: str = "string", 
                      description: str = None, is_editable: bool = True) -> SystemSetting:
        """创建新设置"""
        # 检查设置是否已存在
        existing = self.get_setting_by_key(key)
        if existing:
            raise ValueError(f"设置 '{key}' 已存在")

        setting = SystemSetting(
            category=category,
            key=key,
            value_type=value_type,
            description=description,
            is_system=False,
            is_editable=is_editable
        )
        setting.set_typed_value(value)

        self.db.add(setting)
        self.db.commit()
        self.db.refresh(setting)
        return setting

    def delete_setting(self, key: str) -> bool:
        """删除设置"""
        setting = self.get_setting_by_key(key)
        if not setting:
            return False

        if setting.is_system:
            raise ValueError(f"系统设置 '{key}' 不能删除")

        self.db.delete(setting)
        self.db.commit()
        return True

    def get_settings_by_keys(self, keys: List[str]) -> Dict[str, Any]:
        """根据键名列表获取设置值"""
        settings = self.db.query(SystemSetting).filter(SystemSetting.key.in_(keys)).all()
        return {setting.key: setting.get_typed_value() for setting in settings}

    def export_settings(self) -> Dict[str, Any]:
        """导出所有设置"""
        settings = self.get_all_settings()
        return {
            "settings": [setting.to_dict() for setting in settings],
            "export_time": "now",  # 可以使用实际时间
            "version": "1.0"
        }

    def import_settings(self, settings_data: List[Dict[str, Any]]) -> bool:
        """导入设置"""
        try:
            for setting_data in settings_data:
                key = setting_data.get("key")
                if not key:
                    continue

                existing = self.get_setting_by_key(key)
                if existing:
                    # 更新现有设置
                    if existing.is_editable:
                        existing.value = setting_data.get("value")
                        existing.description = setting_data.get("description", existing.description)
                else:
                    # 创建新设置
                    setting = SystemSetting(
                        category=setting_data.get("category", "custom"),
                        key=key,
                        value=setting_data.get("value"),
                        value_type=setting_data.get("value_type", "string"),
                        description=setting_data.get("description"),
                        is_system=setting_data.get("is_system", False),
                        is_editable=setting_data.get("is_editable", True)
                    )
                    self.db.add(setting)

            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise e
