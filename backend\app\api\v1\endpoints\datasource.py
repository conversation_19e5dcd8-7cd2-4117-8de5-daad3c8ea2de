from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.security import get_current_user, encrypt_password
from app.models.user import User
from app.models.datasource import DataSource
from app.schemas.datasource import DataSourceCreate, DataSourceUpdate, DataSourceResponse
from app.services.database import DatabaseService

router = APIRouter()

@router.get("/", response_model=List[DataSourceResponse])
async def get_data_sources(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取数据源列表"""
    data_sources = db.query(DataSource).all()
    return data_sources

@router.post("/", response_model=DataSourceResponse)
async def create_data_source(
    data_source: DataSourceCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建数据源"""
    # 加密密码
    encrypted_password = encrypt_password(data_source.password)
    
    db_data_source = DataSource(
        **data_source.dict(exclude={"password"}),
        password=encrypted_password
    )
    
    db.add(db_data_source)
    db.commit()
    db.refresh(db_data_source)
    
    return db_data_source

@router.put("/{data_source_id}", response_model=DataSourceResponse)
async def update_data_source(
    data_source_id: int,
    data_source: DataSourceUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新数据源"""
    db_data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
    if not db_data_source:
        raise HTTPException(status_code=404, detail="数据源不存在")
    
    update_data = data_source.dict(exclude_unset=True)
    if "password" in update_data:
        update_data["password"] = encrypt_password(update_data["password"])
    
    for field, value in update_data.items():
        setattr(db_data_source, field, value)
    
    db.commit()
    db.refresh(db_data_source)
    
    return db_data_source

@router.post("/{data_source_id}/test")
async def test_data_source(
    data_source_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """测试数据源连接"""
    db_service = DatabaseService()
    is_connected = await db_service.test_connection(data_source_id)
    
    # 更新连接状态
    db_data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
    if db_data_source:
        db_data_source.status = "connected" if is_connected else "disconnected"
        db_data_source.last_tested = func.now()
        db.commit()
    
    return {"connected": is_connected}

@router.post("/{data_source_id}/sync")
async def sync_metadata(
    data_source_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """同步数据源元数据"""
    db_service = DatabaseService()
    success = await db_service.sync_metadata(data_source_id)
    
    if success:
        # 更新同步时间
        db_data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
        if db_data_source:
            db_data_source.last_sync = func.now()
            db.commit()
    
    return {"success": success}