-- NL2SQL系统MySQL数据库DDL脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS nl2sql_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE nl2sql_system;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    hashed_password VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    status VARCHAR(20) DEFAULT 'active',
    department VARCHAR(100),
    position VARCHAR(100),
    location VARCHAR(200),
    bio TEXT,
    avatar VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据源表
CREATE TABLE IF NOT EXISTS data_sources (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL COMMENT 'mysql, postgresql, etc.',
    host VARCHAR(255) NOT NULL,
    port INT NOT NULL,
    `database` VARCHAR(100) NOT NULL,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL COMMENT '加密存储',
    status VARCHAR(20) DEFAULT 'disconnected',
    description TEXT,
    config JSON COMMENT '额外配置',
    cron_job VARCHAR(100) COMMENT '同步任务',
    last_tested DATETIME,
    last_sync DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据库元数据表
CREATE TABLE IF NOT EXISTS database_metadata (
    id INT AUTO_INCREMENT PRIMARY KEY,
    data_source_id INT NOT NULL,
    database_name VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    column_name VARCHAR(100) NOT NULL,
    column_type VARCHAR(50) NOT NULL,
    column_comment TEXT,
    is_primary_key BOOLEAN DEFAULT FALSE,
    is_nullable BOOLEAN DEFAULT TRUE,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (data_source_id) REFERENCES data_sources(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 查询历史表
CREATE TABLE IF NOT EXISTS query_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    natural_query TEXT NOT NULL,
    generated_sql TEXT NOT NULL,
    execution_status VARCHAR(20) DEFAULT 'pending',
    results JSON,
    execution_time INT COMMENT '毫秒',
    error_message TEXT,
    data_source_id INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (data_source_id) REFERENCES data_sources(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 收藏表
CREATE TABLE IF NOT EXISTS favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    query_id INT NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    tags JSON,
    is_public BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (query_id) REFERENCES query_history(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- LLM提供商表
CREATE TABLE IF NOT EXISTS llm_providers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    provider_type VARCHAR(50) NOT NULL COMMENT 'openai, anthropic, groq',
    api_key VARCHAR(255) NOT NULL COMMENT '加密存储',
    base_url VARCHAR(255),
    models JSON COMMENT '支持的模型列表',
    config JSON COMMENT '额外配置',
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统设置表（新的键值对结构）
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(50) NOT NULL COMMENT '设置分类',
    `key` VARCHAR(100) NOT NULL UNIQUE COMMENT '设置键名',
    value TEXT COMMENT '设置值',
    value_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '值类型: string, integer, boolean, json',
    description VARCHAR(255) COMMENT '设置描述',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统内置设置',
    is_editable BOOLEAN DEFAULT TRUE COMMENT '是否可编辑',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_key (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统内置角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    description TEXT COMMENT '权限描述',
    category VARCHAR(50) NOT NULL COMMENT '权限分类',
    is_system BOOLEAN DEFAULT TRUE COMMENT '是否为系统权限',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_code (code),
    INDEX idx_category (category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据权限表
CREATE TABLE IF NOT EXISTS data_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '数据权限代码',
    name VARCHAR(100) NOT NULL COMMENT '数据权限名称',
    description TEXT COMMENT '数据权限描述',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型: datasource, database, table',
    resource_id VARCHAR(100) NOT NULL COMMENT '资源ID',
    parent_id INT COMMENT '父级权限ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (parent_id) REFERENCES data_permissions(id) ON DELETE CASCADE,
    INDEX idx_code (code),
    INDEX idx_resource (resource_type, resource_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 角色数据权限关联表
CREATE TABLE IF NOT EXISTS role_data_permissions (
    role_id INT NOT NULL,
    data_permission_id INT NOT NULL,
    PRIMARY KEY (role_id, data_permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (data_permission_id) REFERENCES data_permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL COMMENT 'login, query, create_datasource等',
    resource_type VARCHAR(50) COMMENT 'user, datasource, query等',
    resource_id INT,
    details JSON COMMENT '详细信息',
    ip_address VARCHAR(45),
    user_agent TEXT,
    status VARCHAR(20) DEFAULT 'success' COMMENT 'success, failed',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 初始化默认权限
INSERT INTO permissions (code, name, description, category) VALUES
('query.read', '查看查询', '查看查询历史和结果', 'query'),
('query.create', '创建查询', '创建新的查询', 'query'),
('query.edit', '编辑查询', '编辑现有查询', 'query'),
('query.delete', '删除查询', '删除查询记录', 'query'),
('data.view', '查看数据', '查看数据内容', 'data'),
('data.export', '导出数据', '导出查询结果', 'data'),
('user.view', '查看用户', '查看用户列表和信息', 'user'),
('user.create', '创建用户', '创建新用户', 'user'),
('user.edit', '编辑用户', '编辑用户信息', 'user'),
('user.delete', '删除用户', '删除用户账户', 'user'),
('role.view', '查看角色', '查看角色列表和权限', 'role'),
('role.create', '创建角色', '创建新角色', 'role'),
('role.edit', '编辑角色', '编辑角色和权限', 'role'),
('role.delete', '删除角色', '删除角色', 'role'),
('datasource.view', '查看数据源', '查看数据源配置', 'datasource'),
('datasource.create', '创建数据源', '创建新数据源', 'datasource'),
('datasource.edit', '编辑数据源', '编辑数据源配置', 'datasource'),
('datasource.delete', '删除数据源', '删除数据源', 'datasource'),
('system.config', '系统配置', '管理系统设置', 'system'),
('system.audit', '审计日志', '查看审计日志', 'system'),
('system.backup', '系统备份', '执行系统备份', 'system')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 初始化默认角色
INSERT INTO roles (name, description, is_system) VALUES
('超级管理员', '系统超级管理员，拥有所有权限', TRUE),
('管理员', '系统管理员，拥有大部分管理权限', TRUE),
('数据分析师', '数据分析人员，可以查询和分析数据', TRUE),
('普通用户', '基础用户，只能查看数据', TRUE)
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- 初始化系统设置（使用新的键值对结构）
INSERT INTO system_settings (category, `key`, value, value_type, description, is_system, is_editable) VALUES
('general', 'app_name', 'NL2SQL 数据智能分析系统', 'string', '应用程序名称', TRUE, TRUE),
('general', 'app_description', '自然语言转SQL的数据智能分析平台', 'string', '应用程序描述', TRUE, TRUE),
('general', 'default_language', 'zh-CN', 'string', '默认语言', TRUE, TRUE),
('general', 'timezone', 'Asia/Shanghai', 'string', '时区设置', TRUE, TRUE),
('query', 'max_query_timeout', '30', 'integer', '查询超时时间（秒）', TRUE, TRUE),
('query', 'max_result_rows', '1000', 'integer', '最大结果行数', TRUE, TRUE),
('query', 'enable_query_cache', 'true', 'boolean', '启用查询缓存', TRUE, TRUE),
('query', 'cache_expiration', '3600', 'integer', '缓存过期时间（秒）', TRUE, TRUE),
('security', 'enable_audit_log', 'true', 'boolean', '启用审计日志', TRUE, TRUE),
('security', 'session_timeout', '1800', 'integer', '会话超时时间（秒）', TRUE, TRUE),
('security', 'max_login_attempts', '5', 'integer', '最大登录尝试次数', TRUE, TRUE),
('security', 'enable_two_factor', 'false', 'boolean', '启用双因素认证', TRUE, TRUE),
('notifications', 'enable_email_notifications', 'true', 'boolean', '启用邮件通知', TRUE, TRUE),
('notifications', 'enable_system_alerts', 'true', 'boolean', '启用系统警报', TRUE, TRUE),
('notifications', 'notification_email', '<EMAIL>', 'string', '通知邮箱', TRUE, TRUE),
('appearance', 'default_theme', 'system', 'string', '默认主题', TRUE, TRUE),
('appearance', 'enable_dark_mode', 'true', 'boolean', '启用深色模式', TRUE, TRUE),
('appearance', 'compact_mode', 'false', 'boolean', '紧凑模式', TRUE, TRUE),
('appearance', 'show_welcome_message', 'true', 'boolean', '显示欢迎消息', TRUE, TRUE)
ON DUPLICATE KEY UPDATE value = VALUES(value);

-- 创建默认管理员用户 (密码需要在应用程序中进行哈希处理)
INSERT INTO users (username, email, name, hashed_password, role, is_active)
VALUES ('admin', '<EMAIL>', '系统管理员', 'CHANGE_THIS_PASSWORD_HASH', 'admin', TRUE)
ON DUPLICATE KEY UPDATE id = id;