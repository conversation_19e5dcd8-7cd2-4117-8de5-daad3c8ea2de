from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, <PERSON>olean, ForeignKey
from sqlalchemy.sql import func
from app.core.database import Base

class DataSource(Base):
    __tablename__ = "data_sources"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    type = Column(String(20), nullable=False)  # mysql, postgresql, etc.
    host = Column(String(255), nullable=False)
    port = Column(Integer, nullable=False)
    database = Column(String(100), nullable=False)
    username = Column(String(100), nullable=False)
    password = Column(String(255), nullable=False)  # 加密存储
    status = Column(String(20), default="disconnected")
    description = Column(Text)
    config = Column(JSON)  # 额外配置
    cron_job = Column(String(100))  # 同步任务
    last_tested = Column(DateTime)
    last_sync = Column(DateTime)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

class DatabaseMetadata(Base):
    __tablename__ = "database_metadata"
    
    id = Column(Integer, primary_key=True, index=True)
    data_source_id = Column(Integer, ForeignKey("data_sources.id"), nullable=False)
    database_name = Column(String(100), nullable=False)
    table_name = Column(String(100), nullable=False)
    column_name = Column(String(100), nullable=False)
    column_type = Column(String(50), nullable=False)
    column_comment = Column(Text)
    is_primary_key = Column(Boolean, default=False)
    is_nullable = Column(Boolean, default=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())