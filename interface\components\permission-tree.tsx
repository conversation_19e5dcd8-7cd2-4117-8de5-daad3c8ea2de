"use client"

import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { ChevronRight, ChevronDown, Shield, Database, Users, Settings } from "lucide-react"
import { useState } from "react"

interface Permission {
  id: string
  name: string
  description: string
  children?: Permission[]
}

const permissionTree: Permission[] = [
  {
    id: "query",
    name: "查询权限",
    description: "数据查询相关权限",
    children: [
      {
        id: "query.read",
        name: "查看查询",
        description: "查看SQL查询和结果",
      },
      {
        id: "query.create",
        name: "创建查询",
        description: "创建新的SQL查询",
      },
      {
        id: "query.edit",
        name: "编辑查询",
        description: "修改现有查询",
      },
      {
        id: "query.delete",
        name: "删除查询",
        description: "删除查询记录",
      },
    ],
  },
  {
    id: "data",
    name: "数据权限",
    description: "数据访问和管理权限",
    children: [
      {
        id: "data.view",
        name: "查看数据",
        description: "查看数据库表和字段",
      },
      {
        id: "data.export",
        name: "导出数据",
        description: "导出查询结果",
      },
      {
        id: "data.schema",
        name: "查看结构",
        description: "查看数据库结构信息",
      },
    ],
  },
  {
    id: "user",
    name: "用户管理",
    description: "用户和权限管理",
    children: [
      {
        id: "user.view",
        name: "查看用户",
        description: "查看用户列表和信息",
      },
      {
        id: "user.create",
        name: "创建用户",
        description: "创建新用户账户",
      },
      {
        id: "user.edit",
        name: "编辑用户",
        description: "修改用户信息",
      },
      {
        id: "user.delete",
        name: "删除用户",
        description: "删除用户账户",
      },
    ],
  },
  {
    id: "system",
    name: "系统管理",
    description: "系统配置和管理权限",
    children: [
      {
        id: "system.config",
        name: "系统配置",
        description: "修改系统配置",
      },
      {
        id: "system.logs",
        name: "查看日志",
        description: "查看系统日志",
      },
      {
        id: "system.backup",
        name: "备份管理",
        description: "执行系统备份",
      },
    ],
  },
]

interface PermissionTreeProps {
  selectedPermissions: string[]
  onPermissionChange: (permissions: string[]) => void
}

export default function PermissionTree({ selectedPermissions, onPermissionChange }: PermissionTreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<string[]>(["query", "data", "user", "system"])

  const toggleNode = (nodeId: string) => {
    setExpandedNodes((prev) => (prev.includes(nodeId) ? prev.filter((id) => id !== nodeId) : [...prev, nodeId]))
  }

  const isNodeExpanded = (nodeId: string) => expandedNodes.includes(nodeId)

  const handlePermissionToggle = (permissionId: string, hasChildren: boolean) => {
    let newPermissions = [...selectedPermissions]

    if (hasChildren) {
      // Handle parent permission
      const childPermissions = getChildPermissions(permissionId)
      const allChildrenSelected = childPermissions.every((child) => selectedPermissions.includes(child))

      if (allChildrenSelected) {
        // Remove all children
        newPermissions = newPermissions.filter((p) => !childPermissions.includes(p) && p !== permissionId)
      } else {
        // Add all children
        childPermissions.forEach((child) => {
          if (!newPermissions.includes(child)) {
            newPermissions.push(child)
          }
        })
        if (!newPermissions.includes(permissionId)) {
          newPermissions.push(permissionId)
        }
      }
    } else {
      // Handle leaf permission
      if (selectedPermissions.includes(permissionId)) {
        newPermissions = newPermissions.filter((p) => p !== permissionId)
      } else {
        newPermissions.push(permissionId)
      }
    }

    onPermissionChange(newPermissions)
  }

  const getChildPermissions = (parentId: string): string[] => {
    const parent = findPermissionById(parentId, permissionTree)
    if (!parent || !parent.children) return []
    return parent.children.map((child) => child.id)
  }

  const findPermissionById = (id: string, permissions: Permission[]): Permission | null => {
    for (const permission of permissions) {
      if (permission.id === id) return permission
      if (permission.children) {
        const found = findPermissionById(id, permission.children)
        if (found) return found
      }
    }
    return null
  }

  const getPermissionIcon = (permissionId: string) => {
    if (permissionId.startsWith("query")) return <Database className="h-4 w-4" />
    if (permissionId.startsWith("data")) return <Shield className="h-4 w-4" />
    if (permissionId.startsWith("user")) return <Users className="h-4 w-4" />
    if (permissionId.startsWith("system")) return <Settings className="h-4 w-4" />
    return <Shield className="h-4 w-4" />
  }

  const isPermissionSelected = (permissionId: string, hasChildren: boolean) => {
    if (hasChildren) {
      const childPermissions = getChildPermissions(permissionId)
      return childPermissions.length > 0 && childPermissions.every((child) => selectedPermissions.includes(child))
    }
    return selectedPermissions.includes(permissionId)
  }

  const isPermissionIndeterminate = (permissionId: string, hasChildren: boolean) => {
    if (!hasChildren) return false
    const childPermissions = getChildPermissions(permissionId)
    const selectedChildren = childPermissions.filter((child) => selectedPermissions.includes(child))
    return selectedChildren.length > 0 && selectedChildren.length < childPermissions.length
  }

  const renderPermission = (permission: Permission, level = 0) => {
    const hasChildren = permission.children && permission.children.length > 0
    const isExpanded = isNodeExpanded(permission.id)
    const isSelected = isPermissionSelected(permission.id, hasChildren)
    const isIndeterminate = isPermissionIndeterminate(permission.id, hasChildren)

    return (
      <div key={permission.id} className="space-y-1">
        <div className={`flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 ${level > 0 ? "ml-6" : ""}`}>
          {hasChildren && (
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => toggleNode(permission.id)}>
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
          )}

          {!hasChildren && <div className="w-6" />}

          <Checkbox
            id={permission.id}
            checked={isSelected}
            onCheckedChange={() => handlePermissionToggle(permission.id, hasChildren)}
            className={isIndeterminate ? "data-[state=checked]:bg-blue-600" : ""}
          />

          <div className="flex items-center space-x-2 flex-1">
            {getPermissionIcon(permission.id)}
            <div>
              <label htmlFor={permission.id} className="text-sm font-medium cursor-pointer">
                {permission.name}
              </label>
              <p className="text-xs text-gray-500">{permission.description}</p>
            </div>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="space-y-1">{permission.children!.map((child) => renderPermission(child, level + 1))}</div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">操作权限</h3>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => onPermissionChange([])}>
            清除全部
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const allPermissions = permissionTree.flatMap((p) =>
                p.children ? [p.id, ...p.children.map((c) => c.id)] : [p.id],
              )
              onPermissionChange(allPermissions)
            }}
          >
            选择全部
          </Button>
        </div>
      </div>

      <div className="border rounded-lg p-4 max-h-96 overflow-y-auto">
        {permissionTree.map((permission) => renderPermission(permission))}
      </div>
    </div>
  )
}
