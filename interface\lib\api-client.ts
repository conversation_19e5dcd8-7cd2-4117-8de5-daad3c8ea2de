"use client"

class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string = '/api/v1') {
    this.baseURL = baseURL
    // 从localStorage获取token
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('access_token')
      // 如果localStorage中没有token，尝试从cookie获取
      if (!this.token) {
        this.token = this.getTokenFromCookie()
      }
    }
  }

  // 从cookie获取token的辅助方法
  private getTokenFromCookie(): string | null {
    if (typeof document === 'undefined') return null

    const cookies = document.cookie.split(';')
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=')
      if (name === 'access_token') {
        return value
      }
    }
    return null
  }

  // 设置token
  setToken(token: string) {
    this.token = token
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token)
      // 同时设置cookie，供中间件使用
      document.cookie = `access_token=${token}; path=/; max-age=${30 * 60}; SameSite=Lax`
    }
  }

  // 清除token
  clearToken() {
    this.token = null
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token')
      // 同时清除cookie
      document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    }
  }

  // 通用请求方法
  async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`
    
    // 设置默认headers
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    // 如果有token，添加到headers
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    const config = {
      ...options,
      headers,
    }

    try {
      const response = await fetch(url, config)
      
      // 处理401未授权错误
      if (response.status === 401) {
        // 清除token
        this.clearToken()
        // 重定向到登录页
        if (typeof window !== 'undefined') {
          window.location.href = '/login'
        }
        throw new Error('未授权，请重新登录')
      }

      // 检查响应内容类型
      const contentType = response.headers.get('content-type')

      if (!response.ok) {
        // 处理错误响应
        let errorMessage = '请求失败'

        try {
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.detail || errorData.message || '请求失败'
          } else {
            // 如果不是JSON响应，获取文本内容
            const errorText = await response.text()
            errorMessage = errorText || `HTTP ${response.status}: ${response.statusText}`
          }
        } catch (parseError) {
          console.error('解析错误响应失败:', parseError)
          errorMessage = `HTTP ${response.status}: ${response.statusText}`
        }

        throw new Error(errorMessage)
      }

      // 解析成功响应
      try {
        if (contentType && contentType.includes('application/json')) {
          return await response.json()
        } else {
          // 如果不是JSON响应，返回文本
          return await response.text()
        }
      } catch (parseError) {
        console.error('解析响应失败:', parseError)
        throw new Error('服务器响应格式错误')
      }
    } catch (error) {
      console.error('API请求错误:', error)
      throw error
    }
  }

  // GET请求
  async get(endpoint: string, options: RequestInit = {}) {
    return this.request(endpoint, { ...options, method: 'GET' })
  }

  // POST请求
  async post(endpoint: string, data: any, options: RequestInit = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // PUT请求
  async put(endpoint: string, data: any, options: RequestInit = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  // DELETE请求
  async delete(endpoint: string, options: RequestInit = {}) {
    return this.request(endpoint, { ...options, method: 'DELETE' })
  }

  // 登录
  async login(username: string, password: string) {
    try {
      // 清除之前的token
      this.clearToken()

      // 创建表单数据
      const formData = new FormData()
      formData.append('username', username)
      formData.append('password', password)

      const response = await fetch(`${this.baseURL}/auth/login`, {
        method: 'POST',
        body: formData,
      })

      // 检查响应内容类型
      const contentType = response.headers.get('content-type')

      if (!response.ok) {
        // 处理错误响应
        let errorMessage = '登录失败'

        try {
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.detail || errorData.message || '登录失败'
          } else {
            // 如果不是JSON响应，获取文本内容
            const errorText = await response.text()
            errorMessage = errorText || `HTTP ${response.status}: ${response.statusText}`
          }
        } catch (parseError) {
          console.error('解析登录错误响应失败:', parseError)
          errorMessage = `登录失败: HTTP ${response.status}`
        }

        throw new Error(errorMessage)
      }

      // 解析成功响应
      try {
        if (contentType && contentType.includes('application/json')) {
          const data = await response.json()
          this.setToken(data.access_token)
          return data
        } else {
          throw new Error('服务器返回了非JSON格式的响应')
        }
      } catch (parseError) {
        console.error('解析登录响应失败:', parseError)
        throw new Error('服务器响应格式错误')
      }
    } catch (error) {
      console.error('登录请求失败:', error)
      throw error
    }
  }

  // 注册
  async register(userData: {
    username: string
    email: string
    name: string
    password: string
  }) {
    return this.post('/auth/register', userData)
  }

  // 获取当前用户信息
  async getCurrentUser() {
    return this.get('/users/me')
  }

  // 检查token是否有效
  async validateToken() {
    try {
      await this.getCurrentUser()
      return true
    } catch (error) {
      return false
    }
  }

  // 获取token
  getToken() {
    return this.token
  }

  // 检查是否已登录
  isAuthenticated() {
    return !!this.token
  }

  // 获取查询历史
  async getQueryHistory() {
    return this.get('/query/history')
  }

  // 获取收藏的查询
  async getFavoriteQueries() {
    return this.get('/favorites/')
  }

  // 执行自然语言到SQL的转换并执行
  async nlToSqlAndExecute(query: string, datasourceId: number) {
    return this.post('/query/nl2sql-and-execute', {
      query,
      datasource_id: datasourceId,
    })
  }

  // 直接执行SQL查询
  async executeQuery(sql: string, datasourceId: number) {
    return this.post('/query/execute', {
      sql,
      datasource_id: datasourceId,
    })
  }

  // 获取数据源列表
  async getDataSources() {
    return this.get('/datasources/')
  }
}

// 创建一个单例实例
const apiClient = new ApiClient()

export default apiClient