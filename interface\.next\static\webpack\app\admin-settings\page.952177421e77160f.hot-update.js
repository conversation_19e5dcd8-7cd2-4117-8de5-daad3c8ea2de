"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-settings/page",{

/***/ "(app-pages-browser)/./components/data-source-config.tsx":
/*!*******************************************!*\
  !*** ./components/data-source-config.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DataSourceConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube-diagonal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-sync.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Edit,Eye,EyeOff,FolderSync,Plus,TestTube,TestTubeDiagonal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst mockDataSources = [\n    {\n        id: \"1\",\n        name: \"生产数据库\",\n        type: \"mysql\",\n        host: \"prod-db.example.com\",\n        port: 3306,\n        database: \"production\",\n        username: \"app_user\",\n        password: \"encrypted_password\",\n        status: \"connected\",\n        description: \"生产环境主数据库\",\n        cronJob: \"0 0 * * *\",\n        createdAt: \"2024-01-01\",\n        updatedAt: \"2024-01-15\",\n        lastTested: \"2024-01-15 10:30:00\",\n        lastSync: \"2024-01-15 10:30:00\"\n    },\n    {\n        id: \"2\",\n        name: \"测试数据库\",\n        type: \"postgresql\",\n        host: \"test-db.example.com\",\n        port: 5432,\n        database: \"testdb\",\n        username: \"test_user\",\n        password: \"encrypted_password\",\n        status: \"connected\",\n        description: \"测试环境数据库\",\n        cronJob: \"0 0 * * *\",\n        createdAt: \"2024-01-02\",\n        updatedAt: \"2024-01-10\",\n        lastTested: \"2024-01-14 15:20:00\",\n        lastSync: \"2024-01-14 15:20:00\"\n    },\n    {\n        id: \"3\",\n        name: \"数据仓库\",\n        type: \"doris\",\n        host: \"dws.example.com\",\n        port: 9030,\n        database: \"DWPROD\",\n        username: \"dw_user\",\n        password: \"encrypted_password\",\n        status: \"error\",\n        description: \"数据仓库Doris\",\n        cronJob: \"0 0 * * *\",\n        createdAt: \"2024-01-03\",\n        updatedAt: \"2024-01-12\",\n        lastTested: \"2024-01-12 09:45:00\",\n        lastSync: \"2024-01-14 15:20:00\"\n    }\n];\nconst databaseTypes = [\n    {\n        value: \"doris\",\n        label: \"Doris\"\n    },\n    {\n        value: \"mysql\",\n        label: \"MySQL\"\n    },\n    {\n        value: \"postgresql\",\n        label: \"PostgreSQL\"\n    },\n    {\n        value: \"oracle\",\n        label: \"Oracle\"\n    },\n    {\n        value: \"sqlserver\",\n        label: \"SQL Server\"\n    },\n    {\n        value: \"sqlite\",\n        label: \"SQLite\"\n    }\n];\nfunction DataSourceConfig() {\n    _s();\n    const [dataSources, setDataSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDataSource, setSelectedDataSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTesting, setIsTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        type: \"mysql\",\n        host: \"\",\n        port: 3306,\n        database: \"\",\n        username: \"\",\n        password: \"\",\n        description: \"\"\n    });\n    const handleCreateDataSource = ()=>{\n        const newDataSource = {\n            id: Date.now().toString(),\n            ...formData,\n            status: \"disconnected\",\n            createdAt: new Date().toISOString().split(\"T\")[0],\n            updatedAt: new Date().toISOString().split(\"T\")[0]\n        };\n        setDataSources([\n            ...dataSources,\n            newDataSource\n        ]);\n        setIsCreateDialogOpen(false);\n        resetForm();\n    };\n    const handleEditDataSource = ()=>{\n        if (!selectedDataSource) return;\n        const updatedDataSources = dataSources.map((ds)=>ds.id === selectedDataSource.id ? {\n                ...ds,\n                ...formData,\n                updatedAt: new Date().toISOString().split(\"T\")[0]\n            } : ds);\n        setDataSources(updatedDataSources);\n        setIsEditDialogOpen(false);\n        resetForm();\n    };\n    const handleDeleteDataSource = (id)=>{\n        if (confirm(\"确定要删除这个数据源吗？\")) {\n            setDataSources(dataSources.filter((ds)=>ds.id !== id));\n        }\n    };\n    const handleTestConnection = async (dataSource)=>{\n        setIsTesting(true);\n        setTestResult(null);\n        // Simulate connection test\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        const success = Math.random() > 0.3 // 70% success rate for demo\n        ;\n        setTestResult({\n            success,\n            message: success ? \"连接测试成功！数据库连接正常。\" : \"连接测试失败：无法连接到数据库，请检查配置信息。\"\n        });\n        if (dataSource && success) {\n            // Update the data source status\n            const updatedDataSources = dataSources.map((ds)=>ds.id === dataSource.id ? {\n                    ...ds,\n                    status: \"connected\",\n                    lastTested: new Date().toLocaleString()\n                } : ds);\n            setDataSources(updatedDataSources);\n        }\n        setIsTesting(false);\n    };\n    const openEditDialog = (dataSource)=>{\n        setSelectedDataSource(dataSource);\n        setFormData({\n            name: dataSource.name,\n            type: dataSource.type,\n            host: dataSource.host,\n            port: dataSource.port,\n            database: dataSource.database,\n            username: dataSource.username,\n            password: dataSource.password,\n            description: dataSource.description || \"\"\n        });\n        setIsEditDialogOpen(true);\n    };\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            type: \"mysql\",\n            host: \"\",\n            port: 3306,\n            database: \"\",\n            username: \"\",\n            password: \"\",\n            description: \"\"\n        });\n        setSelectedDataSource(null);\n        setTestResult(null);\n        setShowPassword(false);\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"已连接\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n            case \"disconnected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    children: \"未连接\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"destructive\",\n                    children: \"连接错误\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"outline\",\n                    children: \"未知\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 16\n                }, this);\n            case \"disconnected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getDefaultPort = (type)=>{\n        switch(type){\n            case \"doris\":\n                return 9030;\n            case \"mysql\":\n                return 3306;\n            case \"postgresql\":\n                return 5432;\n            case \"oracle\":\n                return 1521;\n            case \"sqlserver\":\n                return 1433;\n            case \"sqlite\":\n                return 0;\n            default:\n                return 3306;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"数据源配置\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"管理数据库连接和配置信息\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: isCreateDialogOpen,\n                        onOpenChange: setIsCreateDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>resetForm(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"新建数据源\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                className: \"max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                                children: \"创建数据源\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                                children: \"配置新的数据库连接信息\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.Tabs, {\n                                        defaultValue: \"basic\",\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsList, {\n                                                className: \"grid w-full grid-cols-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                                        value: \"basic\",\n                                                        children: \"基本信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                                        value: \"connection\",\n                                                        children: \"连接配置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                                                value: \"basic\",\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"name\",\n                                                                children: \"数据源名称\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"name\",\n                                                                value: formData.name,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        name: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入数据源名称\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"type\",\n                                                                children: \"数据库类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                value: formData.type,\n                                                                onValueChange: (value)=>{\n                                                                    setFormData({\n                                                                        ...formData,\n                                                                        type: value,\n                                                                        port: getDefaultPort(value)\n                                                                    });\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                        children: databaseTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: type.value,\n                                                                                children: type.label\n                                                                            }, type.value, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                lineNumber: 325,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"description\",\n                                                                children: \"描述\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                                id: \"description\",\n                                                                value: formData.description,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        description: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入数据源描述（可选）\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                                                value: \"connection\",\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"host\",\n                                                                        children: \"主机地址\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"host\",\n                                                                        value: formData.host,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                host: e.target.value\n                                                                            }),\n                                                                        placeholder: \"localhost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"port\",\n                                                                        children: \"端口\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"port\",\n                                                                        type: \"number\",\n                                                                        value: formData.port,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                port: Number.parseInt(e.target.value)\n                                                                            })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"database\",\n                                                                children: \"数据库名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"database\",\n                                                                value: formData.database,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        database: e.target.value\n                                                                    }),\n                                                                placeholder: \"输入数据库名称\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"username\",\n                                                                        children: \"用户名\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        id: \"username\",\n                                                                        value: formData.username,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                username: e.target.value\n                                                                            }),\n                                                                        placeholder: \"输入用户名\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                        htmlFor: \"password\",\n                                                                        children: \"密码\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"password\",\n                                                                                type: showPassword ? \"text\" : \"password\",\n                                                                                value: formData.password,\n                                                                                onChange: (e)=>setFormData({\n                                                                                        ...formData,\n                                                                                        password: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"输入密码\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                lineNumber: 389,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                                onClick: ()=>setShowPassword(!showPassword),\n                                                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                    lineNumber: 403,\n                                                                                    columnNumber: 41\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                    lineNumber: 403,\n                                                                                    columnNumber: 74\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                                lineNumber: 396,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 388,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg border text-sm \".concat(testResult.success ? \"bg-green-50 border-green-200 text-green-800\" : \"bg-red-50 border-red-200 text-red-800\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                testResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 41\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 79\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: testResult.message\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setIsCreateDialogOpen(false),\n                                                children: \"取消\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                // type=\"button\"\n                                                // variant=\"outline\"\n                                                onClick: ()=>handleTestConnection(),\n                                                disabled: isTesting || !formData.name.trim() || !formData.host.trim() || !formData.database.trim() || !formData.username.trim() || !formData.password.trim(),\n                                                children: isTesting ? \"测试中...\" : \"测试连接\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: handleCreateDataSource,\n                                                disabled: !formData.name.trim() || !formData.host.trim() || !formData.database.trim() || !formData.username.trim() || !formData.password.trim(),\n                                                children: \"创建数据源\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: \"数据源列表\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                children: \"系统中的所有数据源连接\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"数据源\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"类型\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"同步作业Cron\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"最近同步时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                    children: dataSources.map((dataSource)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: dataSource.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: dataSource.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-900\",\n                                                        children: dataSource.type\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            getStatusIcon(dataSource.status),\n                                                            getStatusBadge(dataSource.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: dataSource.cronJob\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: dataSource.lastSync ? new Date(dataSource.lastSync).toLocaleString() : \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-test\",\n                                                                \"data-tooltip-content\": \"测试连接\",\n                                                                onClick: ()=>handleTestConnection(dataSource),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                                        id: \"tooltip-test\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 504,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-sync\",\n                                                                \"data-tooltip-content\": \"元数据同步\",\n                                                                onClick: ()=>handleTestConnection(dataSource),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 507,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                                        id: \"tooltip-sync\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-edit\",\n                                                                \"data-tooltip-content\": \"编辑\",\n                                                                onClick: ()=>openEditDialog(dataSource),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                                        id: \"tooltip-edit\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                \"data-tooltip-id\": \"tooltip-trash\",\n                                                                \"data-tooltip-content\": \"删除\",\n                                                                onClick: ()=>handleDeleteDataSource(dataSource.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                                        id: \"tooltip-trash\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, dataSource.id, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                open: isEditDialogOpen,\n                onOpenChange: setIsEditDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                    children: \"编辑数据源\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                    children: \"修改数据库连接信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.Tabs, {\n                            defaultValue: \"basic\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                            value: \"basic\",\n                                            children: \"基本信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                            value: \"connection\",\n                                            children: \"连接配置\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                                    value: \"basic\",\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"数据源名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"name\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"输入数据源名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"type\",\n                                                    children: \"数据库类型\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    value: formData.type,\n                                                    onValueChange: (value)=>{\n                                                        setFormData({\n                                                            ...formData,\n                                                            type: value,\n                                                            port: getDefaultPort(value)\n                                                        });\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                            children: databaseTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: type.value,\n                                                                    children: type.label\n                                                                }, type.value, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            description: e.target.value\n                                                        }),\n                                                    placeholder: \"输入数据源描述（可选）\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                                    value: \"connection\",\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"host\",\n                                                            children: \"主机地址\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"host\",\n                                                            value: formData.host,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    host: e.target.value\n                                                                }),\n                                                            placeholder: \"localhost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"port\",\n                                                            children: \"端口\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"port\",\n                                                            type: \"number\",\n                                                            value: formData.port,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    port: Number.parseInt(e.target.value)\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"database\",\n                                                    children: \"数据库名\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"database\",\n                                                    value: formData.database,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            database: e.target.value\n                                                        }),\n                                                    placeholder: \"输入数据库名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"username\",\n                                                            children: \"用户名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"username\",\n                                                            value: formData.username,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    username: e.target.value\n                                                                }),\n                                                            placeholder: \"输入用户名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"password\",\n                                                            children: \"密码\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"password\",\n                                                                    type: showPassword ? \"text\" : \"password\",\n                                                                    value: formData.password,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            password: e.target.value\n                                                                        }),\n                                                                    placeholder: \"输入密码\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                    lineNumber: 632,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 39\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 72\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, this),\n                        testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg border text-sm \".concat(testResult.success ? \"bg-green-50 border-green-200 text-green-800\" : \"bg-red-50 border-red-200 text-red-800\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    testResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 39\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 77\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: testResult.message\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 655,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>handleTestConnection(),\n                                    disabled: isTesting || !formData.host || !formData.database,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Edit_Eye_EyeOff_FolderSync_Plus_TestTube_TestTubeDiagonal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this),\n                                        isTesting ? \"测试中...\" : \"测试连接\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsEditDialogOpen(false),\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleEditDataSource,\n                                    disabled: !formData.name.trim() || !formData.host.trim() || !formData.database.trim() || !formData.username.trim() || !formData.password.trim(),\n                                    children: \"保存\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                    lineNumber: 528,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n                lineNumber: 527,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\workspace\\\\dstp\\\\project\\\\nl2sql\\\\interface\\\\components\\\\data-source-config.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, this);\n}\n_s(DataSourceConfig, \"Z3uuXLGzxTDJIOQCkfiCnr9MbyY=\");\n_c = DataSourceConfig;\nvar _c;\n$RefreshReg$(_c, \"DataSourceConfig\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZGF0YS1zb3VyY2UtY29uZmlnLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBR0k7QUFDRjtBQUNBO0FBQ21EO0FBQ25EO0FBUWQ7QUFDc0U7QUFDQztBQUNuRDtBQUM0QjtBQUMwRDtBQUNsRztBQW9CdkMsTUFBTTRDLGtCQUFnQztJQUNwQztRQUNFQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRWQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VkLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7Q0FDRDtBQUVELE1BQU1DLGdCQUFnQjtJQUNwQjtRQUFFQyxPQUFPO1FBQVNDLE9BQU87SUFBUTtJQUNqQztRQUFFRCxPQUFPO1FBQVNDLE9BQU87SUFBUTtJQUNqQztRQUFFRCxPQUFPO1FBQWNDLE9BQU87SUFBYTtJQUMzQztRQUFFRCxPQUFPO1FBQVVDLE9BQU87SUFBUztJQUNuQztRQUFFRCxPQUFPO1FBQWFDLE9BQU87SUFBYTtJQUMxQztRQUFFRCxPQUFPO1FBQVVDLE9BQU87SUFBUztDQUNwQztBQUVjLFNBQVNDOztJQUN0QixNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR2pFLCtDQUFRQSxDQUFlLEVBQUU7SUFDL0QsTUFBTSxDQUFDa0UsV0FBV0MsYUFBYSxHQUFHbkUsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDb0Usb0JBQW9CQyxzQkFBc0IsR0FBR3JFLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ3NFLGtCQUFrQkMsb0JBQW9CLEdBQUd2RSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUN3RSxvQkFBb0JDLHNCQUFzQixHQUFHekUsK0NBQVFBLENBQW9CO0lBQ2hGLE1BQU0sQ0FBQzBFLGNBQWNDLGdCQUFnQixHQUFHM0UsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDNEUsWUFBWUMsY0FBYyxHQUFHN0UsK0NBQVFBLENBQStDO0lBQzNGLE1BQU0sQ0FBQzhFLFdBQVdDLGFBQWEsR0FBRy9FLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2dGLFVBQVVDLFlBQVksR0FBR2pGLCtDQUFRQSxDQUFDO1FBQ3ZDOEMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWRSxhQUFhO0lBQ2Y7SUFFQSxNQUFNNEIseUJBQXlCO1FBQzdCLE1BQU1DLGdCQUE0QjtZQUNoQ3RDLElBQUl1QyxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7WUFDdkIsR0FBR04sUUFBUTtZQUNYM0IsUUFBUTtZQUNSRyxXQUFXLElBQUk0QixPQUFPRyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUNqRC9CLFdBQVcsSUFBSTJCLE9BQU9HLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQ25EO1FBRUF2QixlQUFlO2VBQUlEO1lBQWFtQjtTQUFjO1FBQzlDZCxzQkFBc0I7UUFDdEJvQjtJQUNGO0lBRUEsTUFBTUMsdUJBQXVCO1FBQzNCLElBQUksQ0FBQ2xCLG9CQUFvQjtRQUV6QixNQUFNbUIscUJBQXFCM0IsWUFBWTRCLEdBQUcsQ0FBQyxDQUFDQyxLQUMxQ0EsR0FBR2hELEVBQUUsS0FBSzJCLG1CQUFtQjNCLEVBQUUsR0FDM0I7Z0JBQ0UsR0FBR2dELEVBQUU7Z0JBQ0wsR0FBR2IsUUFBUTtnQkFDWHZCLFdBQVcsSUFBSTJCLE9BQU9HLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQ25ELElBQ0FLO1FBR041QixlQUFlMEI7UUFDZnBCLG9CQUFvQjtRQUNwQmtCO0lBQ0Y7SUFFQSxNQUFNSyx5QkFBeUIsQ0FBQ2pEO1FBQzlCLElBQUlrRCxRQUFRLGlCQUFpQjtZQUMzQjlCLGVBQWVELFlBQVlnQyxNQUFNLENBQUMsQ0FBQ0gsS0FBT0EsR0FBR2hELEVBQUUsS0FBS0E7UUFDdEQ7SUFDRjtJQUVBLE1BQU1vRCx1QkFBdUIsT0FBT0M7UUFDbENuQixhQUFhO1FBQ2JGLGNBQWM7UUFFZCwyQkFBMkI7UUFDM0IsTUFBTSxJQUFJc0IsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO1FBRW5ELE1BQU1FLFVBQVVDLEtBQUtDLE1BQU0sS0FBSyxJQUFJLDRCQUE0Qjs7UUFDaEUzQixjQUFjO1lBQ1p5QjtZQUNBRyxTQUFTSCxVQUFVLG9CQUFvQjtRQUN6QztRQUVBLElBQUlKLGNBQWNJLFNBQVM7WUFDekIsZ0NBQWdDO1lBQ2hDLE1BQU1YLHFCQUFxQjNCLFlBQVk0QixHQUFHLENBQUMsQ0FBQ0MsS0FDMUNBLEdBQUdoRCxFQUFFLEtBQUtxRCxXQUFXckQsRUFBRSxHQUNuQjtvQkFDRSxHQUFHZ0QsRUFBRTtvQkFDTHhDLFFBQVE7b0JBQ1JLLFlBQVksSUFBSTBCLE9BQU9zQixjQUFjO2dCQUN2QyxJQUNBYjtZQUVONUIsZUFBZTBCO1FBQ2pCO1FBRUFaLGFBQWE7SUFDZjtJQUVBLE1BQU00QixpQkFBaUIsQ0FBQ1Q7UUFDdEJ6QixzQkFBc0J5QjtRQUN0QmpCLFlBQVk7WUFDVm5DLE1BQU1vRCxXQUFXcEQsSUFBSTtZQUNyQkMsTUFBTW1ELFdBQVduRCxJQUFJO1lBQ3JCQyxNQUFNa0QsV0FBV2xELElBQUk7WUFDckJDLE1BQU1pRCxXQUFXakQsSUFBSTtZQUNyQkMsVUFBVWdELFdBQVdoRCxRQUFRO1lBQzdCQyxVQUFVK0MsV0FBVy9DLFFBQVE7WUFDN0JDLFVBQVU4QyxXQUFXOUMsUUFBUTtZQUM3QkUsYUFBYTRDLFdBQVc1QyxXQUFXLElBQUk7UUFDekM7UUFDQWlCLG9CQUFvQjtJQUN0QjtJQUVBLE1BQU1rQixZQUFZO1FBQ2hCUixZQUFZO1lBQ1ZuQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsVUFBVTtZQUNWQyxVQUFVO1lBQ1ZFLGFBQWE7UUFDZjtRQUNBbUIsc0JBQXNCO1FBQ3RCSSxjQUFjO1FBQ2RGLGdCQUFnQjtJQUNsQjtJQUVBLE1BQU1pQyxpQkFBaUIsQ0FBQ3ZEO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQzVDLHVEQUFLQTtvQkFBQ29HLFdBQVU7OEJBQThCOzs7Ozs7WUFDeEQsS0FBSztnQkFDSCxxQkFBTyw4REFBQ3BHLHVEQUFLQTtvQkFBQ3FHLFNBQVE7OEJBQVk7Ozs7OztZQUNwQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDckcsdURBQUtBO29CQUFDcUcsU0FBUTs4QkFBYzs7Ozs7O1lBQ3RDO2dCQUNFLHFCQUFPLDhEQUFDckcsdURBQUtBO29CQUFDcUcsU0FBUTs4QkFBVTs7Ozs7O1FBQ3BDO0lBQ0Y7SUFFQSxNQUFNQyxnQkFBZ0IsQ0FBQzFEO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQ2YsNktBQVdBO29CQUFDdUUsV0FBVTs7Ozs7O1lBQ2hDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNyRSw2S0FBV0E7b0JBQUNxRSxXQUFVOzs7Ozs7WUFDaEMsS0FBSztnQkFDSCxxQkFBTyw4REFBQ3RFLDZLQUFPQTtvQkFBQ3NFLFdBQVU7Ozs7OztZQUM1QjtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1HLGlCQUFpQixDQUFDakU7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDa0U7UUFBSUosV0FBVTs7MEJBQ2IsOERBQUNJO2dCQUFJSixXQUFVOztrQ0FDYiw4REFBQ0k7OzBDQUNDLDhEQUFDQztnQ0FBR0wsV0FBVTswQ0FBd0I7Ozs7OzswQ0FDdEMsOERBQUNNO2dDQUFFTixXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7O2tDQUUvQiw4REFBQ25HLHlEQUFNQTt3QkFBQzBHLE1BQU1oRDt3QkFBb0JpRCxjQUFjaEQ7OzBDQUM5Qyw4REFBQ3RELGdFQUFhQTtnQ0FBQ3VHLE9BQU87MENBQ3BCLDRFQUFDckgseURBQU1BO29DQUFDc0gsU0FBUyxJQUFNOUI7O3NEQUNyQiw4REFBQ3hELDZLQUFJQTs0Q0FBQzRFLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7OzswQ0FJckMsOERBQUNsRyxnRUFBYUE7Z0NBQUNrRyxXQUFVOztrREFDdkIsOERBQUNoRywrREFBWUE7OzBEQUNYLDhEQUFDQyw4REFBV0E7MERBQUM7Ozs7OzswREFDYiw4REFBQ0Ysb0VBQWlCQTswREFBQzs7Ozs7Ozs7Ozs7O2tEQUdyQiw4REFBQ2dCLHNEQUFJQTt3Q0FBQzRGLGNBQWE7d0NBQVFYLFdBQVU7OzBEQUNuQyw4REFBQy9FLDBEQUFRQTtnREFBQytFLFdBQVU7O2tFQUNsQiw4REFBQzlFLDZEQUFXQTt3REFBQzhCLE9BQU07a0VBQVE7Ozs7OztrRUFDM0IsOERBQUM5Qiw2REFBV0E7d0RBQUM4QixPQUFNO2tFQUFhOzs7Ozs7Ozs7Ozs7MERBR2xDLDhEQUFDaEMsNkRBQVdBO2dEQUFDZ0MsT0FBTTtnREFBUWdELFdBQVU7O2tFQUNuQyw4REFBQ0k7d0RBQUlKLFdBQVU7OzBFQUNiLDhEQUFDMUcsdURBQUtBO2dFQUFDc0gsU0FBUTswRUFBTzs7Ozs7OzBFQUN0Qiw4REFBQ3ZILHVEQUFLQTtnRUFDSjJDLElBQUc7Z0VBQ0hnQixPQUFPbUIsU0FBU2xDLElBQUk7Z0VBQ3BCNEUsVUFBVSxDQUFDQyxJQUFNMUMsWUFBWTt3RUFBRSxHQUFHRCxRQUFRO3dFQUFFbEMsTUFBTTZFLEVBQUVDLE1BQU0sQ0FBQy9ELEtBQUs7b0VBQUM7Z0VBQ2pFZ0UsYUFBWTs7Ozs7Ozs7Ozs7O2tFQUloQiw4REFBQ1o7d0RBQUlKLFdBQVU7OzBFQUNiLDhEQUFDMUcsdURBQUtBO2dFQUFDc0gsU0FBUTswRUFBTzs7Ozs7OzBFQUN0Qiw4REFBQ25HLHlEQUFNQTtnRUFDTHVDLE9BQU9tQixTQUFTakMsSUFBSTtnRUFDcEIrRSxlQUFlLENBQUNqRTtvRUFDZG9CLFlBQVk7d0VBQ1YsR0FBR0QsUUFBUTt3RUFDWGpDLE1BQU1jO3dFQUNOWixNQUFNK0QsZUFBZW5EO29FQUN2QjtnRUFDRjs7a0ZBRUEsOERBQUNwQyxnRUFBYUE7a0ZBQ1osNEVBQUNDLDhEQUFXQTs7Ozs7Ozs7OztrRkFFZCw4REFBQ0gsZ0VBQWFBO2tGQUNYcUMsY0FBY2dDLEdBQUcsQ0FBQyxDQUFDN0MscUJBQ2xCLDhEQUFDdkIsNkRBQVVBO2dGQUFrQnFDLE9BQU9kLEtBQUtjLEtBQUs7MEZBQzNDZCxLQUFLZSxLQUFLOytFQURJZixLQUFLYyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQVFuQyw4REFBQ29EO3dEQUFJSixXQUFVOzswRUFDYiw4REFBQzFHLHVEQUFLQTtnRUFBQ3NILFNBQVE7MEVBQWM7Ozs7OzswRUFDN0IsOERBQUM5Riw4REFBUUE7Z0VBQ1BrQixJQUFHO2dFQUNIZ0IsT0FBT21CLFNBQVMxQixXQUFXO2dFQUMzQm9FLFVBQVUsQ0FBQ0MsSUFBTTFDLFlBQVk7d0VBQUUsR0FBR0QsUUFBUTt3RUFBRTFCLGFBQWFxRSxFQUFFQyxNQUFNLENBQUMvRCxLQUFLO29FQUFDO2dFQUN4RWdFLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLbEIsOERBQUNoRyw2REFBV0E7Z0RBQUNnQyxPQUFNO2dEQUFhZ0QsV0FBVTs7a0VBQ3hDLDhEQUFDSTt3REFBSUosV0FBVTs7MEVBQ2IsOERBQUNJO2dFQUFJSixXQUFVOztrRkFDYiw4REFBQzFHLHVEQUFLQTt3RUFBQ3NILFNBQVE7a0ZBQU87Ozs7OztrRkFDdEIsOERBQUN2SCx1REFBS0E7d0VBQ0oyQyxJQUFHO3dFQUNIZ0IsT0FBT21CLFNBQVNoQyxJQUFJO3dFQUNwQjBFLFVBQVUsQ0FBQ0MsSUFBTTFDLFlBQVk7Z0ZBQUUsR0FBR0QsUUFBUTtnRkFBRWhDLE1BQU0yRSxFQUFFQyxNQUFNLENBQUMvRCxLQUFLOzRFQUFDO3dFQUNqRWdFLGFBQVk7Ozs7Ozs7Ozs7OzswRUFHaEIsOERBQUNaO2dFQUFJSixXQUFVOztrRkFDYiw4REFBQzFHLHVEQUFLQTt3RUFBQ3NILFNBQVE7a0ZBQU87Ozs7OztrRkFDdEIsOERBQUN2SCx1REFBS0E7d0VBQ0oyQyxJQUFHO3dFQUNIRSxNQUFLO3dFQUNMYyxPQUFPbUIsU0FBUy9CLElBQUk7d0VBQ3BCeUUsVUFBVSxDQUFDQyxJQUFNMUMsWUFBWTtnRkFBRSxHQUFHRCxRQUFRO2dGQUFFL0IsTUFBTThFLE9BQU9DLFFBQVEsQ0FBQ0wsRUFBRUMsTUFBTSxDQUFDL0QsS0FBSzs0RUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUt4Riw4REFBQ29EO3dEQUFJSixXQUFVOzswRUFDYiw4REFBQzFHLHVEQUFLQTtnRUFBQ3NILFNBQVE7MEVBQVc7Ozs7OzswRUFDMUIsOERBQUN2SCx1REFBS0E7Z0VBQ0oyQyxJQUFHO2dFQUNIZ0IsT0FBT21CLFNBQVM5QixRQUFRO2dFQUN4QndFLFVBQVUsQ0FBQ0MsSUFBTTFDLFlBQVk7d0VBQUUsR0FBR0QsUUFBUTt3RUFBRTlCLFVBQVV5RSxFQUFFQyxNQUFNLENBQUMvRCxLQUFLO29FQUFDO2dFQUNyRWdFLGFBQVk7Ozs7Ozs7Ozs7OztrRUFJaEIsOERBQUNaO3dEQUFJSixXQUFVOzswRUFDYiw4REFBQ0k7Z0VBQUlKLFdBQVU7O2tGQUNiLDhEQUFDMUcsdURBQUtBO3dFQUFDc0gsU0FBUTtrRkFBVzs7Ozs7O2tGQUMxQiw4REFBQ3ZILHVEQUFLQTt3RUFDSjJDLElBQUc7d0VBQ0hnQixPQUFPbUIsU0FBUzdCLFFBQVE7d0VBQ3hCdUUsVUFBVSxDQUFDQyxJQUFNMUMsWUFBWTtnRkFBRSxHQUFHRCxRQUFRO2dGQUFFN0IsVUFBVXdFLEVBQUVDLE1BQU0sQ0FBQy9ELEtBQUs7NEVBQUM7d0VBQ3JFZ0UsYUFBWTs7Ozs7Ozs7Ozs7OzBFQUdoQiw4REFBQ1o7Z0VBQUlKLFdBQVU7O2tGQUNiLDhEQUFDMUcsdURBQUtBO3dFQUFDc0gsU0FBUTtrRkFBVzs7Ozs7O2tGQUMxQiw4REFBQ1I7d0VBQUlKLFdBQVU7OzBGQUNiLDhEQUFDM0csdURBQUtBO2dGQUNKMkMsSUFBRztnRkFDSEUsTUFBTTJCLGVBQWUsU0FBUztnRkFDOUJiLE9BQU9tQixTQUFTNUIsUUFBUTtnRkFDeEJzRSxVQUFVLENBQUNDLElBQU0xQyxZQUFZO3dGQUFFLEdBQUdELFFBQVE7d0ZBQUU1QixVQUFVdUUsRUFBRUMsTUFBTSxDQUFDL0QsS0FBSztvRkFBQztnRkFDckVnRSxhQUFZOzs7Ozs7MEZBRWQsOERBQUM1SCx5REFBTUE7Z0ZBQ0w4QyxNQUFLO2dGQUNMK0QsU0FBUTtnRkFDUm1CLE1BQUs7Z0ZBQ0xwQixXQUFVO2dGQUNWVSxTQUFTLElBQU01QyxnQkFBZ0IsQ0FBQ0Q7MEZBRS9CQSw2QkFBZSw4REFBQ2hDLDZLQUFNQTtvRkFBQ21FLFdBQVU7Ozs7O3lHQUFlLDhEQUFDcEUsNktBQUdBO29GQUFDb0UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBUTNFakMsNEJBQ0MsOERBQUNxQzt3Q0FDQ0osV0FBVyxpQ0FBOEksT0FBN0dqQyxXQUFXMEIsT0FBTyxHQUFHLGdEQUFnRDtrREFFakgsNEVBQUNXOzRDQUFJSixXQUFVOztnREFDWmpDLFdBQVcwQixPQUFPLGlCQUFHLDhEQUFDaEUsNktBQVdBO29EQUFDdUUsV0FBVTs7Ozs7eUVBQWUsOERBQUNyRSw2S0FBV0E7b0RBQUNxRSxXQUFVOzs7Ozs7OERBQ25GLDhEQUFDcUI7OERBQU10RCxXQUFXNkIsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSy9CLDhEQUFDUTt3Q0FBSUosV0FBVTs7MERBQ2IsOERBQUM1Ryx5REFBTUE7Z0RBQUM2RyxTQUFRO2dEQUFVUyxTQUFTLElBQU1sRCxzQkFBc0I7MERBQVE7Ozs7OzswREFHdkUsOERBQUNwRSx5REFBTUE7Z0RBQ0wsZ0JBQWdCO2dEQUNoQixvQkFBb0I7Z0RBQ3BCc0gsU0FBUyxJQUFNdEI7Z0RBQ2ZrQyxVQUNFckQsYUFDQSxDQUFDRSxTQUFTbEMsSUFBSSxDQUFDc0YsSUFBSSxNQUNuQixDQUFDcEQsU0FBU2hDLElBQUksQ0FBQ29GLElBQUksTUFDbkIsQ0FBQ3BELFNBQVM5QixRQUFRLENBQUNrRixJQUFJLE1BQ3ZCLENBQUNwRCxTQUFTN0IsUUFBUSxDQUFDaUYsSUFBSSxNQUN2QixDQUFDcEQsU0FBUzVCLFFBQVEsQ0FBQ2dGLElBQUk7MERBR3hCdEQsWUFBWSxXQUFXOzs7Ozs7MERBRTFCLDhEQUFDN0UseURBQU1BO2dEQUNMc0gsU0FBU3JDO2dEQUNUaUQsVUFDRSxDQUFDbkQsU0FBU2xDLElBQUksQ0FBQ3NGLElBQUksTUFDbkIsQ0FBQ3BELFNBQVNoQyxJQUFJLENBQUNvRixJQUFJLE1BQ25CLENBQUNwRCxTQUFTOUIsUUFBUSxDQUFDa0YsSUFBSSxNQUN2QixDQUFDcEQsU0FBUzdCLFFBQVEsQ0FBQ2lGLElBQUksTUFDdkIsQ0FBQ3BELFNBQVM1QixRQUFRLENBQUNnRixJQUFJOzBEQUUxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFULDhEQUFDaEkscURBQUlBOztrQ0FDSCw4REFBQ0csMkRBQVVBOzswQ0FDVCw4REFBQ0MsMERBQVNBO2dDQUFDcUcsV0FBVTswQ0FBMEI7Ozs7OzswQ0FHL0MsOERBQUN2RyxnRUFBZUE7MENBQUM7Ozs7Ozs7Ozs7OztrQ0FFbkIsOERBQUNELDREQUFXQTtrQ0FDViw0RUFBQ1csdURBQUtBOzs4Q0FDSiw4REFBQ0ksNkRBQVdBOzhDQUNWLDRFQUFDQywwREFBUUE7OzBEQUNQLDhEQUFDRiwyREFBU0E7MERBQUM7Ozs7OzswREFDWCw4REFBQ0EsMkRBQVNBOzBEQUFDOzs7Ozs7MERBQ1gsOERBQUNBLDJEQUFTQTswREFBQzs7Ozs7OzBEQUNYLDhEQUFDQSwyREFBU0E7MERBQUM7Ozs7OzswREFDWCw4REFBQ0EsMkRBQVNBOzBEQUFDOzs7Ozs7MERBQ1gsOERBQUNBLDJEQUFTQTswREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR2YsOERBQUNGLDJEQUFTQTs4Q0FDUCtDLFlBQVk0QixHQUFHLENBQUMsQ0FBQ00sMkJBQ2hCLDhEQUFDN0UsMERBQVFBOzs4REFDUCw4REFBQ0gsMkRBQVNBOztzRUFDTiw4REFBQytGOzREQUFJSixXQUFVO3NFQUFxQ1gsV0FBV3BELElBQUk7Ozs7OztzRUFDbkUsOERBQUNtRTs0REFBSUosV0FBVTtzRUFBeUJYLFdBQVc1QyxXQUFXOzs7Ozs7Ozs7Ozs7OERBR2xFLDhEQUFDcEMsMkRBQVNBOzhEQUNSLDRFQUFDK0Y7d0RBQUlKLFdBQVU7a0VBQXlCWCxXQUFXbkQsSUFBSTs7Ozs7Ozs7Ozs7OERBRXpELDhEQUFDN0IsMkRBQVNBOzhEQUNSLDRFQUFDK0Y7d0RBQUlKLFdBQVU7OzREQUNaRSxjQUFjYixXQUFXN0MsTUFBTTs0REFDL0J1RCxlQUFlVixXQUFXN0MsTUFBTTs7Ozs7Ozs7Ozs7OzhEQUdyQyw4REFBQ25DLDJEQUFTQTs4REFDUiw0RUFBQ2dIO3dEQUFLckIsV0FBVTtrRUFBeUJYLFdBQVczQyxPQUFPOzs7Ozs7Ozs7Ozs4REFFN0QsOERBQUNyQywyREFBU0E7OERBQ1IsNEVBQUNnSDt3REFBS3JCLFdBQVU7a0VBQXlCWCxXQUFXdkMsUUFBUSxHQUFHLElBQUl5QixLQUFLYyxXQUFXdkMsUUFBUSxFQUFFK0MsY0FBYyxLQUFLOzs7Ozs7Ozs7Ozs4REFFbEgsOERBQUN4RiwyREFBU0E7OERBQ1IsNEVBQUMrRjt3REFBSUosV0FBVTs7MEVBQ2IsOERBQUM1Ryx5REFBTUE7Z0VBQUM2RyxTQUFRO2dFQUFVbUIsTUFBSztnRUFBS0ksbUJBQWdCO2dFQUFlQyx3QkFBcUI7Z0VBQU9mLFNBQVMsSUFBTXRCLHFCQUFxQkM7O2tGQUMvSCw4REFBQzdELDZLQUFnQkE7d0VBQUN3RSxXQUFVOzs7Ozs7a0ZBQzVCLDhEQUFDbEUsbURBQU9BO3dFQUFDRSxJQUFHOzs7Ozs7Ozs7Ozs7MEVBRWhCLDhEQUFDNUMseURBQU1BO2dFQUFDNkcsU0FBUTtnRUFBVW1CLE1BQUs7Z0VBQUtJLG1CQUFnQjtnRUFBZUMsd0JBQXFCO2dFQUFRZixTQUFTLElBQU10QixxQkFBcUJDOztrRkFDaEksOERBQUNsRSw2S0FBVUE7d0VBQUM2RSxXQUFVOzs7Ozs7a0ZBQ3RCLDhEQUFDbEUsbURBQU9BO3dFQUFDRSxJQUFHOzs7Ozs7Ozs7Ozs7MEVBRWhCLDhEQUFDNUMseURBQU1BO2dFQUFDNkcsU0FBUTtnRUFBVW1CLE1BQUs7Z0VBQUtJLG1CQUFnQjtnRUFBZUMsd0JBQXFCO2dFQUFLZixTQUFTLElBQU1aLGVBQWVUOztrRkFDdkgsOERBQUNoRSw2S0FBSUE7d0VBQUMyRSxXQUFVOzs7Ozs7a0ZBQ2hCLDhEQUFDbEUsbURBQU9BO3dFQUFDRSxJQUFHOzs7Ozs7Ozs7Ozs7MEVBRWhCLDhEQUFDNUMseURBQU1BO2dFQUFDNkcsU0FBUTtnRUFBVW1CLE1BQUs7Z0VBQUtJLG1CQUFnQjtnRUFBZ0JDLHdCQUFxQjtnRUFBS2YsU0FBUyxJQUFNekIsdUJBQXVCSSxXQUFXckQsRUFBRTs7a0ZBQy9JLDhEQUFDViw2S0FBTUE7d0VBQUMwRSxXQUFVOzs7Ozs7a0ZBQ2xCLDhEQUFDbEUsbURBQU9BO3dFQUFDRSxJQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBckNMcUQsV0FBV3JELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFnRHRDLDhEQUFDbkMseURBQU1BO2dCQUFDMEcsTUFBTTlDO2dCQUFrQitDLGNBQWM5QzswQkFDNUMsNEVBQUM1RCxnRUFBYUE7b0JBQUNrRyxXQUFVOztzQ0FDdkIsOERBQUNoRywrREFBWUE7OzhDQUNYLDhEQUFDQyw4REFBV0E7OENBQUM7Ozs7Ozs4Q0FDYiw4REFBQ0Ysb0VBQWlCQTs4Q0FBQzs7Ozs7Ozs7Ozs7O3NDQUdyQiw4REFBQ2dCLHNEQUFJQTs0QkFBQzRGLGNBQWE7NEJBQVFYLFdBQVU7OzhDQUNuQyw4REFBQy9FLDBEQUFRQTtvQ0FBQytFLFdBQVU7O3NEQUNsQiw4REFBQzlFLDZEQUFXQTs0Q0FBQzhCLE9BQU07c0RBQVE7Ozs7OztzREFDM0IsOERBQUM5Qiw2REFBV0E7NENBQUM4QixPQUFNO3NEQUFhOzs7Ozs7Ozs7Ozs7OENBR2xDLDhEQUFDaEMsNkRBQVdBO29DQUFDZ0MsT0FBTTtvQ0FBUWdELFdBQVU7O3NEQUNuQyw4REFBQ0k7NENBQUlKLFdBQVU7OzhEQUNiLDhEQUFDMUcsdURBQUtBO29EQUFDc0gsU0FBUTs4REFBTzs7Ozs7OzhEQUN0Qiw4REFBQ3ZILHVEQUFLQTtvREFDSjJDLElBQUc7b0RBQ0hnQixPQUFPbUIsU0FBU2xDLElBQUk7b0RBQ3BCNEUsVUFBVSxDQUFDQyxJQUFNMUMsWUFBWTs0REFBRSxHQUFHRCxRQUFROzREQUFFbEMsTUFBTTZFLEVBQUVDLE1BQU0sQ0FBQy9ELEtBQUs7d0RBQUM7b0RBQ2pFZ0UsYUFBWTs7Ozs7Ozs7Ozs7O3NEQUloQiw4REFBQ1o7NENBQUlKLFdBQVU7OzhEQUNiLDhEQUFDMUcsdURBQUtBO29EQUFDc0gsU0FBUTs4REFBTzs7Ozs7OzhEQUN0Qiw4REFBQ25HLHlEQUFNQTtvREFDTHVDLE9BQU9tQixTQUFTakMsSUFBSTtvREFDcEIrRSxlQUFlLENBQUNqRTt3REFDZG9CLFlBQVk7NERBQ1YsR0FBR0QsUUFBUTs0REFDWGpDLE1BQU1jOzREQUNOWixNQUFNK0QsZUFBZW5EO3dEQUN2QjtvREFDRjs7c0VBRUEsOERBQUNwQyxnRUFBYUE7c0VBQ1osNEVBQUNDLDhEQUFXQTs7Ozs7Ozs7OztzRUFFZCw4REFBQ0gsZ0VBQWFBO3NFQUNYcUMsY0FBY2dDLEdBQUcsQ0FBQyxDQUFDN0MscUJBQ2xCLDhEQUFDdkIsNkRBQVVBO29FQUFrQnFDLE9BQU9kLEtBQUtjLEtBQUs7OEVBQzNDZCxLQUFLZSxLQUFLO21FQURJZixLQUFLYyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQVFuQyw4REFBQ29EOzRDQUFJSixXQUFVOzs4REFDYiw4REFBQzFHLHVEQUFLQTtvREFBQ3NILFNBQVE7OERBQWM7Ozs7Ozs4REFDN0IsOERBQUM5Riw4REFBUUE7b0RBQ1BrQixJQUFHO29EQUNIZ0IsT0FBT21CLFNBQVMxQixXQUFXO29EQUMzQm9FLFVBQVUsQ0FBQ0MsSUFBTTFDLFlBQVk7NERBQUUsR0FBR0QsUUFBUTs0REFBRTFCLGFBQWFxRSxFQUFFQyxNQUFNLENBQUMvRCxLQUFLO3dEQUFDO29EQUN4RWdFLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLbEIsOERBQUNoRyw2REFBV0E7b0NBQUNnQyxPQUFNO29DQUFhZ0QsV0FBVTs7c0RBQ3hDLDhEQUFDSTs0Q0FBSUosV0FBVTs7OERBQ2IsOERBQUNJO29EQUFJSixXQUFVOztzRUFDYiw4REFBQzFHLHVEQUFLQTs0REFBQ3NILFNBQVE7c0VBQU87Ozs7OztzRUFDdEIsOERBQUN2SCx1REFBS0E7NERBQ0oyQyxJQUFHOzREQUNIZ0IsT0FBT21CLFNBQVNoQyxJQUFJOzREQUNwQjBFLFVBQVUsQ0FBQ0MsSUFBTTFDLFlBQVk7b0VBQUUsR0FBR0QsUUFBUTtvRUFBRWhDLE1BQU0yRSxFQUFFQyxNQUFNLENBQUMvRCxLQUFLO2dFQUFDOzREQUNqRWdFLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFHaEIsOERBQUNaO29EQUFJSixXQUFVOztzRUFDYiw4REFBQzFHLHVEQUFLQTs0REFBQ3NILFNBQVE7c0VBQU87Ozs7OztzRUFDdEIsOERBQUN2SCx1REFBS0E7NERBQ0oyQyxJQUFHOzREQUNIRSxNQUFLOzREQUNMYyxPQUFPbUIsU0FBUy9CLElBQUk7NERBQ3BCeUUsVUFBVSxDQUFDQyxJQUFNMUMsWUFBWTtvRUFBRSxHQUFHRCxRQUFRO29FQUFFL0IsTUFBTThFLE9BQU9DLFFBQVEsQ0FBQ0wsRUFBRUMsTUFBTSxDQUFDL0QsS0FBSztnRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUt4Riw4REFBQ29EOzRDQUFJSixXQUFVOzs4REFDYiw4REFBQzFHLHVEQUFLQTtvREFBQ3NILFNBQVE7OERBQVc7Ozs7Ozs4REFDMUIsOERBQUN2SCx1REFBS0E7b0RBQ0oyQyxJQUFHO29EQUNIZ0IsT0FBT21CLFNBQVM5QixRQUFRO29EQUN4QndFLFVBQVUsQ0FBQ0MsSUFBTTFDLFlBQVk7NERBQUUsR0FBR0QsUUFBUTs0REFBRTlCLFVBQVV5RSxFQUFFQyxNQUFNLENBQUMvRCxLQUFLO3dEQUFDO29EQUNyRWdFLGFBQVk7Ozs7Ozs7Ozs7OztzREFJaEIsOERBQUNaOzRDQUFJSixXQUFVOzs4REFDYiw4REFBQ0k7b0RBQUlKLFdBQVU7O3NFQUNiLDhEQUFDMUcsdURBQUtBOzREQUFDc0gsU0FBUTtzRUFBVzs7Ozs7O3NFQUMxQiw4REFBQ3ZILHVEQUFLQTs0REFDSjJDLElBQUc7NERBQ0hnQixPQUFPbUIsU0FBUzdCLFFBQVE7NERBQ3hCdUUsVUFBVSxDQUFDQyxJQUFNMUMsWUFBWTtvRUFBRSxHQUFHRCxRQUFRO29FQUFFN0IsVUFBVXdFLEVBQUVDLE1BQU0sQ0FBQy9ELEtBQUs7Z0VBQUM7NERBQ3JFZ0UsYUFBWTs7Ozs7Ozs7Ozs7OzhEQUdoQiw4REFBQ1o7b0RBQUlKLFdBQVU7O3NFQUNiLDhEQUFDMUcsdURBQUtBOzREQUFDc0gsU0FBUTtzRUFBVzs7Ozs7O3NFQUMxQiw4REFBQ1I7NERBQUlKLFdBQVU7OzhFQUNiLDhEQUFDM0csdURBQUtBO29FQUNKMkMsSUFBRztvRUFDSEUsTUFBTTJCLGVBQWUsU0FBUztvRUFDOUJiLE9BQU9tQixTQUFTNUIsUUFBUTtvRUFDeEJzRSxVQUFVLENBQUNDLElBQU0xQyxZQUFZOzRFQUFFLEdBQUdELFFBQVE7NEVBQUU1QixVQUFVdUUsRUFBRUMsTUFBTSxDQUFDL0QsS0FBSzt3RUFBQztvRUFDckVnRSxhQUFZOzs7Ozs7OEVBRWQsOERBQUM1SCx5REFBTUE7b0VBQ0w4QyxNQUFLO29FQUNMK0QsU0FBUTtvRUFDUm1CLE1BQUs7b0VBQ0xwQixXQUFVO29FQUNWVSxTQUFTLElBQU01QyxnQkFBZ0IsQ0FBQ0Q7OEVBRS9CQSw2QkFBZSw4REFBQ2hDLDZLQUFNQTt3RUFBQ21FLFdBQVU7Ozs7OzZGQUFlLDhEQUFDcEUsNktBQUdBO3dFQUFDb0UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBUTNFakMsNEJBQ0MsOERBQUNxQzs0QkFDQ0osV0FBVyxpQ0FBOEksT0FBN0dqQyxXQUFXMEIsT0FBTyxHQUFHLGdEQUFnRDtzQ0FFakgsNEVBQUNXO2dDQUFJSixXQUFVOztvQ0FDWmpDLFdBQVcwQixPQUFPLGlCQUFHLDhEQUFDaEUsNktBQVdBO3dDQUFDdUUsV0FBVTs7Ozs7NkRBQWUsOERBQUNyRSw2S0FBV0E7d0NBQUNxRSxXQUFVOzs7Ozs7a0RBQ25GLDhEQUFDcUI7a0RBQU10RCxXQUFXNkIsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSy9CLDhEQUFDUTs0QkFBSUosV0FBVTs7OENBQ2IsOERBQUM1Ryx5REFBTUE7b0NBQ0w4QyxNQUFLO29DQUNMK0QsU0FBUTtvQ0FDUlMsU0FBUyxJQUFNdEI7b0NBQ2ZrQyxVQUFVckQsYUFBYSxDQUFDRSxTQUFTaEMsSUFBSSxJQUFJLENBQUNnQyxTQUFTOUIsUUFBUTs7c0RBRTNELDhEQUFDZCw2S0FBUUE7NENBQUN5RSxXQUFVOzs7Ozs7d0NBQ25CL0IsWUFBWSxXQUFXOzs7Ozs7OzhDQUUxQiw4REFBQzdFLHlEQUFNQTtvQ0FBQzZHLFNBQVE7b0NBQVVTLFNBQVMsSUFBTWhELG9CQUFvQjs4Q0FBUTs7Ozs7OzhDQUdyRSw4REFBQ3RFLHlEQUFNQTtvQ0FDTHNILFNBQVM3QjtvQ0FDVHlDLFVBQ0UsQ0FBQ25ELFNBQVNsQyxJQUFJLENBQUNzRixJQUFJLE1BQ25CLENBQUNwRCxTQUFTaEMsSUFBSSxDQUFDb0YsSUFBSSxNQUNuQixDQUFDcEQsU0FBUzlCLFFBQVEsQ0FBQ2tGLElBQUksTUFDdkIsQ0FBQ3BELFNBQVM3QixRQUFRLENBQUNpRixJQUFJLE1BQ3ZCLENBQUNwRCxTQUFTNUIsUUFBUSxDQUFDZ0YsSUFBSTs4Q0FFMUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7R0E1a0J3QnJFO0tBQUFBIiwic291cmNlcyI6WyJEOlxcd29ya3NwYWNlXFxkc3RwXFxwcm9qZWN0XFxubDJzcWxcXGludGVyZmFjZVxcY29tcG9uZW50c1xcZGF0YS1zb3VyY2UtY29uZmlnLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tIFwiQC9jb250ZXh0cy9hdXRoLWNvbnRleHRcIlxuaW1wb3J0IGFwaUNsaWVudCBmcm9tIFwiQC9saWIvYXBpLWNsaWVudFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2xhYmVsXCJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHtcbiAgRGlhbG9nLFxuICBEaWFsb2dDb250ZW50LFxuICBEaWFsb2dEZXNjcmlwdGlvbixcbiAgRGlhbG9nSGVhZGVyLFxuICBEaWFsb2dUaXRsZSxcbiAgRGlhbG9nVHJpZ2dlcixcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9kaWFsb2dcIlxuaW1wb3J0IHsgVGFibGUsIFRhYmxlQm9keSwgVGFibGVDZWxsLCBUYWJsZUhlYWQsIFRhYmxlSGVhZGVyLCBUYWJsZVJvdyB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGFibGVcIlxuaW1wb3J0IHsgU2VsZWN0LCBTZWxlY3RDb250ZW50LCBTZWxlY3RJdGVtLCBTZWxlY3RUcmlnZ2VyLCBTZWxlY3RWYWx1ZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc2VsZWN0XCJcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiXG5pbXBvcnQgeyBUYWJzLCBUYWJzQ29udGVudCwgVGFic0xpc3QsIFRhYnNUcmlnZ2VyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90YWJzXCJcbmltcG9ydCB7IEZvbGRlclN5bmMsIFBsdXMsIEVkaXQsIFRyYXNoMiwgVGVzdFR1YmUsIFRlc3RUdWJlRGlhZ29uYWwsIENoZWNrQ2lyY2xlLCBYQ2lyY2xlLCBBbGVydENpcmNsZSwgRXllLCBFeWVPZmYgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcbmltcG9ydCB7IFRvb2x0aXAgfSBmcm9tICdyZWFjdC10b29sdGlwJ1xuXG5pbnRlcmZhY2UgRGF0YVNvdXJjZSB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIHR5cGU6IFwiZG9yaXNcIiB8IFwibXlzcWxcIiB8IFwicG9zdGdyZXNxbFwiIHwgXCJvcmFjbGVcIiB8IFwic3Fsc2VydmVyXCIgfCBcInNxbGl0ZVwiXG4gIGhvc3Q6IHN0cmluZ1xuICBwb3J0OiBudW1iZXJcbiAgZGF0YWJhc2U6IHN0cmluZ1xuICB1c2VybmFtZTogc3RyaW5nXG4gIHBhc3N3b3JkOiBzdHJpbmdcbiAgc3RhdHVzOiBcImNvbm5lY3RlZFwiIHwgXCJkaXNjb25uZWN0ZWRcIiB8IFwiZXJyb3JcIlxuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xuICBjcm9uSm9iPzogc3RyaW5nXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIHVwZGF0ZWRBdDogc3RyaW5nXG4gIGxhc3RUZXN0ZWQ/OiBzdHJpbmdcbiAgbGFzdFN5bmM/OiBzdHJpbmdcbn1cblxuY29uc3QgbW9ja0RhdGFTb3VyY2VzOiBEYXRhU291cmNlW10gPSBbXG4gIHtcbiAgICBpZDogXCIxXCIsXG4gICAgbmFtZTogXCLnlJ/kuqfmlbDmja7lupNcIixcbiAgICB0eXBlOiBcIm15c3FsXCIsXG4gICAgaG9zdDogXCJwcm9kLWRiLmV4YW1wbGUuY29tXCIsXG4gICAgcG9ydDogMzMwNixcbiAgICBkYXRhYmFzZTogXCJwcm9kdWN0aW9uXCIsXG4gICAgdXNlcm5hbWU6IFwiYXBwX3VzZXJcIixcbiAgICBwYXNzd29yZDogXCJlbmNyeXB0ZWRfcGFzc3dvcmRcIixcbiAgICBzdGF0dXM6IFwiY29ubmVjdGVkXCIsXG4gICAgZGVzY3JpcHRpb246IFwi55Sf5Lqn546v5aKD5Li75pWw5o2u5bqTXCIsXG4gICAgY3JvbkpvYjogXCIwIDAgKiAqICpcIixcbiAgICBjcmVhdGVkQXQ6IFwiMjAyNC0wMS0wMVwiLFxuICAgIHVwZGF0ZWRBdDogXCIyMDI0LTAxLTE1XCIsXG4gICAgbGFzdFRlc3RlZDogXCIyMDI0LTAxLTE1IDEwOjMwOjAwXCIsXG4gICAgbGFzdFN5bmM6IFwiMjAyNC0wMS0xNSAxMDozMDowMFwiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiMlwiLFxuICAgIG5hbWU6IFwi5rWL6K+V5pWw5o2u5bqTXCIsXG4gICAgdHlwZTogXCJwb3N0Z3Jlc3FsXCIsXG4gICAgaG9zdDogXCJ0ZXN0LWRiLmV4YW1wbGUuY29tXCIsXG4gICAgcG9ydDogNTQzMixcbiAgICBkYXRhYmFzZTogXCJ0ZXN0ZGJcIixcbiAgICB1c2VybmFtZTogXCJ0ZXN0X3VzZXJcIixcbiAgICBwYXNzd29yZDogXCJlbmNyeXB0ZWRfcGFzc3dvcmRcIixcbiAgICBzdGF0dXM6IFwiY29ubmVjdGVkXCIsXG4gICAgZGVzY3JpcHRpb246IFwi5rWL6K+V546v5aKD5pWw5o2u5bqTXCIsXG4gICAgY3JvbkpvYjogXCIwIDAgKiAqICpcIixcbiAgICBjcmVhdGVkQXQ6IFwiMjAyNC0wMS0wMlwiLFxuICAgIHVwZGF0ZWRBdDogXCIyMDI0LTAxLTEwXCIsXG4gICAgbGFzdFRlc3RlZDogXCIyMDI0LTAxLTE0IDE1OjIwOjAwXCIsXG4gICAgbGFzdFN5bmM6IFwiMjAyNC0wMS0xNCAxNToyMDowMFwiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiM1wiLFxuICAgIG5hbWU6IFwi5pWw5o2u5LuT5bqTXCIsXG4gICAgdHlwZTogXCJkb3Jpc1wiLFxuICAgIGhvc3Q6IFwiZHdzLmV4YW1wbGUuY29tXCIsXG4gICAgcG9ydDogOTAzMCxcbiAgICBkYXRhYmFzZTogXCJEV1BST0RcIixcbiAgICB1c2VybmFtZTogXCJkd191c2VyXCIsXG4gICAgcGFzc3dvcmQ6IFwiZW5jcnlwdGVkX3Bhc3N3b3JkXCIsXG4gICAgc3RhdHVzOiBcImVycm9yXCIsXG4gICAgZGVzY3JpcHRpb246IFwi5pWw5o2u5LuT5bqTRG9yaXNcIixcbiAgICBjcm9uSm9iOiBcIjAgMCAqICogKlwiLFxuICAgIGNyZWF0ZWRBdDogXCIyMDI0LTAxLTAzXCIsXG4gICAgdXBkYXRlZEF0OiBcIjIwMjQtMDEtMTJcIixcbiAgICBsYXN0VGVzdGVkOiBcIjIwMjQtMDEtMTIgMDk6NDU6MDBcIixcbiAgICBsYXN0U3luYzogXCIyMDI0LTAxLTE0IDE1OjIwOjAwXCIsXG4gIH0sXG5dXG5cbmNvbnN0IGRhdGFiYXNlVHlwZXMgPSBbXG4gIHsgdmFsdWU6IFwiZG9yaXNcIiwgbGFiZWw6IFwiRG9yaXNcIiB9LFxuICB7IHZhbHVlOiBcIm15c3FsXCIsIGxhYmVsOiBcIk15U1FMXCIgfSxcbiAgeyB2YWx1ZTogXCJwb3N0Z3Jlc3FsXCIsIGxhYmVsOiBcIlBvc3RncmVTUUxcIiB9LFxuICB7IHZhbHVlOiBcIm9yYWNsZVwiLCBsYWJlbDogXCJPcmFjbGVcIiB9LFxuICB7IHZhbHVlOiBcInNxbHNlcnZlclwiLCBsYWJlbDogXCJTUUwgU2VydmVyXCIgfSxcbiAgeyB2YWx1ZTogXCJzcWxpdGVcIiwgbGFiZWw6IFwiU1FMaXRlXCIgfSxcbl1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGF0YVNvdXJjZUNvbmZpZygpIHtcbiAgY29uc3QgW2RhdGFTb3VyY2VzLCBzZXREYXRhU291cmNlc10gPSB1c2VTdGF0ZTxEYXRhU291cmNlW10+KFtdKVxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2lzQ3JlYXRlRGlhbG9nT3Blbiwgc2V0SXNDcmVhdGVEaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaXNFZGl0RGlhbG9nT3Blbiwgc2V0SXNFZGl0RGlhbG9nT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3NlbGVjdGVkRGF0YVNvdXJjZSwgc2V0U2VsZWN0ZWREYXRhU291cmNlXSA9IHVzZVN0YXRlPERhdGFTb3VyY2UgfCBudWxsPihudWxsKVxuICBjb25zdCBbc2hvd1Bhc3N3b3JkLCBzZXRTaG93UGFzc3dvcmRdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFt0ZXN0UmVzdWx0LCBzZXRUZXN0UmVzdWx0XSA9IHVzZVN0YXRlPHsgc3VjY2VzczogYm9vbGVhbjsgbWVzc2FnZTogc3RyaW5nIH0gfCBudWxsPihudWxsKVxuICBjb25zdCBbaXNUZXN0aW5nLCBzZXRJc1Rlc3RpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGUoe1xuICAgIG5hbWU6IFwiXCIsXG4gICAgdHlwZTogXCJteXNxbFwiIGFzIERhdGFTb3VyY2VbXCJ0eXBlXCJdLFxuICAgIGhvc3Q6IFwiXCIsXG4gICAgcG9ydDogMzMwNixcbiAgICBkYXRhYmFzZTogXCJcIixcbiAgICB1c2VybmFtZTogXCJcIixcbiAgICBwYXNzd29yZDogXCJcIixcbiAgICBkZXNjcmlwdGlvbjogXCJcIixcbiAgfSlcblxuICBjb25zdCBoYW5kbGVDcmVhdGVEYXRhU291cmNlID0gKCkgPT4ge1xuICAgIGNvbnN0IG5ld0RhdGFTb3VyY2U6IERhdGFTb3VyY2UgPSB7XG4gICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxuICAgICAgLi4uZm9ybURhdGEsXG4gICAgICBzdGF0dXM6IFwiZGlzY29ubmVjdGVkXCIsXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdChcIlRcIilbMF0sXG4gICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdChcIlRcIilbMF0sXG4gICAgfVxuXG4gICAgc2V0RGF0YVNvdXJjZXMoWy4uLmRhdGFTb3VyY2VzLCBuZXdEYXRhU291cmNlXSlcbiAgICBzZXRJc0NyZWF0ZURpYWxvZ09wZW4oZmFsc2UpXG4gICAgcmVzZXRGb3JtKClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUVkaXREYXRhU291cmNlID0gKCkgPT4ge1xuICAgIGlmICghc2VsZWN0ZWREYXRhU291cmNlKSByZXR1cm5cblxuICAgIGNvbnN0IHVwZGF0ZWREYXRhU291cmNlcyA9IGRhdGFTb3VyY2VzLm1hcCgoZHMpID0+XG4gICAgICBkcy5pZCA9PT0gc2VsZWN0ZWREYXRhU291cmNlLmlkXG4gICAgICAgID8ge1xuICAgICAgICAgICAgLi4uZHMsXG4gICAgICAgICAgICAuLi5mb3JtRGF0YSxcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KFwiVFwiKVswXSxcbiAgICAgICAgICB9XG4gICAgICAgIDogZHMsXG4gICAgKVxuXG4gICAgc2V0RGF0YVNvdXJjZXModXBkYXRlZERhdGFTb3VyY2VzKVxuICAgIHNldElzRWRpdERpYWxvZ09wZW4oZmFsc2UpXG4gICAgcmVzZXRGb3JtKClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZURhdGFTb3VyY2UgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIGlmIChjb25maXJtKFwi56Gu5a6a6KaB5Yig6Zmk6L+Z5Liq5pWw5o2u5rqQ5ZCX77yfXCIpKSB7XG4gICAgICBzZXREYXRhU291cmNlcyhkYXRhU291cmNlcy5maWx0ZXIoKGRzKSA9PiBkcy5pZCAhPT0gaWQpKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVRlc3RDb25uZWN0aW9uID0gYXN5bmMgKGRhdGFTb3VyY2U/OiBEYXRhU291cmNlKSA9PiB7XG4gICAgc2V0SXNUZXN0aW5nKHRydWUpXG4gICAgc2V0VGVzdFJlc3VsdChudWxsKVxuXG4gICAgLy8gU2ltdWxhdGUgY29ubmVjdGlvbiB0ZXN0XG4gICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMjAwMCkpXG5cbiAgICBjb25zdCBzdWNjZXNzID0gTWF0aC5yYW5kb20oKSA+IDAuMyAvLyA3MCUgc3VjY2VzcyByYXRlIGZvciBkZW1vXG4gICAgc2V0VGVzdFJlc3VsdCh7XG4gICAgICBzdWNjZXNzLFxuICAgICAgbWVzc2FnZTogc3VjY2VzcyA/IFwi6L+e5o6l5rWL6K+V5oiQ5Yqf77yB5pWw5o2u5bqT6L+e5o6l5q2j5bi444CCXCIgOiBcIui/nuaOpea1i+ivleWksei0pe+8muaXoOazlei/nuaOpeWIsOaVsOaNruW6k++8jOivt+ajgOafpemFjee9ruS/oeaBr+OAglwiLFxuICAgIH0pXG5cbiAgICBpZiAoZGF0YVNvdXJjZSAmJiBzdWNjZXNzKSB7XG4gICAgICAvLyBVcGRhdGUgdGhlIGRhdGEgc291cmNlIHN0YXR1c1xuICAgICAgY29uc3QgdXBkYXRlZERhdGFTb3VyY2VzID0gZGF0YVNvdXJjZXMubWFwKChkcykgPT5cbiAgICAgICAgZHMuaWQgPT09IGRhdGFTb3VyY2UuaWRcbiAgICAgICAgICA/IHtcbiAgICAgICAgICAgICAgLi4uZHMsXG4gICAgICAgICAgICAgIHN0YXR1czogXCJjb25uZWN0ZWRcIiBhcyBjb25zdCxcbiAgICAgICAgICAgICAgbGFzdFRlc3RlZDogbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygpLFxuICAgICAgICAgICAgfVxuICAgICAgICAgIDogZHMsXG4gICAgICApXG4gICAgICBzZXREYXRhU291cmNlcyh1cGRhdGVkRGF0YVNvdXJjZXMpXG4gICAgfVxuXG4gICAgc2V0SXNUZXN0aW5nKGZhbHNlKVxuICB9XG5cbiAgY29uc3Qgb3BlbkVkaXREaWFsb2cgPSAoZGF0YVNvdXJjZTogRGF0YVNvdXJjZSkgPT4ge1xuICAgIHNldFNlbGVjdGVkRGF0YVNvdXJjZShkYXRhU291cmNlKVxuICAgIHNldEZvcm1EYXRhKHtcbiAgICAgIG5hbWU6IGRhdGFTb3VyY2UubmFtZSxcbiAgICAgIHR5cGU6IGRhdGFTb3VyY2UudHlwZSxcbiAgICAgIGhvc3Q6IGRhdGFTb3VyY2UuaG9zdCxcbiAgICAgIHBvcnQ6IGRhdGFTb3VyY2UucG9ydCxcbiAgICAgIGRhdGFiYXNlOiBkYXRhU291cmNlLmRhdGFiYXNlLFxuICAgICAgdXNlcm5hbWU6IGRhdGFTb3VyY2UudXNlcm5hbWUsXG4gICAgICBwYXNzd29yZDogZGF0YVNvdXJjZS5wYXNzd29yZCxcbiAgICAgIGRlc2NyaXB0aW9uOiBkYXRhU291cmNlLmRlc2NyaXB0aW9uIHx8IFwiXCIsXG4gICAgfSlcbiAgICBzZXRJc0VkaXREaWFsb2dPcGVuKHRydWUpXG4gIH1cblxuICBjb25zdCByZXNldEZvcm0gPSAoKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEoe1xuICAgICAgbmFtZTogXCJcIixcbiAgICAgIHR5cGU6IFwibXlzcWxcIixcbiAgICAgIGhvc3Q6IFwiXCIsXG4gICAgICBwb3J0OiAzMzA2LFxuICAgICAgZGF0YWJhc2U6IFwiXCIsXG4gICAgICB1c2VybmFtZTogXCJcIixcbiAgICAgIHBhc3N3b3JkOiBcIlwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXCIsXG4gICAgfSlcbiAgICBzZXRTZWxlY3RlZERhdGFTb3VyY2UobnVsbClcbiAgICBzZXRUZXN0UmVzdWx0KG51bGwpXG4gICAgc2V0U2hvd1Bhc3N3b3JkKGZhbHNlKVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzQmFkZ2UgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSBcImNvbm5lY3RlZFwiOlxuICAgICAgICByZXR1cm4gPEJhZGdlIGNsYXNzTmFtZT1cImJnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMFwiPuW3sui/nuaOpTwvQmFkZ2U+XG4gICAgICBjYXNlIFwiZGlzY29ubmVjdGVkXCI6XG4gICAgICAgIHJldHVybiA8QmFkZ2UgdmFyaWFudD1cInNlY29uZGFyeVwiPuacqui/nuaOpTwvQmFkZ2U+XG4gICAgICBjYXNlIFwiZXJyb3JcIjpcbiAgICAgICAgcmV0dXJuIDxCYWRnZSB2YXJpYW50PVwiZGVzdHJ1Y3RpdmVcIj7ov57mjqXplJnor688L0JhZGdlPlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiPuacquefpTwvQmFkZ2U+XG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzSWNvbiA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlIFwiY29ubmVjdGVkXCI6XG4gICAgICAgIHJldHVybiA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICBjYXNlIFwiZGlzY29ubmVjdGVkXCI6XG4gICAgICAgIHJldHVybiA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXllbGxvdy02MDBcIiAvPlxuICAgICAgY2FzZSBcImVycm9yXCI6XG4gICAgICAgIHJldHVybiA8WENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtcmVkLTYwMFwiIC8+XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gbnVsbFxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldERlZmF1bHRQb3J0ID0gKHR5cGU6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSBcImRvcmlzXCI6XG4gICAgICAgIHJldHVybiA5MDMwXG4gICAgICBjYXNlIFwibXlzcWxcIjpcbiAgICAgICAgcmV0dXJuIDMzMDZcbiAgICAgIGNhc2UgXCJwb3N0Z3Jlc3FsXCI6XG4gICAgICAgIHJldHVybiA1NDMyXG4gICAgICBjYXNlIFwib3JhY2xlXCI6XG4gICAgICAgIHJldHVybiAxNTIxXG4gICAgICBjYXNlIFwic3Fsc2VydmVyXCI6XG4gICAgICAgIHJldHVybiAxNDMzXG4gICAgICBjYXNlIFwic3FsaXRlXCI6XG4gICAgICAgIHJldHVybiAwXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gMzMwNlxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZFwiPuaVsOaNrua6kOmFjee9rjwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPueuoeeQhuaVsOaNruW6k+i/nuaOpeWSjOmFjee9ruS/oeaBrzwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxEaWFsb2cgb3Blbj17aXNDcmVhdGVEaWFsb2dPcGVufSBvbk9wZW5DaGFuZ2U9e3NldElzQ3JlYXRlRGlhbG9nT3Blbn0+XG4gICAgICAgICAgPERpYWxvZ1RyaWdnZXIgYXNDaGlsZD5cbiAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17KCkgPT4gcmVzZXRGb3JtKCl9PlxuICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICDmlrDlu7rmlbDmja7mupBcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvRGlhbG9nVHJpZ2dlcj5cbiAgICAgICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJtYXgtdy0yeGxcIj5cbiAgICAgICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICAgIDxEaWFsb2dUaXRsZT7liJvlu7rmlbDmja7mupA8L0RpYWxvZ1RpdGxlPlxuICAgICAgICAgICAgICA8RGlhbG9nRGVzY3JpcHRpb24+6YWN572u5paw55qE5pWw5o2u5bqT6L+e5o6l5L+h5oGvPC9EaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuXG4gICAgICAgICAgICA8VGFicyBkZWZhdWx0VmFsdWU9XCJiYXNpY1wiIGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8VGFic0xpc3QgY2xhc3NOYW1lPVwiZ3JpZCB3LWZ1bGwgZ3JpZC1jb2xzLTJcIj5cbiAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJiYXNpY1wiPuWfuuacrOS/oeaBrzwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiY29ubmVjdGlvblwiPui/nuaOpemFjee9rjwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICAgIDwvVGFic0xpc3Q+XG5cbiAgICAgICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiYmFzaWNcIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJuYW1lXCI+5pWw5o2u5rqQ5ZCN56ewPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBpZD1cIm5hbWVcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXmlbDmja7mupDlkI3np7BcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInR5cGVcIj7mlbDmja7lupPnsbvlnos8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudHlwZX1cbiAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlOiBEYXRhU291cmNlW1widHlwZVwiXSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmZvcm1EYXRhLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogdmFsdWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBwb3J0OiBnZXREZWZhdWx0UG9ydCh2YWx1ZSksXG4gICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAge2RhdGFiYXNlVHlwZXMubWFwKCh0eXBlKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSBrZXk9e3R5cGUudmFsdWV9IHZhbHVlPXt0eXBlLnZhbHVlfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3R5cGUubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZGVzY3JpcHRpb25cIj7mj4/ov7A8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgIGlkPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIui+k+WFpeaVsOaNrua6kOaPj+i/sO+8iOWPr+mAie+8iVwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cImNvbm5lY3Rpb25cIiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiaG9zdFwiPuS4u+acuuWcsOWdgDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmhvc3R9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBob3N0OiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImxvY2FsaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicG9ydFwiPuerr+WPozwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwicG9ydFwiXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBvcnR9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBwb3J0OiBOdW1iZXIucGFyc2VJbnQoZS50YXJnZXQudmFsdWUpIH0pfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJkYXRhYmFzZVwiPuaVsOaNruW6k+WQjTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgaWQ9XCJkYXRhYmFzZVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kYXRhYmFzZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBkYXRhYmFzZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl5pWw5o2u5bqT5ZCN56ewXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidXNlcm5hbWVcIj7nlKjmiLflkI08L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cInVzZXJuYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudXNlcm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCB1c2VybmFtZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXnlKjmiLflkI1cIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInBhc3N3b3JkXCI+5a+G56CBPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPXtzaG93UGFzc3dvcmQgPyBcInRleHRcIiA6IFwicGFzc3dvcmRcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wYXNzd29yZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgcGFzc3dvcmQ6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXlr4bnoIFcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTAgdG9wLTAgaC1mdWxsIHB4LTMgcHktMiBob3ZlcjpiZy10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93UGFzc3dvcmQoIXNob3dQYXNzd29yZCl9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge3Nob3dQYXNzd29yZCA/IDxFeWVPZmYgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEV5ZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz59XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICAgICAgICA8L1RhYnM+XG5cbiAgICAgICAgICAgIHt0ZXN0UmVzdWx0ICYmIChcbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMyByb3VuZGVkLWxnIGJvcmRlciB0ZXh0LXNtICR7dGVzdFJlc3VsdC5zdWNjZXNzID8gXCJiZy1ncmVlbi01MCBib3JkZXItZ3JlZW4tMjAwIHRleHQtZ3JlZW4tODAwXCIgOiBcImJnLXJlZC01MCBib3JkZXItcmVkLTIwMCB0ZXh0LXJlZC04MDBcIn1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAge3Rlc3RSZXN1bHQuc3VjY2VzcyA/IDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz4gOiA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICAgICAgPHNwYW4+e3Rlc3RSZXN1bHQubWVzc2FnZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtMiBtdC00XCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiBzZXRJc0NyZWF0ZURpYWxvZ09wZW4oZmFsc2UpfT5cbiAgICAgICAgICAgICAgICDlj5bmtohcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAvLyB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAvLyB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVGVzdENvbm5lY3Rpb24oKX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17XG4gICAgICAgICAgICAgICAgICBpc1Rlc3RpbmcgfHxcbiAgICAgICAgICAgICAgICAgICFmb3JtRGF0YS5uYW1lLnRyaW0oKSB8fFxuICAgICAgICAgICAgICAgICAgIWZvcm1EYXRhLmhvc3QudHJpbSgpIHx8XG4gICAgICAgICAgICAgICAgICAhZm9ybURhdGEuZGF0YWJhc2UudHJpbSgpIHx8XG4gICAgICAgICAgICAgICAgICAhZm9ybURhdGEudXNlcm5hbWUudHJpbSgpIHx8XG4gICAgICAgICAgICAgICAgICAhZm9ybURhdGEucGFzc3dvcmQudHJpbSgpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzVGVzdGluZyA/IFwi5rWL6K+V5LitLi4uXCIgOiBcIua1i+ivlei/nuaOpVwifVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNyZWF0ZURhdGFTb3VyY2V9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e1xuICAgICAgICAgICAgICAgICAgIWZvcm1EYXRhLm5hbWUudHJpbSgpIHx8XG4gICAgICAgICAgICAgICAgICAhZm9ybURhdGEuaG9zdC50cmltKCkgfHxcbiAgICAgICAgICAgICAgICAgICFmb3JtRGF0YS5kYXRhYmFzZS50cmltKCkgfHxcbiAgICAgICAgICAgICAgICAgICFmb3JtRGF0YS51c2VybmFtZS50cmltKCkgfHxcbiAgICAgICAgICAgICAgICAgICFmb3JtRGF0YS5wYXNzd29yZC50cmltKClcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDliJvlu7rmlbDmja7mupBcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG4gICAgICAgIDwvRGlhbG9nPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxDYXJkPlxuICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICDmlbDmja7mupDliJfooahcbiAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPuezu+e7n+S4reeahOaJgOacieaVsOaNrua6kOi/nuaOpTwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICA8VGFibGU+XG4gICAgICAgICAgICA8VGFibGVIZWFkZXI+XG4gICAgICAgICAgICAgIDxUYWJsZVJvdz5cbiAgICAgICAgICAgICAgICA8VGFibGVIZWFkPuaVsOaNrua6kDwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+57G75Z6LPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgPFRhYmxlSGVhZD7nirbmgIE8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICA8VGFibGVIZWFkPuWQjOatpeS9nOS4mkNyb248L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICA8VGFibGVIZWFkPuacgOi/keWQjOatpeaXtumXtDwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+5pON5L2cPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICA8L1RhYmxlSGVhZGVyPlxuICAgICAgICAgICAgPFRhYmxlQm9keT5cbiAgICAgICAgICAgICAge2RhdGFTb3VyY2VzLm1hcCgoZGF0YVNvdXJjZSkgPT4gKFxuICAgICAgICAgICAgICAgIDxUYWJsZVJvdyBrZXk9e2RhdGFTb3VyY2UuaWR9PlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntkYXRhU291cmNlLm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj57ZGF0YVNvdXJjZS5kZXNjcmlwdGlvbn08L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPntkYXRhU291cmNlLnR5cGV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2dldFN0YXR1c0ljb24oZGF0YVNvdXJjZS5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNCYWRnZShkYXRhU291cmNlLnN0YXR1cyl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj57ZGF0YVNvdXJjZS5jcm9uSm9ifTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e2RhdGFTb3VyY2UubGFzdFN5bmMgPyBuZXcgRGF0ZShkYXRhU291cmNlLmxhc3RTeW5jKS50b0xvY2FsZVN0cmluZygpIDogXCJOL0FcIn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgZGF0YS10b29sdGlwLWlkPVwidG9vbHRpcC10ZXN0XCIgZGF0YS10b29sdGlwLWNvbnRlbnQ9XCLmtYvor5Xov57mjqVcIiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUZXN0Q29ubmVjdGlvbihkYXRhU291cmNlKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXN0VHViZURpYWdvbmFsIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBpZD1cInRvb2x0aXAtdGVzdFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiIGRhdGEtdG9vbHRpcC1pZD1cInRvb2x0aXAtc3luY1wiIGRhdGEtdG9vbHRpcC1jb250ZW50PVwi5YWD5pWw5o2u5ZCM5q2lXCIgb25DbGljaz17KCkgPT4gaGFuZGxlVGVzdENvbm5lY3Rpb24oZGF0YVNvdXJjZSl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9sZGVyU3luYyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgaWQ9XCJ0b29sdGlwLXN5bmNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwic21cIiBkYXRhLXRvb2x0aXAtaWQ9XCJ0b29sdGlwLWVkaXRcIiBkYXRhLXRvb2x0aXAtY29udGVudD1cIue8lui+kVwiIG9uQ2xpY2s9eygpID0+IG9wZW5FZGl0RGlhbG9nKGRhdGFTb3VyY2UpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGlkPVwidG9vbHRpcC1lZGl0XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgZGF0YS10b29sdGlwLWlkPVwidG9vbHRpcC10cmFzaFwiIGRhdGEtdG9vbHRpcC1jb250ZW50PVwi5Yig6ZmkXCIgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlRGF0YVNvdXJjZShkYXRhU291cmNlLmlkKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgaWQ9XCJ0b29sdGlwLXRyYXNoXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvVGFibGVCb2R5PlxuICAgICAgICAgIDwvVGFibGU+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIDxEaWFsb2cgb3Blbj17aXNFZGl0RGlhbG9nT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRJc0VkaXREaWFsb2dPcGVufT5cbiAgICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwibWF4LXctMnhsXCI+XG4gICAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgIDxEaWFsb2dUaXRsZT7nvJbovpHmlbDmja7mupA8L0RpYWxvZ1RpdGxlPlxuICAgICAgICAgICAgPERpYWxvZ0Rlc2NyaXB0aW9uPuS/ruaUueaVsOaNruW6k+i/nuaOpeS/oeaBrzwvRGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgICA8VGFicyBkZWZhdWx0VmFsdWU9XCJiYXNpY1wiIGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy0yXCI+XG4gICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImJhc2ljXCI+5Z+65pys5L+h5oGvPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiY29ubmVjdGlvblwiPui/nuaOpemFjee9rjwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICA8L1RhYnNMaXN0PlxuXG4gICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJiYXNpY1wiIGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwibmFtZVwiPuaVsOaNrua6kOWQjeensDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBpZD1cIm5hbWVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLm5hbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIG5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXmlbDmja7mupDlkI3np7BcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ0eXBlXCI+5pWw5o2u5bqT57G75Z6LPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudHlwZX1cbiAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZTogRGF0YVNvdXJjZVtcInR5cGVcIl0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybURhdGEoe1xuICAgICAgICAgICAgICAgICAgICAgIC4uLmZvcm1EYXRhLFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU6IHZhbHVlLFxuICAgICAgICAgICAgICAgICAgICAgIHBvcnQ6IGdldERlZmF1bHRQb3J0KHZhbHVlKSxcbiAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSAvPlxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgIHtkYXRhYmFzZVR5cGVzLm1hcCgodHlwZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17dHlwZS52YWx1ZX0gdmFsdWU9e3R5cGUudmFsdWV9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3R5cGUubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImRlc2NyaXB0aW9uXCI+5o+P6L+wPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIGlkPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBkZXNjcmlwdGlvbjogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIui+k+WFpeaVsOaNrua6kOaPj+i/sO+8iOWPr+mAie+8iVwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJjb25uZWN0aW9uXCIgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImhvc3RcIj7kuLvmnLrlnLDlnYA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgIGlkPVwiaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5ob3N0fVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIGhvc3Q6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImxvY2FsaG9zdFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInBvcnRcIj7nq6/lj6M8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgIGlkPVwicG9ydFwiXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucG9ydH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBwb3J0OiBOdW1iZXIucGFyc2VJbnQoZS50YXJnZXQudmFsdWUpIH0pfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImRhdGFiYXNlXCI+5pWw5o2u5bqT5ZCNPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwiZGF0YWJhc2VcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRhdGFiYXNlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBkYXRhYmFzZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIui+k+WFpeaVsOaNruW6k+WQjeensFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidXNlcm5hbWVcIj7nlKjmiLflkI08L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgIGlkPVwidXNlcm5hbWVcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudXNlcm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgdXNlcm5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIui+k+WFpeeUqOaIt+WQjVwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInBhc3N3b3JkXCI+5a+G56CBPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgaWQ9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd1Bhc3N3b3JkID8gXCJ0ZXh0XCIgOiBcInBhc3N3b3JkXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBhc3N3b3JkfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgcGFzc3dvcmQ6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl5a+G56CBXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0wIHRvcC0wIGgtZnVsbCBweC0zIHB5LTIgaG92ZXI6YmctdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dQYXNzd29yZCghc2hvd1Bhc3N3b3JkKX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtzaG93UGFzc3dvcmQgPyA8RXllT2ZmIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPiA6IDxFeWUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICAgICAgPC9UYWJzPlxuXG4gICAgICAgICAge3Rlc3RSZXN1bHQgJiYgKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgcm91bmRlZC1sZyBib3JkZXIgdGV4dC1zbSAke3Rlc3RSZXN1bHQuc3VjY2VzcyA/IFwiYmctZ3JlZW4tNTAgYm9yZGVyLWdyZWVuLTIwMCB0ZXh0LWdyZWVuLTgwMFwiIDogXCJiZy1yZWQtNTAgYm9yZGVyLXJlZC0yMDAgdGV4dC1yZWQtODAwXCJ9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIHt0ZXN0UmVzdWx0LnN1Y2Nlc3MgPyA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPn1cbiAgICAgICAgICAgICAgICA8c3Bhbj57dGVzdFJlc3VsdC5tZXNzYWdlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtMiBtdC00XCI+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVRlc3RDb25uZWN0aW9uKCl9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc1Rlc3RpbmcgfHwgIWZvcm1EYXRhLmhvc3QgfHwgIWZvcm1EYXRhLmRhdGFiYXNlfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8VGVzdFR1YmUgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAge2lzVGVzdGluZyA/IFwi5rWL6K+V5LitLi4uXCIgOiBcIua1i+ivlei/nuaOpVwifVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gc2V0SXNFZGl0RGlhbG9nT3BlbihmYWxzZSl9PlxuICAgICAgICAgICAgICDlj5bmtohcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFZGl0RGF0YVNvdXJjZX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e1xuICAgICAgICAgICAgICAgICFmb3JtRGF0YS5uYW1lLnRyaW0oKSB8fFxuICAgICAgICAgICAgICAgICFmb3JtRGF0YS5ob3N0LnRyaW0oKSB8fFxuICAgICAgICAgICAgICAgICFmb3JtRGF0YS5kYXRhYmFzZS50cmltKCkgfHxcbiAgICAgICAgICAgICAgICAhZm9ybURhdGEudXNlcm5hbWUudHJpbSgpIHx8XG4gICAgICAgICAgICAgICAgIWZvcm1EYXRhLnBhc3N3b3JkLnRyaW0oKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOS/neWtmFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgIDwvRGlhbG9nPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJhZGdlIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0Rlc2NyaXB0aW9uIiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJEaWFsb2dUcmlnZ2VyIiwiVGFibGUiLCJUYWJsZUJvZHkiLCJUYWJsZUNlbGwiLCJUYWJsZUhlYWQiLCJUYWJsZUhlYWRlciIsIlRhYmxlUm93IiwiU2VsZWN0IiwiU2VsZWN0Q29udGVudCIsIlNlbGVjdEl0ZW0iLCJTZWxlY3RUcmlnZ2VyIiwiU2VsZWN0VmFsdWUiLCJUZXh0YXJlYSIsIlRhYnMiLCJUYWJzQ29udGVudCIsIlRhYnNMaXN0IiwiVGFic1RyaWdnZXIiLCJGb2xkZXJTeW5jIiwiUGx1cyIsIkVkaXQiLCJUcmFzaDIiLCJUZXN0VHViZSIsIlRlc3RUdWJlRGlhZ29uYWwiLCJDaGVja0NpcmNsZSIsIlhDaXJjbGUiLCJBbGVydENpcmNsZSIsIkV5ZSIsIkV5ZU9mZiIsIlRvb2x0aXAiLCJtb2NrRGF0YVNvdXJjZXMiLCJpZCIsIm5hbWUiLCJ0eXBlIiwiaG9zdCIsInBvcnQiLCJkYXRhYmFzZSIsInVzZXJuYW1lIiwicGFzc3dvcmQiLCJzdGF0dXMiLCJkZXNjcmlwdGlvbiIsImNyb25Kb2IiLCJjcmVhdGVkQXQiLCJ1cGRhdGVkQXQiLCJsYXN0VGVzdGVkIiwibGFzdFN5bmMiLCJkYXRhYmFzZVR5cGVzIiwidmFsdWUiLCJsYWJlbCIsIkRhdGFTb3VyY2VDb25maWciLCJkYXRhU291cmNlcyIsInNldERhdGFTb3VyY2VzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNDcmVhdGVEaWFsb2dPcGVuIiwic2V0SXNDcmVhdGVEaWFsb2dPcGVuIiwiaXNFZGl0RGlhbG9nT3BlbiIsInNldElzRWRpdERpYWxvZ09wZW4iLCJzZWxlY3RlZERhdGFTb3VyY2UiLCJzZXRTZWxlY3RlZERhdGFTb3VyY2UiLCJzaG93UGFzc3dvcmQiLCJzZXRTaG93UGFzc3dvcmQiLCJ0ZXN0UmVzdWx0Iiwic2V0VGVzdFJlc3VsdCIsImlzVGVzdGluZyIsInNldElzVGVzdGluZyIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJoYW5kbGVDcmVhdGVEYXRhU291cmNlIiwibmV3RGF0YVNvdXJjZSIsIkRhdGUiLCJub3ciLCJ0b1N0cmluZyIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJyZXNldEZvcm0iLCJoYW5kbGVFZGl0RGF0YVNvdXJjZSIsInVwZGF0ZWREYXRhU291cmNlcyIsIm1hcCIsImRzIiwiaGFuZGxlRGVsZXRlRGF0YVNvdXJjZSIsImNvbmZpcm0iLCJmaWx0ZXIiLCJoYW5kbGVUZXN0Q29ubmVjdGlvbiIsImRhdGFTb3VyY2UiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJzdWNjZXNzIiwiTWF0aCIsInJhbmRvbSIsIm1lc3NhZ2UiLCJ0b0xvY2FsZVN0cmluZyIsIm9wZW5FZGl0RGlhbG9nIiwiZ2V0U3RhdHVzQmFkZ2UiLCJjbGFzc05hbWUiLCJ2YXJpYW50IiwiZ2V0U3RhdHVzSWNvbiIsImdldERlZmF1bHRQb3J0IiwiZGl2IiwiaDIiLCJwIiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsImFzQ2hpbGQiLCJvbkNsaWNrIiwiZGVmYXVsdFZhbHVlIiwiaHRtbEZvciIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwib25WYWx1ZUNoYW5nZSIsIk51bWJlciIsInBhcnNlSW50Iiwic2l6ZSIsInNwYW4iLCJkaXNhYmxlZCIsInRyaW0iLCJkYXRhLXRvb2x0aXAtaWQiLCJkYXRhLXRvb2x0aXAtY29udGVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/data-source-config.tsx\n"));

/***/ })

});